{"name": "next-saas-boilerplate", "version": "0.1.0", "private": true, "scripts": {"clean": "rm -rf .next", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci", "ts:check": "pnpm exec tsc --noEmit", "format": "prettier --write --ignore-unknown --log-level error '**/*.{js,jsx,ts,tsx,json,md,mdx}'", "build-search-index": "node scripts/build-search-index.mjs", "docker:container": "docker run -p 3000:3000 nextjs-docker", "docker:build": "docker build -t nextjs-docker .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:start": "docker-compose -f docker-compose.dev.yml up --build", "docker:stop": "docker-compose -f docker-compose.dev.yml down", "prisma:seed": "npx prisma db seed", "prisma:migrate": "npx prisma migrate dev", "prisma:push": "npx prisma db push", "prisma:generate": "npx prisma generate", "prisma:reset": "npx prisma migrate reset", "prisma:studio": "npx prisma studio", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "postinstall": "npx prisma generate", "vercel-build": "prisma generate && prisma migrate deploy && next build", "prepare": "husky", "pretty-quick": "pretty-quick --staged"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@auth/prisma-adapter": "^2.5.3", "@aws-sdk/client-s3": "^3.664.0", "@aws-sdk/client-ses": "^3.664.0", "@aws-sdk/credential-providers": "^3.664.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@neondatabase/serverless": "^0.10.1", "@next/mdx": "^14.2.14", "@omit/react-fancy-switch": "^0.1.3", "@plaiceholder/next": "^3.0.0", "@prisma/adapter-neon": "^5.21.1", "@prisma/client": "^5.20.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@react-email/components": "^0.0.25", "@remixicon/react": "^4.6.0", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.59.15", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.13.5", "@tiptap/pm": "^2.8.0", "@tiptap/react": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@types/canvas-confetti": "^1.9.0", "@upstash/ratelimit": "^2.0.3", "@vercel/blob": "^0.24.1", "@vercel/kv": "^3.0.0", "add": "^2.0.6", "ai": "^3.4.9", "aws-sdk": "^2.1691.0", "bufferutil": "^4.0.8", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "flexsearch": "^0.7.43", "framer-motion": "^11.11.1", "gray-matter": "^4.0.3", "highlight.js": "^11.10.0", "jsdom": "^26.0.0", "lowlight": "^3.1.0", "lucide-react": "^0.453.0", "lunr": "^2.3.9", "mdast-util-toc": "^7.1.0", "nanoid": "^5.0.9", "next": "14.2.25", "next-auth": "^5.0.0-beta.22", "next-intl": "^3.21.1", "next-mdx-remote": "^5.0.0", "next-themes": "^0.3.0", "nodemailer": "^6.9.15", "novel": "^0.5.0", "npx": "^10.2.2", "nuqs": "^2.3.0", "openai": "^4.67.1", "plaiceholder": "^3.0.0", "postcss-import": "^16.1.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-image-crop": "^11.0.7", "react-markdown": "^9.0.3", "recharts": "^2.12.7", "rehype-pretty-code": "^0.14.0", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-mdx": "^3.0.1", "remark-mdx-to-plain-text": "^3.0.0", "resend": "^4.0.0", "select": "^1.1.2", "shadcn-ui": "^0.9.2", "sharp": "^0.33.5", "shiki": "^1.21.0", "sonner": "^1.7.0", "stripe": "^17.6.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "textarea": "^0.3.0", "tiptap-extension-auto-joiner": "^0.1.3", "tiptap-extension-global-drag-handle": "^0.1.13", "ts-pattern": "^5.4.0", "unist-util-visit": "^5.0.0", "use-debounce": "^10.0.3", "uuid": "^10.0.0", "ws": "^8.18.0", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.4.0", "@ngneat/falso": "^7.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/lunr": "^2.3.7", "@types/mdast": "^4.0.4", "@types/mdx": "^2.0.13", "@types/node": "^22.7.4", "@types/react": "^18", "@types/react-dom": "^18", "@types/unist": "^3.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.14", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-mdx": "^3.1.5", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-testing-library": "^6.3.0", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "postcss-nesting": "^13.0.0", "prettier": "^3.3.3", "pretty-quick": "^4.1.1", "prisma": "^5.20.0", "tailwindcss": "^3.4.13", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}