# Environment variables for Next.js SaaS Boilerplate


# ====== LOCAL DEVELOPMENT VARIABLES ======

# Application Name (for local development)
APP_NAME=next-sass-boilerplate

# PostgreSQL Configuration (for local development)
POSTGRES_USER=root
POSTGRES_PASSWORD=postgres
POSTGRES_DB="${APP_NAME}-db"
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=prefer

# ====== MANDATORY VARIABLES (Both Local and Production) ======

# Domain Configuration
# Main App Domain (include both local and production)
NEXT_PUBLIC_DOMAIN=http://localhost:3000

# Authentication Secret (IMPORTANT: Generate a new secret for production)
AUTH_SECRET=your-secret-key

# Database Configuration (you can leave this as is for local development)
DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public&sslmode=${POSTGRES_SSL_MODE}"


# Authentication Providers
# Google (Get your keys from https://console.cloud.google.com/)
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

# GitHub (Get your keys from https://github.com/settings/developers)
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Email Configuration (Using Resend)
AUTH_RESEND_KEY=your-resend-api-key

# Email Configuration (Using Resend, you can leave this as is for local development)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.resend.com
EMAIL_PORT=465
EMAIL_USER=resend
EMAIL_PASS=${AUTH_RESEND_KEY}

# Stripe Configuration
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
# NEXT_PUBLIC_PRO_PRICE_ID=your-stripe-pro-price-id

# Amazon S3 and SES Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=your-aws-region
AWS_BUCKET_NAME=your-aws-bucket-name
AWS_SES_FROM_EMAIL=your-verified-ses-email-address

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# ====== OPTIONAL VARIABLES ======

# Vercel Blob (for uploading images)
BLOB_READ_WRITE_TOKEN=your-vercel-blob-read-write-token

# OpenAI Base URL (default: https://api.openai.com/v1)
# OPENAI_BASE_URL=

# Vercel KV (for rate limiting)
KV_URL=your-vercel-kv-url
KV_REST_API_URL=your-vercel-kv-rest-api-url
KV_REST_API_TOKEN=your-vercel-kv-rest-api-token
KV_REST_API_READ_ONLY_TOKEN=your-vercel-kv-rest-api-read-only-token
