# Next.js SaaS Boilerplate

A modern, feature-rich boilerplate for building SaaS applications using Next.js 14, TypeScript, and various other technologies.

## Core Features

- **Next.js 14**: Utilizing the latest version of Next.js for optimal performance and features
- **TypeScript**: For type-safe code and improved developer experience
- **Authentication**: Implemented using NextAuth.js with Prisma adapter
- **Database**: Prisma ORM for database management
- **Styling**: Tailwind CSS for utility-first styling
- **UI Components**: Radix UI and Shadcn UI for accessible and customizable components
- **Blog Functionality**: With MDX support and a rich text editor (Novel)
- **Documentation**: Built-in documentation system using MDX
- **API Rate Limiting**: Using Upstash
- **File Uploads**: AWS SDK for S3 integration
- **Payments**: Stripe integration for subscription management
- **Emails**: Nodemailer for email functionality
- **Testing**: Jest and React Testing Library for unit and integration tests
- **Docker Support**: For containerized development and deployment

## Prerequisites

Before you begin, ensure you have the following installed:

- Node.js (v18 or later recommended)
- Yarn or npm
- Docker (optional, for containerized development)

## Project Structure

```
src/
├── actions/       # Server actions
├── app/           # Next.js app router
├── components/    # React components
│   ├── posts/    # Blog and post-related components
│   ├── providers/# Application providers
│   └── ui/       # Shared UI components
├── config/        # Configuration files
├── content/       # MDX content
│   └── docs/     # Documentation content
├── lib/          # Utility functions and libraries
├── styles/       # Global styles
└── types/        # TypeScript type definitions
```

## Installation & Setup

1. Clone the repository:

   ```
   git clone https://github.com/yourusername/next-saas-boilerplate.git
   cd next-saas-boilerplate
   ```

2. Install dependencies:

   ```
   yarn install
   ```

   or

   ```
   npm install
   ```

3. Set up environment variables:
   Copy the `.env.example` file to `.env` and fill in the required values.

4. Open the Docker app and run the following command:

   ```
   npm run docker:up
   ```

   This step is necessary to set up the required Docker containers before proceeding with database setup.

5. Set up the database:

   ```
   npx prisma generate
   npx prisma db push
   ```

6. Seed the database (optional):
   ```
   yarn prisma:seed
   ```
   or
   ```
   npm run prisma:seed
   ```

## Running the Application

For development:

```
yarn dev
```

or

```
npm run dev
```

For production:

```
yarn build
yarn start
```

or

```
npm run build
npm start
```

## Development with Docker

We've set up a Docker development environment to make it easier to run the application with all its dependencies, including the database. Here's how to use it:

1. Ensure you have Docker and Docker Compose installed on your machine.

2. Create a `.env` file in the root of your project if you haven't already. This file should contain all the necessary environment variables for your application and database. For example:

   ```
   APP_NAME=your_app_name
   POSTGRES_PASSWORD=your_db_password
   POSTGRES_USER=your_db_user
   POSTGRES_DB=your_db_name
   # Add any other environment variables your Next.js app needs
   ```

3. To start the development environment, run:

   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

   This command will:

   - Build the Docker images if they don't exist or if there have been changes
   - Start the Next.js application in development mode
   - Start a PostgreSQL database
   - Apply any pending database migrations
   - Load environment variables from your `.env` file

4. Your Next.js application will be available at `http://localhost:3000`. The PostgreSQL database will be accessible on port 5432.

5. Any changes you make to your local files will be reflected in the running app due to the volume mount and development server.

6. To stop the development environment, use:

   ```bash
   docker-compose -f docker-compose.dev.yml down
   ```

7. If you need to rebuild the containers (e.g., after adding new dependencies), use the same command as in step 3.

8. To view the logs of a specific service, you can use:

   ```bash
   docker-compose -f docker-compose.dev.yml logs -f [service_name]
   ```

   Replace `[service_name]` with either `app` for the Next.js application or `postgres` for the database.

Remember to never commit your `.env` file to version control. It's already included in the `.gitignore` file to prevent accidental commits.

## Available Scripts

- `dev`: Start the development server
- `build`: Build the application for production
- `start`: Start the production server
- `lint`: Run ESLint
- `test`: Run Jest tests in watch mode
- `test:ci`: Run Jest tests for CI
- `docker:container`: Run the application in a Docker container
- `docker:up`: Start the application using Docker Compose
- `docker:down`: Stop the Docker containers
- `docker:start`: Start both the app and database Docker containers in development mode
- `docker:stop`: Stops both the app and database Docker containers
- `prisma:seed`: Seed the database
- `prisma:migrate`: Run Prisma migrations
- `prisma:push`: Push the Prisma schema to the database
- `prisma:generate`: Generate Prisma client
- `prisma:studio`: Open Prisma Studio
- `stripe:listen`: Start Stripe webhook listener
- `vercel-build`: Build script for Vercel deployment

## Implementation Todos

### Authentication

- [x] Set up NextAuth.js with Prisma adapter
- [x] Implement login/logout functionality
- [x] Add social login options (Google, GitHub)
- [x] Implement email verification
- [ ] Add two-factor authentication
- [ ] Implement password reset flow
- [ ] Add session management

### Database

- [x] Set up Prisma ORM
  - [x] Configure Prisma client
  - [x] Set up database adapters
  - [x] Configure connection pooling
- [x] Create initial database schema
  - [x] User and authentication models
  - [x] Multi-tenant models
  - [x] Content models (posts, comments)
  - [x] Subscription and billing models
- [x] Implement database migrations
  - [x] Initial schema migration
  - [x] Database seeding
  - [x] Migration scripts
- [x] Create seed data for development environment
  - [x] Test users and tenants
  - [x] Sample content
  - [x] Subscription plans
- [x] Implement database indexing
  - [x] Performance indexes
  - [x] Foreign key indexes
  - [x] Unique constraints
- [ ] Set up database backups
  - [ ] Automated backups
  - [ ] Backup verification
  - [ ] Restore procedures
- [ ] Add database monitoring and logging
  - [ ] Query performance monitoring
  - [ ] Error tracking
  - [ ] Connection pooling metrics

### Testing

- [x] Set up Jest and React Testing Library
  - [x] Configure Jest for the project
  - [x] Set up test environment for React components
  - [ ] Configure test coverage reporting
- [ ] Write unit tests for core components
  - [ ] Test authentication components
  - [ ] Create tests for UI components (buttons, forms, etc.)
  - [ ] Test utility functions and helpers
- [ ] Implement integration tests for key features
  - [ ] Test user registration and login flows
  - [ ] Create tests for blog post creation and editing
  - [ ] Test API endpoints
- [ ] Set up end-to-end (E2E) testing
  - [ ] Choose and configure an E2E testing tool (e.g., Cypress, Playwright)
  - [ ] Write E2E tests for critical user journeys
- [ ] Implement continuous integration (CI) for running tests
  - [x] Set up GitHub Actions or another CI tool
  - [ ] Configure test automation on PR creation
  - [ ] Set up test reporting and notifications

### Performance Optimization

- [ ] Implement comprehensive caching strategy
  - [x] Client-side query caching (React Query)
  - [ ] Server-side caching
  - [ ] API response caching
  - [ ] Static asset caching
- [x] Add image optimization (via Next.js Image component)
- [ ] Configure CDN integration
- [ ] Set up performance monitoring
- [x] Implement lazy loading for components
- [ ] Add service worker for offline support

### Security

- [x] Implement rate limiting for API routes
  - [x] Upstash Redis integration
  - [x] Different limits for various operations
  - [x] IP-based and user-based limiting
  - [x] Rate limit headers
- [x] Set up CORS policies
  - [x] Configurable allowed origins
  - [x] Proper headers configuration
  - [x] Options handling
- [x] Add input sanitization
  - [x] HTML sanitization with DOMPurify
  - [x] Input validation with Zod
  - [x] Field-specific sanitization
- [x] Implement content security policies
- [x] Set up security headers
- [x] Add API authentication
  - [x] Route protection
  - [x] API key authentication
  - [x] Session validation
- [x] Role-based access control
  - [x] Role-based route protection
  - [x] Permission system
  - [x] Resource-based access control
- [ ] Add two-factor authentication
- [ ] Implement audit logging
  - [x] Error logging
  - [ ] Access logging
  - [ ] Change tracking
- [ ] Set up security monitoring
  - [ ] Intrusion detection
  - [ ] Automated security scanning
  - [ ] Vulnerability reporting
- [ ] Implement API versioning
- [ ] Add request signing

### Error Management

- [x] Implement centralized error handling system
  - [x] Custom AppError class with context support
  - [x] Standardized error codes and HTTP status codes
  - [x] Error serialization/deserialization
- [x] Server-side error handling
  - [x] Prisma error transformation
  - [x] NextAuth error handling
  - [x] Validation error handling (Zod)
  - [x] Network error detection
  - [x] Generic error wrapping
- [x] Client-side error handling
  - [x] Toast notifications integration
  - [x] Loading state management
  - [x] Form validation error handling
  - [x] Network error handling
- [x] API error responses
  - [x] Consistent error format
  - [x] Status code mapping
  - [x] Context preservation
- [x] Error documentation
  - [x] Error types documentation
  - [x] Usage examples
  - [x] Best practices
- [x] Error monitoring and logging
  - [x] Error context logging
  - [x] Stack trace preservation
  - [ ] Integration with external error tracking (e.g., Sentry)
- [ ] Error recovery
  - [x] Global error boundary
  - [ ] Retry mechanisms
  - [ ] Fallback UI components
  - [ ] Auto-recovery strategies

## Technologies Used

- [Next.js](https://nextjs.org/)
- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Prisma](https://www.prisma.io/)
- [NextAuth.js](https://next-auth.js.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Shadcn UI](https://ui.shadcn.com/)
- [Novel](https://novel.sh/)
- [Stripe](https://stripe.com/)
- [AWS SDK](https://aws.amazon.com/sdk-for-javascript/)
- [Jest](https://jestjs.io/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Docker](https://www.docker.com/)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License.
