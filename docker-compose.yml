services:
  postgres:
    image: postgres:latest
    container_name: ${APP_NAME}-container
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - 5432:5432
    volumes:
      - postgres:/var/lib/postgresql/data

  app:
    build: .
    container_name: ${APP_NAME}-nextjs
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
    depends_on:
      - postgres

volumes:
  postgres:
    name: ${APP_NAME}-volume