import withMD<PERSON> from '@next/mdx';
import withPlaiceholder from '@plaiceholder/next';
import createNextIntlPlugin from 'next-intl/plugin';
import rehypePrettyCode from 'rehype-pretty-code';
import rehypeSlug from 'rehype-slug';
import remarkGfm from 'remark-gfm';
import moonlightTheme from './public/assets/monlight-ii.json' with { type: 'json' };

const nextConfig = {
  output: 'standalone',
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.public.blob.vercel-storage.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'saasboilerplate.s3.amazonaws.com',
        pathname: '/**',
      },
      { protocol: 'https', hostname: 'placehold.co', pathname: '/**' },
      { protocol: 'https', hostname: 'doodleipsum.com', pathname: '/**' },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        pathname: '/**',
      },
      { protocol: 'https', hostname: 'picsum.photos', pathname: '/**' },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '/**',
      },
    ],
  },
};

// Configuration objects
const mdxOptions = {
  extension: /\.(md|mdx)$/,
  options: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [
      rehypeSlug,
      [rehypePrettyCode, { keepBackground: false, theme: moonlightTheme }],
    ],
  },
};

// Array of wrapper functions
const wrappers = [
  (config) => withPlaiceholder(config),
  (config) => createNextIntlPlugin()(config),
  (config) => withMDX(mdxOptions)(config),
];

// Compose function to apply all wrappers
const composeWrappers = (baseConfig) =>
  wrappers.reduce((config, wrapper) => wrapper(config), baseConfig);

const config = async () => composeWrappers(nextConfig);

export default config;
