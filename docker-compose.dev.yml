services:
  postgres:
    image: postgres:latest
    container_name: ${APP_NAME}-dev-postgres
    env_file: .env
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - 5432:5432
    volumes:
      - postgres-dev:/var/lib/postgresql/data

  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: deps
    container_name: ${APP_NAME}-dev-nextjs
    env_file: .env
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    command: |
      if [ -f yarn.lock ]; then
        yarn dev
      elif [ -f package-lock.json ]; then
        npm run dev
      elif [ -f pnpm-lock.yaml ]; then
        pnpm dev
      else
        echo "No lockfile found. Please ensure you have a package manager lockfile."
        exit 1
      fi
    depends_on:
      - postgres

volumes:
  postgres-dev:
    name: ${APP_NAME}-dev-volume
