{
  "extends": [
    "next/core-web-vitals",
    "next/typescript",
    "plugin:testing-library/react",
    "plugin:jest-dom/recommended",
    "prettier"
  ],
  "rules": {
    "react-hooks/exhaustive-deps": "off",
    // TODO: come back to this later
    "@typescript-eslint/no-empty-object-type": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-require-imports": "off"
  },
  "ignorePatterns": ["src/components/ui/**/*", "src/components/archived/**/*"]
}
