'use client';

import { useState } from 'react';
import { CreatePostSchema, CreatePostValues } from '@/schemas/posts.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

export const usePost = () => {
  const [contentLoaded, setContentLoaded] = useState<boolean>(false);
  const [coverImageUrl, setCoverImageUrl] = useState<string | null>(null);
  const [isPublished, setIsPublished] = useState<boolean>(false);
  const [cover, setCover] = useState<FormData | null>(null);

  const hasCover = Boolean(coverImageUrl);

  const form = useForm<CreatePostValues>({
    resolver: zodResolver(CreatePostSchema),
    defaultValues: {
      title: '',
      content: undefined,
      tags: [],
      cover: null,
    },
  });

  const reset = () => {
    form.reset({
      title: '',
      content: undefined,
      tags: [],
      cover: null,
    });
    setContentLoaded(false);
    setCoverImageUrl(null);
    setIsPublished(false);
  };

  return {
    form,
    post: {
      contentLoaded,
      coverImageUrl,
      hasCover,
      setCoverImageUrl,
      setContentLoaded,
      reset,
      isPublished,
      setIsPublished,
      cover,
      setCover,
      author: {
        id: '',
        name: '',
        image: '',
        email: '',
        username: '',
      },
    },
  };
};
