import { useMemo, useState } from 'react';
import { ITEMS_PER_PAGE } from '@/constants';
import { SortingState } from '@tanstack/react-table';
import { parseAsInteger, parseAsStringLiteral, useQueryState } from 'nuqs';
import { useDebounce } from 'use-debounce';

import { GetPaginatedResult, Order } from '@/types/shared.types';

type UseTableProps<T> = {
  initialData: GetPaginatedResult<T>;
};

const order = ['asc', 'desc'] as const;

export function useTable<T>({ initialData }: UseTableProps<T>) {
  const [data, setData] = useState<T[]>(initialData.items);

  const [searchTerm, setSearchTerm] = useQueryState('q', {
    defaultValue: initialData.filter || '',
  });
  const [debouncedSearchTerm] = useDebounce(
    searchTerm,
    // if user clears the search, it will not debounce it and kick in immediately
    searchTerm === '' ? 0 : 500
  );

  const [sortOrder, setSortOrder] = useQueryState(
    'order',
    parseAsStringLiteral(order).withDefault(initialData.order as Order)
  );

  const [sortBy, setSortBy] = useQueryState('orderBy', {
    defaultValue: initialData.orderBy,
  });

  const [page, setPage] = useQueryState(
    'page',
    parseAsInteger.withDefault(initialData.page)
  );
  const [totalItems, setTotalItems] = useState(initialData.totalCount);
  const [isLoading, setIsLoading] = useState(false);
  const itemsPerPage = ITEMS_PER_PAGE;
  const isEmpty = initialData.totalCount === 0;
  const isPaginationVisible = totalItems > itemsPerPage;

  const sorting: SortingState = useMemo(
    () =>
      sortBy && sortOrder ? [{ id: sortBy, desc: sortOrder === 'desc' }] : [],
    [sortBy, sortOrder]
  );

  const onSortChange = useMemo(
    () => (updater: any) => {
      const newSorting =
        updater instanceof Function ? updater(sorting) : updater;
      if (newSorting && newSorting.length > 0) {
        const { id, desc } = newSorting[0];
        setSortBy(id);
        setSortOrder(desc ? 'desc' : 'asc');
      }
    },
    [sorting, setSortBy, setSortOrder]
  );

  const defaultParams = useMemo(
    () => ({
      take: itemsPerPage,
      page,
      orderBy: sortBy ? { [sortBy]: sortOrder as 'asc' | 'desc' } : undefined,
      filter: { searchQuery: debouncedSearchTerm },
    }),
    [itemsPerPage, page, sortBy, sortOrder, debouncedSearchTerm]
  );

  // reset nuqs function
  const resetQueryState = () => {
    setSearchTerm('');
    setSortOrder(initialData.order as Order);
    setSortBy(initialData.orderBy);
    setPage(1);
  };

  return {
    data,
    setData,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sorting,
    setSortOrder,
    sortBy,
    onSortChange,
    setSortBy,
    page,
    setPage,
    totalItems,
    setTotalItems,
    isLoading,
    setIsLoading,
    itemsPerPage,
    resetQueryState,
    initialData,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  };
}
