'use client';

import { useEffect, useReducer, useRef } from 'react';
import { z } from 'zod';

import { Notification } from '../types/notification.types';

type State = {
  notifications: Notification[];
  unreadCount: number;
};

type Action =
  | { type: 'ADD_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'MARK_AS_READ'; payload: string[] }
  | { type: 'DELETE_NOTIFICATION'; payload: string };

// Define nested schemas first
const UserSchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  image: z.string().nullable(),
  email: z.string().nullable(),
  username: z.string().nullable(),
});

const PostSchema = z.object({
  id: z.string(),
  slug: z.string(),
  title: z.string(),
});

// Main notification schema with proper relationships
const notificationSchema = z
  .object({
    id: z.string(),
    createdAt: z.string().transform((str) => new Date(str)),
    updatedAt: z.string().transform((str) => new Date(str)),
    content: z.string(),
    type: z.string(),
    isRead: z.boolean(),
    userId: z.string(),
    fromId: z.string(),
    from: UserSchema,
    postId: z.string().nullable(),
    post: PostSchema.nullable(),
    commentId: z.string().nullable(),
    likeId: z.string().nullable(),
    tenantId: z.string(),
  })
  .strict()
  .transform((val) => ({
    ...val,
    createdAt: new Date(val.createdAt),
    updatedAt: new Date(val.updatedAt),
    post: val.post || null,
  })) as unknown as z.ZodType<Notification>;

type SSEEvent = z.infer<typeof sseEventSchema>;

// Type guard for SSEEvent
function isSSEEvent(data: unknown): data is SSEEvent {
  return typeof data === 'object' && data !== null && 'type' in data;
}

const sseEventSchema = z.union([
  z.object({
    type: z.literal('NEW_NOTIFICATIONS'),
    data: z.array(notificationSchema),
  }),
  z.object({
    type: z.literal('READ_NOTIFICATIONS'),
    data: z.array(z.object({ id: z.string() })),
  }),
  z.object({
    type: z.literal('TIMEOUT'),
    data: z.optional(z.unknown()),
  }),
]);

function notificationsReducer(state: State, action: Action): State {
  switch (action.type) {
    case 'ADD_NOTIFICATIONS': {
      const existingIds = new Set(state.notifications.map((n) => n.id));
      const newNotifications = action.payload.filter(
        (n) => !existingIds.has(n.id)
      );

      return {
        notifications: [...newNotifications, ...state.notifications],
        unreadCount: state.unreadCount + newNotifications.length,
      };
    }
    case 'MARK_AS_READ': {
      const readIds = new Set(action.payload);
      return {
        notifications: state.notifications.map((notif) =>
          readIds.has(notif.id) ? { ...notif, isRead: true } : notif
        ),
        unreadCount: state.notifications.filter(
          (notif) => !readIds.has(notif.id) && !notif.isRead
        ).length,
      };
    }
    case 'DELETE_NOTIFICATION': {
      const updatedNotifications = state.notifications.filter(
        (notif) => notif.id !== action.payload
      );
      return {
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter((notif) => !notif.isRead)
          .length,
      };
    }
    default:
      return state;
  }
}

export function useSSE() {
  const [state, dispatch] = useReducer(notificationsReducer, {
    notifications: [],
    unreadCount: 0,
  });
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connect = () => {
    console.log('[SSE] Initializing connection...');

    if (eventSourceRef.current) {
      console.log('[SSE] Connection already exists');
      return;
    }

    const apiUrl =
      process.env.NODE_ENV === 'development'
        ? 'http://localhost:3000/api/sse'
        : '/api/sse';

    console.log('[SSE] Creating new connection to:', apiUrl);
    try {
      const eventSource = new EventSource(apiUrl);
      eventSourceRef.current = eventSource;
      console.log('[SSE] Connection created successfully');

      const messageHandler = (event: MessageEvent) => {
        try {
          console.log('[SSE] Received message:', event.data);
          const rawData = JSON.parse(event.data);
          const result = sseEventSchema.safeParse(rawData) as {
            success: boolean;
            data?: SSEEvent;
            error?: z.ZodError;
          };

          if (!result.success) {
            console.error('[SSE] Invalid data format:', result.error);
            return;
          }

          if (!result.data) {
            console.error('[SSE] Missing data in event');
            return;
          }

          if (!isSSEEvent(result.data)) {
            console.error('[SSE] Invalid event format');
            return;
          }

          const data = result.data as SSEEvent;
          switch (data.type) {
            case 'NEW_NOTIFICATIONS':
              console.log('[SSE] New notifications:', data.data.length);
              dispatch({ type: 'ADD_NOTIFICATIONS', payload: data.data });
              break;
            case 'READ_NOTIFICATIONS':
              console.log('[SSE] Read notifications:', data.data.length);
              dispatch({
                type: 'MARK_AS_READ',
                payload: data.data.map((notif) => notif.id),
              });
              break;
            case 'TIMEOUT':
              console.log('[SSE] Connection timeout, reconnecting...');
              eventSourceRef.current?.close();
              eventSourceRef.current = null;
              reconnectTimeoutRef.current = setTimeout(connect, 3000);
              break;
            default: {
              console.warn(
                '[SSE] Unknown event type:',
                (data as { type: string }).type
              );
              break;
            }
          }
        } catch (error) {
          console.error('[SSE] Error processing message:', error);
        }
      };

      const errorHandler = () => {
        console.log('[SSE] Connection error, reconnecting...');
        eventSourceRef.current?.close();
        eventSourceRef.current = null;
        reconnectTimeoutRef.current = setTimeout(connect, 3000);
      };

      eventSource.addEventListener('open', () => {
        console.log('[SSE] Connection opened');
      });

      eventSource.addEventListener('message', messageHandler);
      eventSource.addEventListener('error', errorHandler);

      return () => {
        if (eventSourceRef.current) {
          eventSourceRef.current.removeEventListener('message', messageHandler);
          eventSourceRef.current.removeEventListener('error', errorHandler);
          eventSourceRef.current.close();
          eventSourceRef.current = null;
        }
        clearTimeout(reconnectTimeoutRef.current);
      };
    } catch (error) {
      console.error('[SSE] Failed to create connection:', error);
      return;
    }
  };

  useEffect(() => {
    const cleanup = connect();

    return () => {
      cleanup?.();
      clearTimeout(reconnectTimeoutRef.current);
    };
  }, []);

  const markNotificationsAsRead = (ids: string[]) => {
    dispatch({ type: 'MARK_AS_READ', payload: ids });
  };

  const removeNotification = (id: string) => {
    dispatch({ type: 'DELETE_NOTIFICATION', payload: id });
  };

  return {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    markNotificationsAsRead,
    removeNotification,
  };
}
