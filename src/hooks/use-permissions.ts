import type { Tier } from '@prisma/client';
import { useSession } from 'next-auth/react';

import { SessionUser } from '@/types/user.types';
import type { Action, Resource } from '@/config/permissions.config';
import { permissionService } from '@/lib/permissions';

type PermissionParams = {
  resource: Resource;
  action: Action;
  requiredPlan?: Tier;
};
/**
 * Hook for checking user permissions in components
 *
 * @example Basic usage
 * ```tsx
 * function PostActions({ post }) {
 *   const { can } = usePermissions();
 *
 *   // Check single permission
 *   const canEdit = await can({
 *     resource: 'post',
 *     action: 'update'
 *   });
 *
 *   if (!canEdit) return null;
 *
 *   return <EditButton post={post} />;
 * }
 * ```
 *
 * @example Check multiple permissions
 * ```tsx
 * function PostToolbar({ post }) {
 *   const { checkMultiple } = usePermissions();
 *
 *   const [canEditAndDelete] = await checkMultiple([
 *     { resource: 'post', action: 'update' },
 *     { resource: 'post', action: 'delete' }
 *   ]);
 *
 *   if (!canEditAndDelete) return null;
 *
 *   return <ToolbarActions post={post} />;
 * }
 * ```
 *
 * @example Check premium feature access
 * ```tsx
 * function PremiumFeature() {
 *   const { can } = usePermissions();
 *
 *   const canAccess = await can({
 *     resource: 'feature',
 *     action: 'access',
 *     requiredPlan: Tier.PRO
 *   });
 *
 *   if (!canAccess) {
 *     return <UpgradePrompt />;
 *   }
 *
 *   return <PremiumContent />;
 * }
 * ```
 *
 * @example Using with loading states
 * ```tsx
 * function ProtectedButton({ onClick }) {
 *   const { can } = usePermissions();
 *   const [loading, setLoading] = useState(false);
 *   const [allowed, setAllowed] = useState(false);
 *
 *   useEffect(() => {
 *     const checkPermission = async () => {
 *       setLoading(true);
 *       const hasPermission = await can({
 *         resource: 'feature',
 *         action: 'access'
 *       });
 *       setAllowed(hasPermission);
 *       setLoading(false);
 *     };
 *
 *     checkPermission();
 *   }, [can]);
 *
 *   if (loading) return <Spinner />;
 *   if (!allowed) return null;
 *
 *   return <Button onClick={onClick}>Protected Action</Button>;
 * }
 * ```
 *
 * @returns Object with permission checking functions
 */
export const usePermissions = () => {
  const { data: session } = useSession();
  const user = session?.user as SessionUser;

  return {
    /**
     * Check if user has permission to perform an action on a resource
     * @param params Permission parameters including resource, action and optional plan requirement
     * @returns Promise<boolean> indicating if user has permission
     */
    can: (params: PermissionParams) => {
      return permissionService.can({ ...params, user });
    },
    /**
     * Check multiple permissions at once
     * @param checks Array of permission checks to perform
     * @returns Promise<boolean> indicating if user has ALL permissions
     */
    checkMultiple: (checks: PermissionParams[]) => {
      return permissionService.checkMultiple(
        checks.map((check) => ({ ...check, user }))
      );
    },
  };
};
