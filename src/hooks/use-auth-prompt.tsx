'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

import { Confirm } from '@/components/shared/confirm';

export function useAuthPrompt() {
  const router = useRouter();
  const session = useSession();
  const [isLoginPromptOpen, setIsLoginPromptOpen] = useState(false);
  const [pendingCallback, setPendingCallback] = useState<(() => void) | null>(
    null
  );

  useEffect(() => {
    if (session.data?.user && pendingCallback) {
      pendingCallback();
      setPendingCallback(null);
      setIsLoginPromptOpen(false);
    }
  }, [session.data?.user, pendingCallback]);

  const checkAuth = (callback: () => void) => {
    if (!session.data?.user) {
      setPendingCallback(() => callback);
      setIsLoginPromptOpen(true);
      return false;
    }
    callback();
    return true;
  };

  const handleLoginConfirm = () => {
    const currentPath = window.location.pathname;
    router.push(`/signin?callbackUrl=${encodeURIComponent(currentPath)}`);
  };

  const AuthPrompt = () => (
    <Confirm
      isOpen={isLoginPromptOpen}
      setIsOpen={setIsLoginPromptOpen}
      onCancel={() => {
        setIsLoginPromptOpen(false);
        setPendingCallback(null);
      }}
      onConfirm={handleLoginConfirm}
      title="Sign in required"
      description="You need to be signed in to perform this action. Would you like to sign in now?"
    />
  );

  return {
    checkAuth,
    AuthPrompt,
    isAuthenticated: !!session.data?.user,
  };
}
