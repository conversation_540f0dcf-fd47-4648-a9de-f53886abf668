import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import {
  API_AUTH_PREFIX,
  AUTH_ROUTES,
  PUBLIC_ROUTES,
  REDIRECT_ONBOARDING_URL,
  REDIRECT_URL,
} from '@/constants';
import { Role } from '@prisma/client';

import { appConfig } from '@/config/app.config';

import { corsMiddleware } from './middleware/cors';

// Helper functions to make the middleware more readable
const isMatchingRoute = (pathname: string, routes: string[]) =>
  routes.some((route) => pathname.startsWith(route));

const createRedirectResponse = (url: string, nextUrl: URL) =>
  NextResponse.redirect(new URL(url, nextUrl));

export default auth(async (req) => {
  // Apply CORS middleware first
  const corsResponse = corsMiddleware(req);
  if (req.method === 'OPTIONS') {
    return corsResponse;
  }

  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;
  const user = req.auth?.user;
  const hasProfile = user?.name && user?.username;

  // Fast path for API auth routes
  if (nextUrl.pathname.startsWith(API_AUTH_PREFIX)) {
    return corsResponse;
  }

  // Fast path for public routes
  if (isMatchingRoute(nextUrl.pathname, PUBLIC_ROUTES)) {
    return corsResponse;
  }

  // Handle onboarding redirect
  if (
    nextUrl.pathname === REDIRECT_ONBOARDING_URL &&
    isLoggedIn &&
    hasProfile
  ) {
    return createRedirectResponse(REDIRECT_URL, nextUrl);
  }

  // Handle root path
  if (nextUrl.pathname === '/') {
    if (!isLoggedIn) return corsResponse;
    return createRedirectResponse(
      hasProfile ? REDIRECT_URL : REDIRECT_ONBOARDING_URL,
      nextUrl
    );
  }

  // Handle auth routes
  if (isMatchingRoute(nextUrl.pathname, AUTH_ROUTES)) {
    if (!isLoggedIn) return corsResponse;
    return createRedirectResponse(
      hasProfile ? REDIRECT_URL : REDIRECT_ONBOARDING_URL,
      nextUrl
    );
  }

  // Handle authenticated routes
  if (!isLoggedIn) {
    const searchParams = new URLSearchParams({
      callbackUrl: nextUrl.pathname,
    });
    return createRedirectResponse(
      `/signin?${searchParams.toString()}`,
      nextUrl
    );
  }

  // Handle incomplete profile
  if (
    isLoggedIn &&
    !hasProfile &&
    nextUrl.pathname !== REDIRECT_ONBOARDING_URL
  ) {
    return createRedirectResponse(REDIRECT_ONBOARDING_URL, nextUrl);
  }

  // Handle role-based access
  const userRole = user?.role as Role;
  const configRoutes = [...appConfig.sidebarNav, ...appConfig.admin];
  const matchingRoute = configRoutes.find(
    (route) => route.href && nextUrl.pathname.startsWith(route.href)
  );

  if (matchingRoute?.roles && !matchingRoute.roles.includes(userRole)) {
    return createRedirectResponse(REDIRECT_URL, nextUrl);
  }

  return corsResponse;
}) as unknown as typeof auth;

export const config = {
  matcher: [
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    '/(api|trpc)(.*)',
  ],
};
