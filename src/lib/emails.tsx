import React from 'react';
import MagicLinkEmail, { MagicLinkEmailProps } from '@/emails/magic-link';
import { NewsletterEmail, NewsletterEmailProps } from '@/emails/newsletter';
import {
  NewsletterConfirmationEmail,
  NewsletterConfirmationEmailProps,
} from '@/emails/newsletter-confirmation';
import {
  SubscriptionEmail,
  SubscriptionEmailProps,
} from '@/emails/subscription';
import { render } from '@react-email/components';

import {
  UserInvitationEmail,
  UserInvitationEmailProps,
} from '../emails/user-invitation';

export function generateInvitationEmail(params: UserInvitationEmailProps) {
  return render(<UserInvitationEmail {...params} />);
}

export function generateMagicLinkEmail(params: MagicLinkEmailProps) {
  return render(<MagicLinkEmail {...params} />);
}

export function generateSubscriptionEmail(params: SubscriptionEmailProps) {
  // Render email component
  return render(<SubscriptionEmail {...params} />);
}

export function generateNewsletterEmail(params: NewsletterEmailProps) {
  return render(<NewsletterEmail {...params} />);
}

export function generateNewsletterConfirmationEmail(
  params: NewsletterConfirmationEmailProps
) {
  return render(<NewsletterConfirmationEmail {...params} />);
}
