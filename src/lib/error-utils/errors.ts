export const HttpStatusCode = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

export type HttpStatusCode =
  (typeof HttpStatusCode)[keyof typeof HttpStatusCode];

export const ErrorCodes = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_INPUT: 'INVALID_INPUT',
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  FORBIDDEN: 'FORBIDDEN',
  SERVER_ERROR: 'SERVER_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  CONFLICT: 'CONFLICT',
} as const;

export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes];

export interface ErrorContext {
  description?: string;
  field?: string;
  value?: unknown;
  target?: string[]; // Often from Prisma P2002 meta
  prismaCode?: string; // Specific Prisma error code
  issues?: any; // For Zod issues (use a more specific type if possible, e.g., ZodIssue[])
  cause?: unknown; // Explicitly add the cause (can be Error, unknown, etc.)
  originalError?: unknown; // For unknown errors caught at the end
  [key: string]: unknown; // Allow flexible additional context (keeps working)
}

export class AppError extends Error {
  constructor(
    message: string,
    public code: ErrorCode,
    public statusCode: HttpStatusCode = DefaultStatusCodes[code],
    public context?: ErrorContext
  ) {
    super(message);
    this.name = 'AppError';
    Object.setPrototypeOf(this, AppError.prototype);
  }
}

// Add mapping between ErrorCodes and HttpStatusCode
export const DefaultStatusCodes: Record<ErrorCode, HttpStatusCode> = {
  UNAUTHORIZED: HttpStatusCode.UNAUTHORIZED,
  INVALID_INPUT: HttpStatusCode.BAD_REQUEST,
  NOT_FOUND: HttpStatusCode.NOT_FOUND,
  ALREADY_EXISTS: HttpStatusCode.CONFLICT,
  FORBIDDEN: HttpStatusCode.FORBIDDEN,
  SERVER_ERROR: HttpStatusCode.INTERNAL_SERVER_ERROR,
  RATE_LIMIT_EXCEEDED: HttpStatusCode.TOO_MANY_REQUESTS,
  VALIDATION_ERROR: HttpStatusCode.UNPROCESSABLE_ENTITY,
  DATABASE_ERROR: HttpStatusCode.INTERNAL_SERVER_ERROR,
  NETWORK_ERROR: HttpStatusCode.SERVICE_UNAVAILABLE,
  BAD_REQUEST: HttpStatusCode.BAD_REQUEST,
  CONFLICT: HttpStatusCode.CONFLICT,
} as const;

// Predefined errors for common cases
// Predefined errors for common cases - NOW WITH CONTEXT PARAMETER
export const Errors = {
  // ADDED context?: ErrorContext and pass it to new AppError
  Unauthorized: (message = 'Unauthorized access', context?: ErrorContext) =>
    new AppError(
      message,
      ErrorCodes.UNAUTHORIZED,
      HttpStatusCode.UNAUTHORIZED,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  NotFound: (resource = 'Resource', context?: ErrorContext) =>
    new AppError(
      `${resource} not found`,
      ErrorCodes.NOT_FOUND,
      HttpStatusCode.NOT_FOUND,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  ValidationError: (message: string, context?: ErrorContext) =>
    new AppError(
      message,
      ErrorCodes.VALIDATION_ERROR,
      HttpStatusCode.UNPROCESSABLE_ENTITY,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  RateLimitExceeded: (context?: ErrorContext) =>
    new AppError(
      'Rate limit exceeded. Please try again later.',
      ErrorCodes.RATE_LIMIT_EXCEEDED,
      HttpStatusCode.TOO_MANY_REQUESTS,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  DatabaseError: (
    message = 'A database error occurred',
    context?: ErrorContext
  ) =>
    new AppError(
      message,
      ErrorCodes.DATABASE_ERROR,
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  NetworkError: (
    message = 'A network error occurred',
    context?: ErrorContext
  ) =>
    new AppError(
      message,
      ErrorCodes.NETWORK_ERROR,
      HttpStatusCode.SERVICE_UNAVAILABLE,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  Forbidden: (message = 'Permission denied', context?: ErrorContext) =>
    new AppError(
      message,
      ErrorCodes.FORBIDDEN,
      HttpStatusCode.FORBIDDEN,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  ServerError: (
    message = 'An internal server error occurred',
    context?: ErrorContext
  ) =>
    new AppError(
      message,
      ErrorCodes.SERVER_ERROR,
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  Conflict: (message = 'Resource conflict', context?: ErrorContext) =>
    new AppError(
      message,
      ErrorCodes.ALREADY_EXISTS,
      HttpStatusCode.CONFLICT,
      context
    ),

  // ADDED context?: ErrorContext and pass it to new AppError
  BadRequest: (message = 'Bad request', context?: ErrorContext) =>
    new AppError(
      message,
      ErrorCodes.INVALID_INPUT,
      HttpStatusCode.BAD_REQUEST,
      context
    ),
};
