import { Prisma } from '@prisma/client';
import { toast } from 'sonner';
import { ZodError } from 'zod'; // Import ZodError if you use Zod for validation

// Import everything needed from your errors file
import {
  AppError,
  ErrorCodes,
  ErrorContext,
  Errors, // Import the Errors factory
  HttpStatusCode,
} from './errors';

// Add new type for validation error formatting
interface FormattedValidationError {
  field: string;
  message: string;
  code: string;
}

// Add helper function for formatting validation errors
export function formatZodError(error: ZodError): FormattedValidationError[] {
  return error.issues.map((issue) => ({
    field: issue.path.join('.'),
    message: issue.message,
    code: issue.code,
  }));
}

export function logError(error: unknown, context?: Record<string, any>) {
  const errorInfo = {
    timestamp: new Date().toISOString(),
    error:
      error instanceof Error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
            ...(error instanceof AppError && {
              code: error.code,
              statusCode: error.statusCode,
              context: error.context,
            }),
          }
        : error,
    additionalContext: context,
  };

  // Log actual errors
  console.error('Error occurred:', errorInfo);

  // Here you could add integration with error tracking services
  // e.g., Sentry, LogRocket, etc.
}

// Server-side error handling - throws transformed errors
export function handleServerError(
  error: unknown,
  context?: Record<string, any>
): never {
  // Return type is 'never' as it always throws
  // 1. Handle NextAuth/Next.js redirects FIRST.
  // These must be re-thrown for the framework to handle them.
  if (error instanceof Error && error.message === 'NEXT_REDIRECT') {
    throw error;
  }

  // 2. Log all other errors *after* checking for redirects.
  logError(error, context);

  // 3. If it's already a standardized AppError, propagate it.
  if (error instanceof AppError) {
    throw error;
  }

  // 4. Handle specific known error types (e.g., from NextAuth, Zod)
  if (error instanceof Error) {
    switch (error.name) {
      // --- NextAuth specific errors ---
      case 'CredentialsSignin':
        throw Errors.Unauthorized(
          error.message || 'Invalid credentials.', // Use error message if available
          { cause: error } // Add original error to context if needed
        );
      case 'AccessDenied': // Often means authenticated but not allowed
      case 'Verification': // E.g., Email verification error
        throw Errors.Forbidden(error.message || 'Access Denied', {
          cause: error,
        });
      // Add other specific NextAuth error names as needed

      // --- Zod validation errors ---

      case 'ZodError': // Assuming error is ZodError instance
        if (error instanceof ZodError) {
          const validationContext: ErrorContext = {
            issues: formatZodError(error),
            description: error.issues[0]?.message,
          };
          throw Errors.ValidationError('Validation failed', validationContext);
        }
        break; // Fall through if somehow it wasn't a ZodError instance

      // Add other specific error names/types from libraries here
    }

    // 4.b Handle generic auth-related errors based on context or message (Use cautiously)
    if (context?.isAuthRoute || error.message.toLowerCase().includes('auth')) {
      // If flagged as auth route or mentions auth, and not caught above,
      // treat as Unauthorized as a fallback.
      throw Errors.Unauthorized(error.message || 'Authentication required.', {
        cause: error,
      });
    }
  }

  // 5. Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002': // Unique constraint violation
        throw Errors.Conflict('Resource already exists', {
          target: error.meta?.target as string[] | undefined,
          prismaCode: error.code,
        });
      case 'P2025': // Record to update/delete not found
        throw Errors.NotFound(
          'Resource', // Or derive resource name from context/error if possible
          { prismaCode: error.code }
        );
      default: // Other known database errors
        throw Errors.DatabaseError('A database error occurred', {
          prismaCode: error.code,
          cause: error,
        });
    }
  }
  // Handle other Prisma error types if needed
  if (error instanceof Prisma.PrismaClientValidationError) {
    throw Errors.ValidationError('Database input validation failed', {
      cause: error,
    });
  }

  // 6. Handle network errors (e.g., server-side fetch failing)
  if (error instanceof TypeError && error.message === 'Failed to fetch') {
    throw Errors.NetworkError('Network error: Failed to fetch', {
      cause: error,
    });
  }

  // 7. Handle generic JavaScript Errors
  if (error instanceof Error) {
    // Wrap generic errors in a standard ServerError AppError
    throw Errors.ServerError(error.message, { cause: error });
  }

  // 8. Handle unknown errors (values that aren't Error instances)
  throw Errors.ServerError(
    'An unexpected error occurred',
    { originalError: error } // Include the original value in context
  );
}

// --- Corrected handleClientError ---
export function handleClientError(
  error: unknown,
  customErrorTitle?: string // Renamed from customMessage for clarity
): void {
  // Log error for debugging purposes on the client
  console.error('Client error:', error);

  let title: string;
  let description: string | undefined;

  if (error instanceof AppError) {
    title = customErrorTitle || error.message; // Use custom title if provided, else error message
    description = getErrorDescription(error); // Get user-friendly description

    if (error.code === ErrorCodes.VALIDATION_ERROR && error.context?.issues) {
      const issues = error.context.issues as FormattedValidationError[];
      description = issues.map((i) => `${i.field}: ${i.message}`).join('\n');
    } else {
      description = getErrorDescription(error);
    }
  } else if (
    error instanceof TypeError &&
    error.message === 'Failed to fetch'
  ) {
    title = customErrorTitle || 'Network Error';
    description = 'Please check your internet connection and try again.';

    // --- Optional: More robust validation check (if NOT using AppError for it) ---
    // Remove the 'message.includes' check if you always wrap validation errors in AppError
    // } else if (error instanceof ZodError) { // Example if using Zod directly on client
    //   title = customErrorTitle || 'Validation Error';
    //   description = error.errors[0]?.message || 'Please check your input.'; // Basic example
  } else if (
    error instanceof Error &&
    error.message.toLowerCase().includes('validation')
  ) {
    // Fallback: Less reliable check if AppError/ZodError isn't used
    title = customErrorTitle || 'Validation Error';
    description = error.message;
  } else {
    // Generic fallback for other Errors or unknown types
    title = customErrorTitle || 'An Unexpected Error Occurred';
    description =
      error instanceof Error ? error.message : 'Please try again later.';
  }

  toast.error(title, { description });
}

// --- getErrorDescription (remains largely unchanged) ---
// Generates user-friendly descriptions based on AppError status/code
function getErrorDescription(error: AppError): string | undefined {
  // Prefer specific description from context if provided
  if (error.context?.description) {
    return error.context.description;
  }

  switch (error.statusCode) {
    case HttpStatusCode.UNAUTHORIZED:
      return 'Please sign in to continue.';
    case HttpStatusCode.FORBIDDEN:
      return "You don't have permission to perform this action.";
    case HttpStatusCode.TOO_MANY_REQUESTS:
      return 'Too many requests. Please try again later.';
    case HttpStatusCode.NOT_FOUND:
      return 'The requested resource could not be found.';
    case HttpStatusCode.CONFLICT:
      return 'There was a conflict with an existing resource.';
    case HttpStatusCode.UNPROCESSABLE_ENTITY: // Often linked to VALIDATION_ERROR
      return error.message || 'Please check your input for errors.';
    case HttpStatusCode.SERVICE_UNAVAILABLE:
      return 'Service is temporarily unavailable. Please try again later.';
    // Add more cases as needed
    default:
      // For internal server errors, avoid showing technical details from error.message directly
      // unless it's explicitly safe and intended for the user.
      if (error.statusCode >= 500) {
        return 'Something went wrong on our end. Please try again later.';
      }
      // For other client errors (4xx), the error message might be suitable
      return error.message || undefined; // Return undefined if no specific description
  }
}

// --- withToastFeedback ---
export async function withToastFeedback<T>(
  operation: () => Promise<T>,
  options?: {
    loading?: string;
    success?: string | ((data: T) => string); // Allow function for dynamic success message
    error?: string; // Custom error *title* for the toast
    onLoadingChange?: (isLoading: boolean) => void;
  }
): Promise<T> {
  let promise: Promise<T> | null = null;
  options?.onLoadingChange?.(true);

  try {
    // Get the promise *without* awaiting it yet
    promise = operation();

    if (options?.loading) {
      toast.promise(promise, {
        loading: options.loading,
        // Use provided success message (string or function) or a default
        success: (data) =>
          typeof options.success === 'function'
            ? options.success(data)
            : options.success || 'Operation successful!',
        // Error toast is primarily handled by the catch block's handleClientError call.
        // This provides a fallback title if needed, possibly using the custom error option.
        error: (err) =>
          options?.error ||
          (err instanceof Error ? err.message : 'Operation failed'),
      });
    }

    // Await the promise to get the result or trigger the catch block
    const result = await promise;

    // If there was no loading toast, but a success message is specified, show it now.
    if (!options?.loading && options?.success) {
      const successMessage =
        typeof options.success === 'function'
          ? options.success(result)
          : options.success;
      toast.success(successMessage);
    }

    return result;
  } catch (error) {
    // Use the potentially custom error title from options when calling handleClientError.
    // handleClientError will decide the final title/description.
    handleClientError(error, options?.error);
    throw error; // Re-throw the error so the caller knows it failed
  } finally {
    options?.onLoadingChange?.(false);
  }
}

// --- Serialization/Deserialization (remains unchanged) ---
export function serializeError(error: AppError) {
  // Note: Error stack is not typically serialized for client exposure
  return {
    name: error.name, // Include name for potential client-side checks
    code: error.code,
    message: error.message,
    statusCode: error.statusCode,
    context: error.context, // Be mindful of what context is sensitive
  };
}

export function deserializeError(
  serialized: ReturnType<typeof serializeError>
): AppError {
  // Reconstruct the AppError on the client
  const error = new AppError(
    serialized.message,
    serialized.code,
    serialized.statusCode,
    serialized.context
  );
  // Restore the name as it might be lost during serialization
  error.name = serialized.name || 'AppError';
  return error;
}
