import { AppError, ErrorCodes, HttpStatusCode } from '.';

export class ApiResponse {
  static success<T>(data: T, status: HttpStatusCode = HttpStatusCode.OK) {
    return Response.json({ data, success: true }, { status });
  }

  static error(error: unknown) {
    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          error: {
            code: error.code,
            message: error.message,
            statusCode: error.statusCode,
            context: error.context || undefined,
          },
        },
        { status: error.statusCode }
      );
    }

    // For unknown errors, create a standard server error
    const serverError = new AppError(
      'Internal Server Error',
      ErrorCodes.SERVER_ERROR,
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      { originalError: error } // Include the original error in context
    );

    return Response.json(
      {
        success: false,
        error: {
          code: serverError.code,
          message: serverError.message,
          statusCode: serverError.statusCode,
          context: serverError.context,
        },
      },
      { status: serverError.statusCode }
    );
  }

  // Optional: Add a method for created resources
  static created<T>(data: T) {
    return this.success(data, HttpStatusCode.CREATED);
  }
}
