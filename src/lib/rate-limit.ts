import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv'; // Vercel's Key-Value storage for Redis

import { Errors } from './error-utils';

// Define rate limiters for different use cases
// Each limiter specifies:
// - How many requests are allowed
// - Over what time period
// - Using which Redis instance
const limiters = {
  // Default rate limit: 50 requests per day per identifier
  default: new Ratelimit({
    redis: kv,
    limiter: Ratelimit.slidingWindow(50, '1 d'),
    analytics: true, // Enable analytics for monitoring
  }),

  // Stricter limit for authentication: 5 requests per 5 minutes
  // Helps prevent brute force attacks
  auth: new Ratelimit({
    redis: kv,
    limiter: Ratelimit.slidingWindow(5, '5 m'),
    analytics: true,
  }),

  // API rate limit: 100 requests per hour
  // Suitable for general API endpoints
  api: new Ratelimit({
    redis: kv,
    limiter: Ratelimit.slidingWindow(100, '1 h'),
    analytics: true,
  }),
};

/**
 * Checks if a request should be rate limited
 * @param identifier - Unique identifier for the client (usually IP address)
 * @param type - Type of rate limit to apply (default, auth, or api)
 * @returns Object containing limit details (limit, remaining, reset)
 * @throws RateLimitExceeded error when limit is exceeded
 */
export async function rateLimit(
  identifier: string,
  type: keyof typeof limiters = 'default'
) {
  // Attempt to record the request against the rate limit
  const { success, limit, reset, remaining } = await limiters[type].limit(
    `${type}_${identifier}` // Create unique key combining type and identifier
  );

  // If rate limit is exceeded, throw an error with limit details
  if (!success) {
    throw Errors.RateLimitExceeded({
      limit, // Maximum number of requests allowed
      reset, // Timestamp when the limit will reset
      remaining, // Number of requests remaining in current window
    });
  }

  // Return limit details if successful
  return { limit, remaining, reset };
}
