export const NOTIFICATION_TYPES: Record<string, string> = {
  LIKE_COMMENT: 'liked your comment',
  LIKE_POST: 'liked your post',
  COMMENT: 'commented on your post',
  REPL<PERSON>: 'replied to your comment',
  SUBSCRIPTION_PURCHASE: 'purchased a subscription',
  ANNOUNCEMENT: 'New announcement',
};

export const getNotificationContent = (
  type: keyof typeof NOTIFICATION_TYPES,
  additionalInfo?: string
) => {
  const baseContent = NOTIFICATION_TYPES[type];
  if (!additionalInfo) return baseContent;

  switch (type) {
    case 'COMMENT':
    case 'REPLY':
    case 'ANNOUNCEMENT':
    case 'SUBSCRIPTION_PURCHASE':
      return `${baseContent}: "${additionalInfo}"`;
    default:
      return baseContent;
  }
};
