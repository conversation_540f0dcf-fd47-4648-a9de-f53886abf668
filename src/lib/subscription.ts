import { getPlans } from '@/actions/plans';
import { auth } from '@/auth';
import { FREE_PLAN } from '@/constants';

import { db } from '@/lib/db';
import { stripe } from '@/lib/stripe';

export async function getUserSubscriptions() {
  const session = await auth();

  if (!session?.user || !session.user.email) {
    throw new Error('Not authenticated');
  }

  // Get all plans
  const plansResult = await getPlans();

  if (!plansResult.items.length) {
    return FREE_PLAN;
  }

  const plans = plansResult.items;

  const user = await db.user.findFirst({
    where: {
      id: session.user.id,
    },
    select: {
      stripeCustomerId: true,
      stripeSubscriptionId: true,
      stripePriceId: true,
      stripeCurrentPeriodEnd: true,
    },
  });

  if (!user) throw new Error('User not found');

  // Check if user is on a pro plan.
  const isSubscribed =
    user?.stripePriceId &&
    user?.stripeCurrentPeriodEnd &&
    user?.stripeCurrentPeriodEnd?.getTime() + 86_400_000 > Date.now();

  // find the current plan, if not found use the free plan
  const plan =
    plans.find(
      (plan) =>
        plan.stripePriceIdMonthly === user.stripePriceId ||
        plan.stripePriceIdYearly === user.stripePriceId
    ) || FREE_PLAN;

  let isCanceled = false;

  if (isSubscribed && user.stripeSubscriptionId) {
    const subscription = await stripe.subscriptions?.retrieve(
      user.stripeSubscriptionId
    );

    isCanceled = subscription.cancel_at_period_end;
  }

  return {
    ...plan,
    ...user,
    stripeCurrentPeriodEnd: user.stripeCurrentPeriodEnd,
    isSubscribed,
    isCanceled,
  };
}
