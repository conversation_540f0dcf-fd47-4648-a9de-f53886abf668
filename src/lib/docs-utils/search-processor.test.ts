import { findRelevantExcerpt } from './search-processor';

describe('search-processor', () => {
  describe('findRelevantExcerpt', () => {
    const sampleContent =
      'This is a long piece of content that contains multiple sentences. ' +
      'We want to test how the excerpt function works with different search queries. ' +
      'The function should find relevant parts of the text and highlight matching words.';

    it('should return first N characters when no match is found', () => {
      const result = findRelevantExcerpt(sampleContent, 'xyz', 20);
      expect(result).toBe('This is a long piece...');
    });

    it('should find and highlight single word matches', () => {
      const result = findRelevantExcerpt(sampleContent, 'test', 50);
      expect(result).toContain('<mark>test</mark>');
      // Check for surrounding context without the mark tags
      expect(result).toContain('want to');
      expect(result).toContain('how the excerpt');
    });

    it('should handle multiple word queries', () => {
      const result = findRelevantExcerpt(sampleContent, 'function works', 60);
      expect(result).toContain('<mark>function</mark>');
      expect(result).toContain('<mark>works</mark>');
    });

    it('should be case insensitive', () => {
      const result = findRelevantExcerpt(sampleContent, 'CONTENT', 40);
      expect(result).toContain('<mark>content</mark>');
    });

    it('should respect word boundaries', () => {
      const content = 'testing test tester';
      const result = findRelevantExcerpt(content, 'test', 20);
      expect(result).toMatch(/(testing|tester)/); // Should not highlight parts of these words
      expect(result).toContain('<mark>test</mark>'); // Should highlight exact word
    });

    it('should handle content shorter than requested length', () => {
      const shortContent = 'Short text';
      const result = findRelevantExcerpt(shortContent, 'text', 100);
      expect(result).toBe('Short <mark>text</mark>');
    });

    it('should add ellipsis appropriately', () => {
      // Test start ellipsis
      const result1 = findRelevantExcerpt(sampleContent, 'sentences', 20);
      expect(result1.startsWith('...')).toBe(true);

      // Test end ellipsis
      const result2 = findRelevantExcerpt(sampleContent, 'This', 20);
      expect(result2.endsWith('...')).toBe(true);

      // Test both ellipses for middle content
      const result3 = findRelevantExcerpt(sampleContent, 'excerpt', 20);
      expect(result3.startsWith('...')).toBe(true);
      expect(result3.endsWith('...')).toBe(true);
    });

    it('should use default length when not specified', () => {
      const result = findRelevantExcerpt(sampleContent, 'test');
      expect(result.length).toBeLessThan(sampleContent.length);
      expect(result.length).toBeGreaterThan(100); // Default is 150, so should be more than 100
    });
  });
});
