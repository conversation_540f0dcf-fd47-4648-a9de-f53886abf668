import fs from 'fs';
import path from 'path';
import { notFound } from 'next/navigation';
import matter from 'gray-matter';

import { capitalize } from '@/lib/utils';

const DOCS_DIRECTORY = path.join(process.cwd(), 'src/content/docs');
const DEFAULT_POSITION = 9999;

export interface SidebarItem {
  type: 'file' | 'folder';
  slug: string[];
  displayName: string;
  position: number;
  children?: SidebarItem[];
}

interface FolderConfig {
  displayName?: string;
  position?: number;
}

interface Doc {
  title: string;
  content: string;
  path: string;
}

function traverseDirectory(
  directory: string,
  callback: (filePath: string) => void
) {
  const files = fs.readdirSync(directory);
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stat = fs.statSync(filePath);
    if (stat.isDirectory()) {
      traverseDirectory(filePath, callback);
    } else if (path.extname(file) === '.mdx') {
      callback(filePath);
    }
  }
}

export async function getDocsStaticParams(docsPath: string) {
  const paths = [{ slug: [] as string[] }];
  traverseDirectory(path.join(process.cwd(), docsPath), (filePath) => {
    const relativePath = path.relative(DOCS_DIRECTORY, filePath);
    paths.push({
      slug: relativePath.replace(/\.mdx$/, '').split(path.sep),
    });
  });
  return paths;
}

export async function getDocBySlug(slug: string[]) {
  const fullPath =
    slug.length === 0
      ? path.join(DOCS_DIRECTORY, 'index.mdx')
      : path.join(DOCS_DIRECTORY, ...slug) + '.mdx';

  if (!fs.existsSync(fullPath)) {
    return notFound();
  }

  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { content, data } = matter(fileContents);
    return { content, data };
  } catch (error) {
    console.error(`Error reading file: ${fullPath}`, error);
    return notFound();
  }
}

function getFolderConfig(folderPath: string): FolderConfig {
  const configPath = path.join(folderPath, '_sidebar.json');
  try {
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configContent);
    }
  } catch (error) {
    console.error(`Error reading folder config: ${configPath}`, error);
  }
  return {};
}

function createSidebarItem(
  file: string,
  currentPath: string,
  currentSlug: string[]
): SidebarItem {
  const filePath = path.join(currentPath, file);
  const stat = fs.statSync(filePath);
  const slug = [...currentSlug, file];

  if (stat.isDirectory()) {
    const children = generateSidebarItems(filePath, slug);
    const folderConfig = getFolderConfig(filePath);
    return {
      type: 'folder',
      slug,
      displayName: folderConfig.displayName || capitalize(file),
      position: folderConfig.position || DEFAULT_POSITION,
      children,
    };
  } else {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    const { data } = matter(fileContents);
    return {
      type: 'file',
      slug: slug.map((s) => s.replace('.mdx', '')),
      displayName: data.title || capitalize(file.replace('.mdx', '')),
      position: data.position || DEFAULT_POSITION,
    };
  }
}

function generateSidebarItems(
  currentPath: string,
  currentSlug: string[] = []
): SidebarItem[] {
  const files = fs.readdirSync(currentPath);
  const items = files
    .filter((file) => file !== '_sidebar.json' && file !== 'index.mdx')
    .map((file) => createSidebarItem(file, currentPath, currentSlug));
  return items.sort((a, b) => a.position - b.position);
}

export function generateSidebar(): SidebarItem[] {
  return generateSidebarItems(DOCS_DIRECTORY);
}

export async function getDocs() {
  const documents: Doc[] = [];

  traverseDirectory(DOCS_DIRECTORY, (filePath) => {
    try {
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const { content, data } = matter(fileContents);
      const slug = filePath
        .replace(DOCS_DIRECTORY, '')
        .replace(/\.mdx$/, '')
        .split(path.sep)
        .filter(Boolean);

      documents.push({
        title: data.title || slug.join(' '),
        content,
        path: `/docs/${slug.join('/')}`,
      });
    } catch (error) {
      console.error(`Error processing file: ${filePath}`, error);
    }
  });

  return documents;
}

// Updated function to get navigation
export async function getNavigation(currentSlug: string[]) {
  const sidebar = generateSidebar();
  const flatSidebar = flattenSidebar(sidebar);
  const currentIndex =
    currentSlug.length === 0
      ? -1
      : flatSidebar.findIndex(
          (item) => item.slug.join('/') === currentSlug.join('/')
        );

  const getHref = (item: SidebarItem) => {
    const slugPath = item.slug.join('/');
    if (slugPath === 'index') {
      return '/docs';
    }
    return `/docs/${item.slug.join('/')}`;
  };

  const findNextValidItem = (startIndex: number): SidebarItem | undefined => {
    for (let i = startIndex + 1; i < flatSidebar.length; i++) {
      const item = flatSidebar[i];
      if (
        item.type === 'file' ||
        (item.type === 'folder' && item.children && item.children.length > 0)
      ) {
        return item;
      }
    }

    return undefined;
  };

  const prevPage =
    currentIndex > 0
      ? {
          href: getHref(flatSidebar[currentIndex - 1]),
          label: flatSidebar[currentIndex - 1].displayName,
        }
      : undefined;

  const nextItem =
    currentIndex === -1 ? flatSidebar[1] : findNextValidItem(currentIndex);
  const nextPage = nextItem
    ? {
        href: getHref(nextItem),
        label: nextItem.displayName,
      }
    : undefined;

  return { prevPage, nextPage };
}

// Helper function to flatten the sidebar
function flattenSidebar(sidebar: SidebarItem[]): SidebarItem[] {
  return sidebar.reduce((acc: SidebarItem[], item) => {
    if (item.type === 'file') {
      acc.push(item);
    }
    if (item.type === 'folder' && item.children) {
      acc.push(...flattenSidebar(item.children));
    }
    return acc;
  }, []);
}
