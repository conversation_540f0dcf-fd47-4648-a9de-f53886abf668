import { remark } from 'remark';
import mdxToPlainText from 'remark-mdx-to-plain-text';

export interface SearchItem {
  slug: string;
  title: string;
  url: string;
  content: string;
}

export type SearchResultType = {
  id: string;
  title: string;
  url: string;
  content: string;
  excerpt: string;
};

export async function fromMDXToPlainText(mdx: string) {
  const plainTextContent = await remark().use(mdxToPlainText).process(mdx);
  const plainText = String(plainTextContent);
  return plainText;
}

export function findRelevantExcerpt(
  content: string,
  query: string,
  length: number = 150
): string {
  const lowerContent = content.toLowerCase();

  const words = query.split(/\s+/);

  // Find the position of the first matching word
  let startIndex = -1;
  for (const word of words) {
    const index = lowerContent.indexOf(word.toLowerCase());
    if (index !== -1) {
      startIndex = index;
      break;
    }
  }

  if (startIndex === -1) {
    // If no match is found, return the first `length` characters
    return content.substring(0, length) + '...';
  }

  // Calculate start and end indices for the excerpt
  let start = Math.max(0, startIndex - Math.floor(length / 2));
  let end = Math.min(content.length, start + length);

  // Adjust start if end is at the content length
  if (end === content.length) {
    start = Math.max(0, end - length);
  }

  // Trim the excerpt to complete words
  while (start > 0 && content[start - 1] !== ' ') {
    start--;
  }
  while (end < content.length && content[end] !== ' ') {
    end++;
  }

  let excerpt = content.substring(start, end);

  // Highlight matching words
  for (const word of words) {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    excerpt = excerpt.replace(regex, (match) => `<mark>${match}</mark>`);
  }

  return (
    (start > 0 ? '...' : '') + excerpt + (end < content.length ? '...' : '')
  );
}
