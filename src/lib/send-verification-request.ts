import { EmailConfig } from 'next-auth/providers/email';

const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'https://saastarter.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';

export interface Theme {
  colorScheme?: 'auto' | 'dark' | 'light';
  logo?: string;
  brandColor?: string;
  buttonText?: string;
}
type SendVerificationRequestParams = {
  identifier: string;
  url: string;
  expires: Date;
  provider: EmailConfig;
  token: string;
  theme: Theme;
  request: Request;
};

function html({ to, url }: { to: string; url: string }) {
  return `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Sign in to your account</title>
          </head>
          <body style="background-color: #f1f5f9; margin: auto; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Oxygen-Sans,Ubuntu,Cantarell,'Helvetica Neue',sans-serif; padding: 8px;">
            <div style="border: 1px solid #eaeaea; border-radius: 4px; margin: 40px auto; padding: 20px; max-width: 465px; background-color: white;">
              <div style="margin-top: 32px; text-align: center;">
                <img src="${logoUrl}" height="20" alt="Saastarter Logo" style="margin: 0 auto;" />
              </div>

              <h1 style="color: black; font-size: 24px; font-weight: normal; text-align: center; padding: 0; margin: 30px 0;">
                Sign in to <strong>Your Account</strong>
              </h1>

              <p style="color: black; font-size: 14px; line-height: 24px;">
                Hello ${to},
              </p>

              <p style="color: black; font-size: 14px; line-height: 24px;">
                You requested a magic link to sign in to your account. Click the button below to sign in:
              </p>

              <div style="text-align: center; margin: 32px 0;">
                <a href="${url}" style="background-color: #000000; border-radius: 4px; color: white; font-size: 12px; font-weight: 600; text-decoration: none; text-align: center; padding: 15px 20px;">
                  Sign In
                </a>
              </div>

              <hr style="border: none; border-top: 1px solid #eaeaea; margin: 26px 0;" />

              <p style="color: #666666; font-size: 12px; line-height: 24px;">
                This magic link was intended for <span style="color: black;">${to}</span>. If you didn't request this link, you can safely ignore this email. The link will expire in 24 hours for security reasons.
              </p>

              <p style="color: #666666; font-size: 12px; line-height: 24px;">
                If you have any questions, please <a href="${baseUrl}/contact" style="color: #2563eb; text-decoration: none;">contact our support team</a>.
              </p>
            </div>
          </body>
        </html>
      `;
}

export async function sendVerificationRequest(
  params: SendVerificationRequestParams
) {
  const { identifier: to, provider, url } = params;
  const { host } = new URL(url);

  // In development mode, only log the magic link
  if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Magic Link for Development ===');
    console.log('Email:', to);
    console.log('URL:', url);
    console.log('===============================\n');
    return;
  }

  // In production, send the actual email
  const res = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${provider.apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: provider.from,
      to,
      subject: `Sign in to ${host}`,
      html: html({ to, url }),
    }),
  });

  if (!res.ok) {
    throw new Error('Resend error: ' + JSON.stringify(await res.json()));
  }
}
