export type DefaultAvatarType = 'man' | 'woman' | 'boy' | 'girl';

const avatarTypes: DefaultAvatarType[] = ['man', 'woman', 'boy', 'girl'];

export function getDefaultAvatar(type: DefaultAvatarType = 'man'): string {
  return `/default-avatars/${type}.svg`;
}

export function getDeterministicAvatar(seed: string): string {
  // Use the seed to consistently select the same avatar type
  const hash = seed.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);

  const index = Math.abs(hash) % avatarTypes.length;
  return getDefaultAvatar(avatarTypes[index]);
}
