import { act } from '@testing-library/react';

// Wait for promises to resolve
export const waitForPromises = () => act(() => Promise.resolve());

// Helper to wait for a specific time
export const wait = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Helper to match snapshot with dynamic data
export const snapshotWithoutDynamicData = (html: string) => {
  return html
    .replace(/data-testid="[^"]*"/g, '')
    .replace(/class="[^"]*"/g, '')
    .replace(/style="[^"]*"/g, '')
    .replace(/id="[^"]*"/g, '')
    .replace(/\s+/g, ' ')
    .trim();
};
