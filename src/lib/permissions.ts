import { Role, Tier } from '@prisma/client';

import { SessionUser } from '@/types/user.types';
import {
  Action,
  Permission,
  Resource,
  rolePermissions,
  tierFeatures,
} from '@/config/permissions.config';
import { db } from '@/lib/db';

/**
 * Parameters required for checking permissions
 * @example
 * const check = {
 *   user: currentUser,
 *   resource: 'post',
 *   action: 'create',
 *   requiredPlan: Tier.PRO
 * };
 */
export type PermissionCheck = {
  user: SessionUser | null;
  resource: Resource; // The resource being accessed
  action: Action; // The action being performed
  requiredPlan?: Tier; // Required subscription tier
};

/**
 * Service for handling permission checks throughout the application
 * Implements the Singleton pattern to ensure only one instance exists
 *
 * @example Usage in an API route:
 * ```typescript
 * const canCreatePost = await permissionService.can({
 *   user: session.user,
 *   resource: 'post',
 *   action: 'create',
 *   requiredPlan: Tier.PRO
 * });
 *
 * if (!canCreatePost) {
 *   throw new Error('Unauthorized');
 * }
 * ```
 *
 * @example Usage with ownership check:
 * ```typescript
 * const canEditComment = await permissionService.can({
 *   user: session.user,
 *   resource: 'comment',
 *   action: 'update',
 * });
 * ```
 *
 * @example Multiple permission checks:
 * ```typescript
 * const canPerformActions = await permissionService.checkMultiple([
 *   {
 *     user: session.user,
 *     resource: 'post',
 *     action: 'create'
 *   },
 *   {
 *     user: session.user,
 *     resource: 'comment',
 *     action: 'delete',
 *   }
 * ]);
 * ```
 */
export class PermissionService {
  private static instance: PermissionService;
  private constructor() {}

  /**
   * Gets the singleton instance of PermissionService
   */
  public static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }

  /**
   * Checks if a user has permission to perform an action on a resource
   * Validates role-based permissions, ownership, and subscription tier
   */
  async can(params: PermissionCheck): Promise<boolean> {
    const { user, resource, action, requiredPlan } = params;

    // Anonymous users have no permissions
    if (!user) return false;

    // Check role-based permissions
    const permission = `${resource}:${action}` as Permission;
    const userPermissions = rolePermissions[user.role as Role];

    if (!userPermissions) return false;

    const hasPermission = this.checkPermission(userPermissions, permission);
    if (!hasPermission) return false;

    // Check subscription tier if required
    if (requiredPlan) {
      const hasSubscription = await this.checkSubscriptionTier(
        user,
        requiredPlan
      );
      if (!hasSubscription) return false;
    }

    return true;
  }

  /**
   * Checks if a specific permission is included in the user's permissions
   * Handles both direct matches and wildcard permissions
   */
  private checkPermission(
    userPermissions: Permission[],
    permission: Permission
  ): boolean {
    // Wildcard permission grants access to everything
    if (userPermissions.includes('*')) return true;

    // Check for exact match or resource-level wildcard
    return userPermissions.some(
      (p) =>
        p === permission ||
        p === (`${permission.split(':')[0]}:*` as Permission)
    );
  }

  /**
   * Determines if a resource requires ownership validation
   * Only certain resources need ownership checks
   */
  private requiresOwnershipCheck(resource: Resource): boolean {
    return [
      'post',
      'comment',
      'like',
      'bookmark',
      'apiKey',
      'tenant',
      'organization',
    ].includes(resource);
  }

  /**
   * Validates if a user's subscription tier meets the required level
   * Checks both tier hierarchy and specific feature limits
   */
  private async checkSubscriptionTier(
    user: SessionUser,
    requiredTier: Tier
  ): Promise<boolean> {
    try {
      const tierHierarchy = [Tier.STARTER, Tier.PRO, Tier.ENTERPRISE];

      // Get user's current plan
      const plan = await db.plan.findFirst({
        where: {
          OR: [
            { stripePriceIdMonthly: user.stripePriceId },
            { stripePriceIdYearly: user.stripePriceId },
          ],
        },
        select: {
          tier: true,
        },
      });

      const currentTier = plan?.tier || Tier.STARTER;

      // Check tier-specific features
      const currentFeatures = tierFeatures[currentTier];
      if (!currentFeatures) return false;

      // Check tenant limits
      const tenantCount = await db.tenant
        .count({
          where: {
            ownerId: user.id,
          },
        })
        .catch(() => 0); // Handle tenant count errors gracefully

      const maxTenantsFeature = currentFeatures.find((f) =>
        f.startsWith('max_tenants:')
      );

      if (maxTenantsFeature) {
        const maxTenants = parseInt(maxTenantsFeature.split(':')[1]);
        if (tenantCount >= maxTenants) {
          return false;
        }
      }

      // Check tier hierarchy
      const requiredTierIndex = tierHierarchy.indexOf(requiredTier);
      const currentTierIndex = tierHierarchy.indexOf(currentTier);

      return currentTierIndex >= requiredTierIndex;
    } catch (error) {
      // Log error for monitoring but don't expose to caller
      console.error('Error checking subscription tier:', error);
      return false;
    }
  }

  /**
   * Checks multiple permissions at once
   * All checks must pass for the result to be true
   */
  async checkMultiple(checks: PermissionCheck[]): Promise<boolean> {
    const results = await Promise.all(checks.map((check) => this.can(check)));
    return results.every((result) => result);
  }
}

// Export singleton instance
export const permissionService = PermissionService.getInstance();

/**
 * Example usage in components/actions:
 *
 * ```typescript
 * // Check if user can create a post
 * const canCreate = await permissionService.can({
 *   user: session.user,
 *   resource: 'post',
 *   action: 'create'
 * });
 *
 * // Check if user can edit a specific comment
 * const canEdit = await permissionService.can({
 *   user: session.user,
 *   resource: 'comment',
 *   action: 'update',
 * });
 *
 * // Check if user can access premium features
 * const canAccessPremium = await permissionService.can({
 *   user: session.user,
 *   resource: 'feature',
 *   action: 'access',
 *   requiredPlan: Tier.PRO
 * });
 *
 * // Check multiple permissions at once
 * const canPerformAll = await permissionService.checkMultiple([
 *   {
 *     user: session.user,
 *     resource: 'post',
 *     action: 'create'
 *   },
 *   {
 *     user: session.user,
 *     resource: 'comment',
 *     action: 'delete',
 *   }
 * ]);
 * ```
 */
