export const saveFileToLocalStorage = async (
  key: string = 'cover-image',
  file: File
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onloadend = () => {
      try {
        if (!reader.result) {
          reject(new Error('Failed to read file'));
          return;
        }
        const base64String = reader.result as string;
        window.localStorage.setItem(key, base64String);
        resolve();
      } catch (error) {
        console.error('Failed to save to localStorage:', error);
        reject(new Error('Failed to save to localStorage'));
      }
    };

    reader.onerror = () => {
      console.error('Failed to read file:', reader.error);
      reject(new Error('Failed to read file'));
    };

    try {
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Failed to start file reading:', error);
      reject(new Error('Failed to read file'));
    }
  });
};

export const getFileFromLocalStorage = (
  key: string = 'cover-image'
): File | null => {
  try {
    const base64String = window.localStorage.getItem(key);
    if (!base64String) return null;

    // Validate data URL format
    if (!base64String.startsWith('data:')) return null;

    const [header, base64Data] = base64String.split(',');
    if (!header || !base64Data) return null;

    const mimeString = header.split(':')[1]?.split(';')[0];
    if (!mimeString) return null;

    // Validate base64 string before attempting to decode
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)) {
      return null;
    }

    try {
      const byteString = atob(base64Data);
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      return new File([ab], 'coverImage', { type: mimeString });
    } catch (error) {
      console.error('Failed to decode base64 data:', error);
      return null;
    }
  } catch (error) {
    console.error('Failed to get file from localStorage:', error);
    return null;
  }
};

export const saveToLocalStorage = (key: string, value: string): void => {
  try {
    window.localStorage.setItem(key, value);
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
    // Don't throw, just log the error as specified in the test
  }
};

export const getFromLocalStorage = (key: string): string | null => {
  try {
    return window.localStorage.getItem(key);
  } catch (error) {
    console.error('Failed to get from localStorage:', error);
    return null;
  }
};

export const removeFromLocalStorage = (key: string): void => {
  try {
    window.localStorage.removeItem(key);
  } catch (error) {
    console.error('Failed to remove from localStorage:', error);
  }
};
