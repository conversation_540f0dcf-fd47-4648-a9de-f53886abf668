import sharp from 'sharp';

export const compressImage = async (
  file: File,
  sizelimit: number,
  quality: number = 90
) => {
  let compressedBuffer: Buffer;

  if (file instanceof Blob) {
    const arrayBuffer = await file.arrayBuffer();
    compressedBuffer = await sharp(Buffer.from(arrayBuffer))
      // maintain aspect ratio
      .resize(sizelimit, null, { fit: 'inside', withoutEnlargement: true })
      // lower quality to 90%
      .jpeg({ quality: quality, progressive: true })
      .toBuffer();
  } else {
    compressedBuffer = await sharp(file)
      .resize(sizelimit, null, { fit: 'inside', withoutEnlargement: true })
      .jpeg({ quality: quality, progressive: true })
      .toBuffer();
  }

  return compressedBuffer;
};
