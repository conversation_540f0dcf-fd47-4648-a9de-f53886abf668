export function fileToUrl(file: File) {
  return URL.createObjectURL(file);
}

export async function urlToFile(url: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  return new File([blob], url.split('/').pop() || 'image.jpg', {
    type: blob.type,
  });
}

export function fileToBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function base64ToFile(base64: string, filename: string) {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new File([byteArray], filename, { type: 'image/png' });
}

export function fileToBlob(file: File) {
  return new Blob([file], { type: file.type });
}

export function blobToFile(blob: Blob, filename: string) {
  return new File([blob], filename, { type: blob.type });
}

export function blobToUrl(blob: Blob) {
  return URL.createObjectURL(blob);
}

export async function urlToBlob(url: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  return blob;
}

export function blobToBase64(blob: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function base64ToBlob(base64: string) {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: 'image/png' });
}

export function blobToDataUrl(blob: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function fileToBuffer(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function bufferToFile(buffer: ArrayBuffer, filename: string) {
  return new File([buffer], filename, { type: 'image/png' });
}

export function fileToFormData(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return formData;
}

export function formDataToUrl(formData: FormData) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '/upload');
    xhr.onload = () => resolve(xhr.response);
    xhr.onerror = (error) => reject(error);
    xhr.send(formData);
  });
}
