import hljs from 'highlight.js';

//Apply Codeblock Highlighting on the HTML from editor.getHTML()
export const highlightCodeblocks = (content: string) => {
  const doc = new DOMParser().parseFromString(content, 'text/html');
  doc.querySelectorAll('pre code').forEach((el) => {
    // https://highlightjs.readthedocs.io/en/latest/api.html?highlight=highlightElement#highlightelement
    hljs.highlightElement(el as HTMLElement);
  });
  return new XMLSerializer().serializeToString(doc);
};
