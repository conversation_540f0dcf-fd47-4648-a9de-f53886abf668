import {
  DeleteObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { fromEnv } from '@aws-sdk/credential-providers';

const client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: fromEnv(),
});

const bucketName = process.env.AWS_BUCKET_NAME;
const region = process.env.AWS_REGION;
const uploadPath = 'uploads';

export {
  client,
  DeleteObjectCommand,
  PutObjectCommand,
  bucketName,
  region,
  uploadPath,
};
