import '@testing-library/jest-dom';

// Mock Prisma Client with comprehensive models based on schema
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    tenant: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    settings: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    post: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    subscription: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    invite: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    // Add transaction and other utility methods
    $transaction: jest.fn((callback) => callback()),
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  })),
  Role: {
    OWNER: 'OWNER',
    ADMIN: 'ADMIN',
    COLLABORATOR: 'COLLABORATOR',
    USER: 'USER',
  },
  Tier: {
    FREE: 'FREE',
    STARTER: 'STARTER',
    PRO: 'PRO',
    ENTERPRISE: 'ENTERPRISE',
  },
}));

// Enhanced fetch mock with more flexibility
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    statusText: 'OK',
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    headers: new Headers(),
    clone: () => Promise.resolve({}),
  })
) as jest.Mock;

// Mock Stripe with comprehensive API surface
jest.mock('@/lib/stripe', () => ({
  stripe: {
    subscriptions: {
      retrieve: jest.fn().mockResolvedValue({
        id: 'sub_mock',
        status: 'active',
        cancel_at_period_end: false,
        current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
        items: {
          data: [
            {
              price: { id: 'price_mock' },
            },
          ],
        },
      }),
      create: jest.fn(),
      update: jest.fn(),
      del: jest.fn(),
    },
    customers: {
      retrieve: jest.fn().mockResolvedValue({
        id: 'cus_mock',
        email: '<EMAIL>',
        metadata: {},
      }),
      create: jest.fn(),
      update: jest.fn(),
    },
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    prices: {
      retrieve: jest.fn(),
      list: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  },
}));

// Mock next-auth
jest.mock('next-auth', () => ({
  auth: jest.fn().mockResolvedValue({
    user: {
      id: 'user_mock',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'USER',
    },
  }),
  getServerSession: jest.fn(),
}));

// Enhanced next-auth/react mock
jest.mock('next-auth/react', () => ({
  signIn: jest.fn().mockResolvedValue({ ok: true, error: null }),
  signOut: jest.fn().mockResolvedValue({ ok: true }),
  useSession: jest.fn(() => ({
    data: {
      user: {
        id: 'user_mock',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'USER',
      },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    },
    status: 'authenticated',
    update: jest.fn(),
  })),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// next/navigation mock
jest.mock('next/navigation', () => ({
  useRouter: jest.fn().mockReturnValue({
    push: jest.fn().mockResolvedValue(true),
    replace: jest.fn().mockResolvedValue(true),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
    pathname: '/',
    query: {},
  }),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
  usePathname: jest.fn().mockReturnValue('/'),
  useParams: jest.fn().mockReturnValue({}),
  redirect: jest.fn(),
  notFound: jest.fn(),
}));

// next-intl mock
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string, params?: Record<string, any>) => {
    // Simple interpolation support
    if (params) {
      let text = key;
      Object.entries(params).forEach(([k, v]) => {
        text = text.replace(`{${k}}`, String(v));
      });
      return text;
    }
    return key;
  },
  useLocale: () => 'en',
  useTimeZone: () => 'UTC',
  NextIntlClientProvider: ({ children }: { children: React.ReactNode }) =>
    children,
}));

// next-themes mock
jest.mock('next-themes', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
    themes: ['light', 'dark', 'system'],
    systemTheme: 'light',
  }),
}));

// Mock remark and related packages
jest.mock('remark', () => ({
  remark: () => ({
    use: jest.fn().mockReturnThis(),
    process: jest.fn().mockResolvedValue({
      toString: () => 'mocked plain text',
      value: 'mocked plain text',
    }),
  }),
}));

jest.mock('remark-mdx-to-plain-text', () => jest.fn());

// Mock ResizeObserver
class MockResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

global.ResizeObserver = MockResizeObserver;

// Mock Intersection Observer
global.IntersectionObserver = class MockIntersectionObserver
  implements IntersectionObserver
{
  readonly root: Element | null = null;
  readonly rootMargin: string = '0px';
  readonly thresholds: ReadonlyArray<number> = [0];

  constructor(
    private callback: IntersectionObserverCallback,
    private options?: IntersectionObserverInit
  ) {}

  observe(): void {}
  unobserve(): void {}
  disconnect(): void {}
  takeRecords(): IntersectionObserverEntry[] {
    return [];
  }
} as any;

// Console error/warning handler
const CONSOLE_FAIL_TYPES: Array<'error' | 'warn'> = ['error', 'warn'];

const originalConsole = {
  error: console.error,
  warn: console.warn,
};

CONSOLE_FAIL_TYPES.forEach((type) => {
  console[type] = (message, ...args) => {
    if (
      /Warning.*not wrapped in act/i.test(message) ||
      /Warning.*ReactDOM.render is no longer supported/i.test(message) ||
      /Error.*failed/i.test(message) ||
      /Error occurred:/i.test(message) ||
      /Error: Not implemented:/i.test(message) ||
      (typeof message === 'object' && message?.type === 'not implemented') ||
      /Warning: ReactDOM.render is no longer supported in React 18/i.test(
        message
      ) ||
      /Warning: An update to .* inside a test was not wrapped in act/i.test(
        message
      )
    ) {
      return;
    }
    originalConsole[type](message, ...args);
  };
});

// Add missing window properties
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
window.scrollTo = jest.fn();

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});
