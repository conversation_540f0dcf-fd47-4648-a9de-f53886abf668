import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma, PrismaClient } from '@prisma/client';

import { Order } from '@/types/shared.types';

type PrismaModels = keyof PrismaClient;

export type PaginationParams<T extends PrismaModels> = {
  cursor?: string;
  take?: number;
  skip?: number;
  page?: number;
  orderBy?: Prisma.Args<PrismaClient[T], 'findMany'>['orderBy'];
  where?: Prisma.Args<PrismaClient[T], 'findMany'>['where'];
  include?: Prisma.Args<PrismaClient[T], 'findMany'>['include'];
};

export async function cursorPagination<T extends PrismaModels>(
  prisma: PrismaClient,
  model: T,
  params: PaginationParams<T>
) {
  const { cursor, take = 10, skip = 0, orderBy, where, include } = params;

  const items = await (prisma[model] as any).findMany({
    take,
    skip,
    cursor: cursor ? { id: cursor } : undefined,
    orderBy,
    where,
    include,
  });

  const nextCursor = items.length > 0 ? items[items.length - 1].id : null;
  const totalCount = await (prisma[model] as any).count({ where });

  return {
    items,
    nextCursor,
    totalCount,
    hasMore: items.length === take,
  };
}

export type PagePaginationParams<T extends PrismaModels> = Omit<
  PaginationParams<T>,
  'cursor'
> & {
  page?: number;
};

export async function pagePagination<T extends PrismaModels>(
  prisma: PrismaClient,
  model: T,
  params: PagePaginationParams<T>
) {
  const {
    page = 1,
    take = ITEMS_PER_PAGE,
    orderBy = {},
    where,
    include,
  } = params;

  const items = await (prisma[model] as any).findMany({
    take,
    skip: (page - 1) * take,
    orderBy,
    where,
    include,
  });

  const totalCount = await (prisma[model] as any).count({ where });
  const totalPages = Math.ceil(totalCount / take);
  const [key, value] = Object.entries(orderBy)[0] ?? [null, null];

  return {
    items,
    page,
    totalPages,
    totalCount,
    orderBy: key,
    order: value as Order,
  };
}

export function calculateNewPage(
  currentPage: number,
  totalItems: number,
  itemsPerPage: number,
  isAdding: boolean = false
): number {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (isAdding) {
    // If adding and the current page is full, go to the next page
    return totalPages;
  }

  // If deleting and the current page becomes empty, go to the previous page
  return Math.min(currentPage, Math.max(1, totalPages));
}

// Math.min(1, Math.max(1, 1)) = 1
