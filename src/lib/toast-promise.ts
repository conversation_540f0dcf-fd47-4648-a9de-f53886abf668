import { toast } from 'sonner';

export function toastPromise<T>(
  promise: Promise<T>,
  {
    loading = 'Loading...',
    success = 'Operation successful',
    error = 'An error occurred',
  }: {
    loading?: string;
    success?: string;
    error?: string | ((err: Error) => string);
  }
) {
  toast.promise(promise, {
    loading,
    success,
    error: (err) => (typeof error === 'function' ? error(err) : error),
  });
}
