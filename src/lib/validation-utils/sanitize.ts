import DOMPurify, { Config } from 'dompurify';
import { J<PERSON><PERSON> } from 'jsdom'; // Needed for server-side DOM manipulation

import { Errors } from '@/lib/error-utils';

// Create a DOM window for server-side sanitization
const window = new JSDOM('').window;
const purify = DOMPurify(window);

// Configuration for HTML sanitization
// Defines what HTML elements and attributes are allowed
export const sanitizeConfig = {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'] as string[], // Only allow basic formatting tags
  ALLOWED_ATTR: ['href', 'target'] as string[], // Only allow safe attributes
  ALLOW_DATA_ATTR: false, // Prevent data-* attributes
  RETURN_DOM_FRAGMENT: false, // Return string instead of DOM fragment
  RETURN_DOM: false, // Return string instead of DOM
  SANITIZE_DOM: true, // Enable DOM sanitization
} satisfies Config;

/**
 * Sanitizes HTML string to prevent XSS attacks
 * @param dirty - The unsanitized HTML string
 * @returns Clean and safe HTML string
 * @throws ValidationError if sanitization fails
 */
export function sanitizeHtml(dirty: string): string {
  try {
    // Sanitize the HTML using DOMPurify
    const clean = purify.sanitize(dirty, sanitizeConfig);

    // Ensure the result is a string
    if (typeof clean !== 'string') {
      throw new Error('Sanitization failed');
    }

    return clean;
  } catch (error) {
    throw Errors.ValidationError('Invalid HTML content', {
      description: 'The provided HTML content could not be sanitized',
      cause: error,
    });
  }
}

/**
 * Sanitizes all string values in an object
 * @param obj - Object containing potentially unsafe strings
 * @returns New object with all string values sanitized
 * @throws ValidationError if sanitization fails
 */
export function sanitizeObject<T extends object>(obj: T): T {
  try {
    return Object.entries(obj).reduce(
      (acc, [key, value]) => ({
        ...acc,
        // Only sanitize string values, leave other types unchanged
        [key]: typeof value === 'string' ? sanitizeHtml(value) : value,
      }),
      {} as T
    );
  } catch (error) {
    throw Errors.ValidationError('Invalid object content', {
      description: 'One or more fields contain invalid HTML content',
      cause: error,
    });
  }
}

/**
 * Sanitizes specific fields in an object while leaving others untouched
 * @param obj - Object containing potentially unsafe strings
 * @param fields - Array of field names to sanitize
 * @returns New object with specified fields sanitized
 */
export function sanitizeFields<T extends object>(
  obj: T,
  fields: (keyof T)[]
): T {
  return Object.entries(obj).reduce(
    (acc, [key, value]) => ({
      ...acc,
      // Only sanitize string values in specified fields
      [key]:
        fields.includes(key as keyof T) && typeof value === 'string'
          ? sanitizeHtml(value)
          : value,
    }),
    {} as T
  );
}
