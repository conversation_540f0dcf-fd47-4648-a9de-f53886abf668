import { z } from 'zod';

import { Errors } from '../error-utils';
import type { ErrorContext } from '../error-utils';
import { sanitizeHtml } from './sanitize';

// Enhanced validation error context type
interface ValidationErrorContext extends ErrorContext {
  issues: z.ZodIssue[];
  field?: string;
  value?: unknown;
}

// Base validation rules with better typing
export const baseStringRules = {
  trim: true,
  min: 1,
  max: 1000,
} as const;

// Common validation schemas with better error handling
export const ValidationSchemas = {
  email: z.string().trim().toLowerCase().email({
    message: 'Please enter a valid email address',
  }),

  username: z
    .string()
    .min(3, { message: 'Username must be at least 3 characters' })
    .max(30, { message: 'Username must be at most 30 characters' })
    .regex(/^[a-zA-Z0-9_-]+$/, {
      message:
        'Username can only contain letters, numbers, underscores, and hyphens',
    }),

  url: z.string().trim().url({
    message: 'Please enter a valid URL',
  }),

  safeHtml: z.string().transform((val) => sanitizeHtml(val)),

  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .regex(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/, {
      message: 'Password must contain at least one letter and one number',
    }),
} as const;

// Enhanced validation middleware with better error context
export const validateInput = <T>(schema: z.Schema<T>) => {
  return async (input: unknown): Promise<T> => {
    const result = await schema.safeParseAsync(input);

    if (!result.success) {
      const firstIssue = result.error.issues[0];
      const errorContext: ValidationErrorContext = {
        issues: result.error.issues,
        field: firstIssue?.path.join('.'),
        value: firstIssue?.message,
      };

      throw Errors.ValidationError(
        firstIssue?.message || 'Validation failed',
        errorContext
      );
    }

    return result.data;
  };
};

// Type-safe schema creator
export const createSchema = <T extends z.ZodRawShape>(schema: T) => {
  return z.object(schema);
};
