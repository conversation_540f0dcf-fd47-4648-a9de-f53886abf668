import { z } from 'zod';

const TagSchema = z.object({
  name: z.string(),
  slug: z.string(),
});

export const CreatePostSchema = z.object({
  title: z.string().optional(),
  content: z.any().optional(),
  cover: z.string().optional().nullable(),
  published: z.boolean().optional(),
  tags: z.array(TagSchema).optional(),
  excerpt: z.string().optional(),
});

export type CreatePostValues = z.infer<typeof CreatePostSchema>;
export type CreateTagValues = z.infer<typeof TagSchema>;
