import { Role } from '@prisma/client';
import * as z from 'zod';

// Create a subset of Role that excludes OWNER
const InviteRole = z.enum([Role.USER, Role.COLLABORATOR, Role.ADMIN]);

export const SendInviteSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: InviteRole,
});

export const InviteListSchema = z.object({
  invites: z.array(SendInviteSchema).min(1, {
    message: 'Please add at least one invite',
  }),
});

export type SendInviteValues = z.infer<typeof SendInviteSchema>;
export type InviteListFormValues = z.infer<typeof InviteListSchema>;
export type InviteRole = z.infer<typeof InviteRole>;
