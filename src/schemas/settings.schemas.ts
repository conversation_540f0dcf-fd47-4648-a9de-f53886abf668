import { z } from 'zod';

export const UpdateNotificationSettingsSchema = z.object({
  blogNotifications: z.boolean().default(true),
  subscriptionNotifications: z.boolean().default(false),
  marketingNotifications: z.boolean().default(false),
  marketingEmails: z.boolean().default(true),
  securityEmails: z.boolean().default(true),
});

export type UpdateNotificationSettingsValues = z.infer<
  typeof UpdateNotificationSettingsSchema
>;
