import * as z from 'zod';

export const createTenantSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  description: z.string().optional(),
  subdomain: z
    .string()
    .min(3, {
      message: 'Subdomain must be at least 3 characters.',
    })
    .max(63, {
      message: 'Subdomain must be less than 63 characters.',
    })
    .regex(
      /^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/,
      'Subdomain can only contain lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen.'
    ),
  logo: z.string().optional(),
});

export const updateTenantSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  logo: z.string().optional(),
  subdomain: z.string().optional(),
});

export type CreateTenantValues = z.infer<typeof createTenantSchema>;
export type UpdateTenantValues = z.infer<typeof updateTenantSchema>;
