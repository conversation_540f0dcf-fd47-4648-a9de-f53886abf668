import * as z from 'zod';

export const CreateApiKeySchema = z.object({
  name: z
    .string()
    .min(2, { message: 'Name must be at least 2 characters' })
    .max(50, { message: 'Name must be less than 50 characters' })
    .regex(/^[\w\- ]+$/, {
      message: 'Name can only contain letters, numbers, spaces, and hyphens',
    }),
});

export type CreateApiKeyValues = z.infer<typeof CreateApiKeySchema>;