import { z } from 'zod';

export const UpdateProfileSchema = z.object({
  image: z.string().optional().nullable(),
  name: z
    .string()
    .min(2, {
      message: 'Name must be at least 2 characters.',
    })
    .max(30, {
      message: 'Name must not be longer than 30 characters.',
    }),
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  bio: z.string().max(160).optional(),
  urls: z.array(z.object({ value: z.string() })).optional(),
});

export type UpdateProfileValues = z.infer<typeof UpdateProfileSchema>;
