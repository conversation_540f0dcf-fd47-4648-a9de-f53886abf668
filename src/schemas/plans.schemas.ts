import { Tier } from '@prisma/client';
import * as z from 'zod';

export const CreatePlanSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  features: z.string().min(1, 'Features are required'),
  monthlyPrice: z.number().min(0, 'Price must be a positive number'),
  yearlyPrice: z.number().min(0, 'Price must be a positive number'),
  isPopular: z.boolean().default(false),
  tier: z.nativeEnum(Tier),
});

export type CreatePlanValues = z.infer<typeof CreatePlanSchema>;

export type CreatePlanInputValues = Omit<CreatePlanValues, 'features'> & {
  features: string[];
};
