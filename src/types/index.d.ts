import { User } from '@prisma/client';
import Stripe from 'stripe';

export type NavItem = {
  title: string;
  href: string;
  disabled?: boolean;
  subItems?: NavItem[];
};

export type MainNavItem = NavItem;

export type SidebarNavItem = {
  title: string;
  external?: boolean;
  icon?: keyof typeof Icons;
  disabled?: boolean;
  children?: SidebarNavItem[];
  roles?: Role[];
} & (
  | {
      href: string;
    }
  | {
      href?: string;
    }
);
export type SiteConfig = {
  name: string;
  description: string;
  url: string;
  ogImage: string;
  links: {
    twitter: string;
    github: string;
  };
};

export type DocsConfig = {
  mainNav: MainNavItem[];
  sidebarNav?: SidebarNavItem[];
};

export type BlogConfig = {
  mainNav: MainNavItem[];
};

export type LandingConfig = {
  mainNav: MainNavItem[];
};

export type AppConfig = {
  sidebarNav: SidebarNavItem[];
  admin: SidebarNavItem[];
};

export type MarketingConfig = {
  mainNav: MainNavItem[];
};

export type SettingsConfig = {
  sidebarNav: SidebarNavItem[];
};

// Subscriptions
export type BillingInterval = Stripe.Response['data']['billing_scheme'];
export type CurrentPeriod = Stripe.Response['data']['current_period_end'];
export type Pricing = {
  price: number;
  currency: string;
  billingInterval: BillingInterval;
  priceId: string | null;
};

export type SubscriptionPlan = {
  name: string;
  description: string;
  pricing: {
    monthly: Pricing;
    yearly: Pricing;
  };
  features: string[];
};

export type UserSubscriptionPlan = SubscriptionPlan &
  Pick<User, 'stripeCustomerId' | 'stripeSubscriptionId' | 'stripePriceId'> & {
    stripeCurrentPeriodEnd: CurrentPeriod;
    isSubscribed: string | boolean | null;
    isCanceled: boolean | '' | null;
  };
