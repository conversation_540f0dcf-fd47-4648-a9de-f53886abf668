import {
  Bookmark as PrismaBookmark,
  Comment as PrismaComment,
  Like as PrismaLike,
  Post as PrismaPost,
  Tag as PrismaTag,
  User as PrismaUser,
} from '@prisma/client';

// Type definitions

export type Author = Pick<
  PrismaUser,
  'id' | 'name' | 'image' | 'email' | 'username' | 'bio' | 'urls'
>;

export type Comment = PrismaComment & {
  author: Author;
  replies?: Comment[];
  _count: {
    likes: number;
  };
  likes?: Like[];
};

export type Like = PrismaLike & {
  author: Author;
};

export type Post = PrismaPost & {
  author: Author;
  tags: Tag[];
  _count: {
    comments: number;
    likes: number;
    bookmarks: number;
  };
  totalCommentCount?: number;
  comments?: Comment[];
  bookmarks?: Bookmark[];
  likes?: Like[];
};

export type Bookmark = PrismaBookmark;

export type Tag = PrismaTag;

// New type for tag input that doesn't require tenantId
export type TagInput = Pick<PrismaTag, 'name' | 'slug'>;

export type GetPostsParams = {
  take?: number;
  cursor?: string;
  orderBy?: { [key: string]: 'asc' | 'desc' };
  filter?: {
    authors?: string[];
    tags?: string[];
    searchQuery?: string;
  };
};

export type GetPostResult = Post & {
  comments: Comment[];
  likes: Like[];
  bookmarks: Bookmark[];
};
