import { Role } from '@prisma/client';
import { DefaultUser } from 'next-auth';

interface IUser extends Default<PERSON>ser {
  id: string;
  name?: string;
  role?: Role;
  isActive?: boolean;
  email?: string;
  bio?: string;
  urls?: string[];
  username?: string;
  image?: string;
  currentTenantId?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  stripePriceId?: string;
  stripeCurrentPeriodEnd?: Date;
  subscriptionStatus?: string;
}

declare module 'next-auth' {
  interface User extends IUser {}

  interface Session {
    user?: User;
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends IUser {}
}
