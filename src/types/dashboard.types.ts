export interface DashboardData {
  totalRevenue: number;
  totalSubscriptions: number;
  popularPlans: Array<{
    name: string;
    count: number;
  }>;
  paymentMethodBreakdown: Record<string, number>;
  geographicDistribution: Record<string, number>;
  recurringRevenue: number;
  paymentSuccessRate: number;
  subscriptionGrowth: number;
  monthlySales: Array<{ name: string; total: number }>;
  recentSales: Array<{
    amount: number;
    email: string | null;
    date: string;
  }>;
  activeNow: string;
  metrics: {
    revenueChange: string;
    salesChange: string;
    subscriptionTrends: Array<{
      name: string;
      subscriptions: number;
      churn: number;
    }>;
  };
}
