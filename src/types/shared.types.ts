export type Order = 'asc' | 'desc';

export type GetPaginatedResult<T> = {
  items: T[];
  page: number;
  totalPages: number;
  totalCount: number;
  order?: Order;
  orderBy?: any;
  filter?: string;
};

export type GetCursorPaginationResult<T> = {
  items: T[];
  nextCursor: string | null;
  totalCount: number;
  hasMore: boolean;
};

export type GetPaginatedParams = {
  cursor?: string;
  take?: number;
  skip?: number;
  page?: number;
  orderBy?: { [key: string]: Order };
  filter?: {
    [key: string]: any;
    searchQuery?: string;
  };
};

export type PageProps = {
  searchParams: SearchParams;
};

export type SearchParams = {
  q: string;
  page: string;
  orderBy: string;
  order: string;
  tab: string;
};
