// app/store.ts
import { create } from 'zustand';

// Define a generic state type
interface DynamicStore<T extends Record<string, unknown>> {
  state: T;
  setState: <K extends keyof T>(key: K, value: T[K]) => void;
}

// Create the store with a generic type for state
export const useDynamicStore = <T extends Record<string, unknown>>() =>
  create<DynamicStore<T>>((set) => ({
    state: {} as T,

    // Setter function that updates the store dynamically
    setState: (key, value) =>
      set((state) => ({
        state: { ...state.state, [key]: value },
      })),
  }));
