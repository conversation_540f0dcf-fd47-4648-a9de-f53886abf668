import { updateUserSettings } from '@/actions/settings';
import { create } from 'zustand';

type Settings = {
  blogNotifications: boolean;
  subscriptionNotifications: boolean;
  marketingNotifications: boolean;
  marketingEmails: boolean;
  securityEmails: boolean;
};

interface SettingsState {
  settings: Settings | null;
  updateSettings: (newSettings: Partial<Settings>) => Promise<void>;
}

export const useSettingsStore = create<SettingsState>((set) => ({
  settings: null,
  updateSettings: async (newSettings) => {
    const updatedSettings = await updateUserSettings(newSettings);
    set({ settings: updatedSettings });
  },
}));
