'use client';

import { ReactNode, useEffect } from 'react';

import { useDynamicStore } from './store';

// Generic provider that accepts any initial state structure
type ZustandProviderProps<T extends Record<string, unknown>> = {
  initialState: T;
  children: ReactNode;
};

export default function ZustandProvider<T extends Record<string, unknown>>({
  initialState,
  children,
}: ZustandProviderProps<T>) {
  const dynamicStore = useDynamicStore<T>();
  const setState = dynamicStore((state) => state.setState);

  useEffect(() => {
    Object.entries(initialState).forEach(([key, value]) => {
      setState(key as keyof T, value as T[keyof T]);
    });
  }, [initialState, setState]);

  return <>{children}</>;
}
