import { create } from 'zustand';

import { Tenant } from '@/types/tenant.types';

interface TenantState {
  currentTenant: Tenant | null;
  availableTenants: Tenant[];
  setCurrentTenant: (tenant: Tenant) => void;
  setAvailableTenants: (tenants: Tenant[]) => void;
}

export const useTenantStore = create<TenantState>((set) => ({
  currentTenant: null,
  availableTenants: [],
  setCurrentTenant: (tenant) => set({ currentTenant: tenant }),
  setAvailableTenants: (tenants) => set({ availableTenants: tenants }),
}));
