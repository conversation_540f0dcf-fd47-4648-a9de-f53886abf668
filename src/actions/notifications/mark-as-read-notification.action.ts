'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function markAsReadNotification(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!id) {
      throw Errors.ValidationError('Notification ID is required');
    }

    const notification = await db.notification.findUnique({
      where: { id },
    });

    if (!notification) {
      throw Errors.NotFound('Notification');
    }

    if (notification.userId !== user.id) {
      throw Errors.Forbidden('You can only update your own notifications');
    }

    const updatedNotification = await db.notification.update({
      where: {
        id,
        userId: user.id,
      },
      data: {
        isRead: true,
      },
    });

    revalidatePath('/notifications');
    revalidatePath('/', 'layout');
    return updatedNotification;
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
