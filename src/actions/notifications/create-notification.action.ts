'use server';

import { revalidatePath, revalidateTag } from 'next/cache';
import { NotificationType } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getNotificationContent } from '@/lib/get-notification-content';
import { getCurrentUser } from '@/lib/session';

interface CreateNotificationParams {
  type: NotificationType;
  content: string;
  userId: string;
  fromId: string;
  postId?: string;
  commentId?: string;
  likeId?: string;
}

export async function createNotification(params: CreateNotificationParams) {
  try {
    const user = await getCurrentUser();
    const tenant = user?.currentTenantId;

    if (!user || !tenant) {
      throw Errors.Unauthorized();
    }

    const formattedContent = getNotificationContent(
      params.type,
      params.content
    );

    const notification = await db.notification.create({
      data: {
        type: params.type,
        content: formattedContent,
        user: {
          connect: { id: params.userId },
        },
        from: {
          connect: { id: params.fromId },
        },
        tenant: {
          connect: { id: tenant },
        },
        ...(params.postId && {
          post: {
            connect: { id: params.postId },
          },
        }),
        ...(params.commentId && {
          comment: {
            connect: { id: params.commentId },
          },
        }),
        ...(params.likeId && {
          like: {
            connect: { id: params.likeId },
          },
        }),
      },
    });

    revalidateTag('/notifications');
    revalidatePath('/notifications');
    revalidatePath('/', 'layout');
    return notification;
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
