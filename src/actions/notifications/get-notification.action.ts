'use server';

import { Notification } from '@/types/notification.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getNotification(
  id: string
): Promise<Notification | null> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const notification = await db.notification.findUnique({
      where: {
        id: id,
      },
      include: {
        from: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        post: {
          select: {
            id: true,
            slug: true,
            title: true,
          },
        },
      },
    });

    return notification;
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
