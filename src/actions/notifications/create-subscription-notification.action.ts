'use server';

import { revalidatePath } from 'next/cache';
import { NotificationType, Role } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

import { createNotification } from './create-notification.action';

export async function createSubscriptionNotification(
  purchaserId: string,
  purchaserName: string,
  planName: string
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }
    if (!purchaserId || !purchaserName || !planName) {
      throw Errors.ValidationError(
        'Purchaser ID, name, and plan name are required'
      );
    }

    // Find all owner users
    const ownerUsers = await db.user.findMany({
      where: {
        role: Role.OWNER,
      },
    });

    if (!ownerUsers.length) {
      return { success: true };
    }

    // Create a notification for each owner user
    const notificationPromises = ownerUsers.map((owner) =>
      createNotification({
        type: NotificationType.SUBSCRIPTION_PURCHASE,
        content: planName,
        userId: owner.id,
        fromId: purchaserId,
      })
    );

    await Promise.all(notificationPromises);

    revalidatePath('/notifications');
    revalidatePath('/', 'layout');

    return { success: true };
  } catch (error) {
    throw handleServerError(error, { purchaserId, purchaserName, planName });
  }
}
