'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function deleteNotification(notificationId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!notificationId) {
      throw Errors.ValidationError('Notification ID is required');
    }

    const notification = await db.notification.findUnique({
      where: { id: notificationId },
    });

    if (!notification) {
      throw Errors.NotFound('Notification');
    }

    await db.notification.delete({
      where: {
        id: notificationId,
        userId: user.id,
      },
    });

    revalidatePath('/notifications');
    revalidatePath('/', 'layout');

    return { success: true };
  } catch (error) {
    throw handleServerError(error, { notificationId });
  }
}
