'use server';

import { Notification } from '@/types/notification.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getNotifications(): Promise<Notification[]> {
  const user = await getCurrentUser();
  const tenant = user?.currentTenantId;

  if (!user) {
    throw Errors.Unauthorized();
  }

  try {
    const notifications = await db.notification.findMany({
      where: {
        userId: user.id,
        ...(tenant && { tenantId: tenant }),
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        from: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        post: {
          select: {
            id: true,
            slug: true,
            title: true,
          },
        },
      },
    });

    return notifications;
  } catch (error) {
    throw handleServerError(error);
  }
}
