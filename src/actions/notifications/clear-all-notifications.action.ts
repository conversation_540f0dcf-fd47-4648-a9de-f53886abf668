'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function clearAllNotifications() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    await db.notification.deleteMany({
      where: {
        userId: user.id,
      },
    });

    revalidatePath('/notifications');
    revalidatePath('/', 'layout');
    return { success: true };
  } catch (error) {
    throw handleServerError(error);
  }
}
