'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function toggleBookmark(postId: string) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const existingBookmark = await db.bookmark.findFirst({
      where: {
        postId,
        userId: user.id,
      },
    });

    if (existingBookmark) {
      await db.bookmark.delete({
        where: {
          id: existingBookmark.id,
        },
      });
    } else {
      await db.bookmark.create({
        data: {
          userId: user.id,
          postId,
        },
      });
    }

    revalidatePath('/bookmarks');
    revalidatePath(`/blog/${postId}`);
    revalidatePath('/blog');
  } catch (error) {
    throw handleServerError(error, { postId });
  }
}
