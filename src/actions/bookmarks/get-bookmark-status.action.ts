'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getBookmarkStatus(postId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const bookmark = await db.bookmark.findFirst({
      where: {
        postId,
        userId: user.id,
      },
    });

    return !!bookmark;
  } catch (error) {
    throw handleServerError(error, { postId });
  }
}
