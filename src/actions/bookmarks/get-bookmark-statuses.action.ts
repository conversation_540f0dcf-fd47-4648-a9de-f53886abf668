'use server';

import { db } from '@/lib/db';
import { handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getBookmarkStatuses(postIds: string[]) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return postIds.reduce(
        (acc, postId) => {
          acc[postId] = false;
          return acc;
        },
        {} as Record<string, boolean>
      );
    }

    const bookmarks = await db.bookmark.findMany({
      where: {
        postId: { in: postIds },
        userId: user.id,
      },
      select: {
        postId: true,
      },
    });

    const bookmarkMap = bookmarks.reduce(
      (acc, bookmark) => {
        acc[bookmark.postId] = true;
        return acc;
      },
      {} as Record<string, boolean>
    );

    // Ensure all postIds are in the result, even if not bookmarked
    return postIds.reduce(
      (acc, postId) => {
        acc[postId] = !!bookmarkMap[postId];
        return acc;
      },
      {} as Record<string, boolean>
    );
  } catch (error) {
    throw handleServerError(error, { postIds });
  }
}
