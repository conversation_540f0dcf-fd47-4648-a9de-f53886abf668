'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function bookmarkPost(postId: string) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const existingBookmark = await db.bookmark.findFirst({
      where: {
        postId,
        userId: user.id,
      },
    });

    if (existingBookmark) {
      throw Errors.NotFound('Bookmark');
    }

    const newBookmark = await db.bookmark.create({
      data: {
        user: {
          connect: { id: user.id },
        },
        post: {
          connect: { id: postId },
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
            authorId: true,
          },
        },
      },
    });

    return newBookmark;
  } catch (error) {
    throw handleServerError(error, { postId });
  }
}
