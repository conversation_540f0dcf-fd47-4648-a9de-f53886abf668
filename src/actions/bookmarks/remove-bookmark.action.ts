'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function removeBookmark(postId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const existingBookmark = await db.bookmark.findFirst({
      where: {
        postId,
        userId: user.id,
      },
    });

    if (!existingBookmark) {
      throw Errors.NotFound('Bookmark');
    }

    await db.bookmark.delete({
      where: { id: existingBookmark.id, userId: user.id },
    });

    revalidatePath('/bookmarks');
    revalidatePath('/blog');
    revalidatePath(`/blog/${postId}`);
    return { success: true };
  } catch (error) {
    throw handleServerError(error, { postId });
  }
}
