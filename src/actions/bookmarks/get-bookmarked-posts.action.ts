'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getBookmarkedPosts() {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const bookmarkedPosts = await db.post.findMany({
      where: {
        bookmarks: {
          some: {
            userId: user.id,
          },
        },
      },
      include: {
        author: true,
        tags: true,
        _count: {
          select: {
            likes: true,
            comments: true,
            bookmarks: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return bookmarkedPosts;
  } catch (error) {
    throw handleServerError(error);
  }
}
