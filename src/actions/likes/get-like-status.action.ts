'use server';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function getLikeStatus(postId: string) {
  const user = await getCurrentUser();

  if (!user) {
    return false;
  }

  try {
    const like = await db.like.findFirst({
      where: {
        postId,
        authorId: user.id,
      },
    });

    return !!like;
  } catch (error) {
    console.error('Error fetching like status:', error);
    throw new Error('Failed to fetch like status');
  }
}
