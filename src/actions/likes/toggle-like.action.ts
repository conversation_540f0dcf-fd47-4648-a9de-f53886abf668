'use server';

import { revalidatePath } from 'next/cache';
import { createNotification } from '@/actions/notifications';
import { NotificationType } from '@prisma/client';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function toggleLike(postId: string) {
  const user = await getCurrentUser();

  if (!user?.id) {
    throw new Error('Unauthorized');
  }

  const existingLike = await db.like.findFirst({
    where: {
      postId,
      authorId: user.id,
    },
  });

  if (existingLike) {
    await db.like.delete({
      where: {
        id: existingLike.id,
      },
    });
  } else {
    const like = await db.like.create({
      data: {
        authorId: user.id,
        postId,
      },
    });

    const post = await db.post.findUnique({
      where: {
        id: postId,
      },
    });

    if (post && post.authorId !== user.id) {
      await createNotification({
        type: NotificationType.LIKE_POST,
        content: '', // No additional content needed for likes
        userId: post.authorId,
        fromId: user.id,
        postId: postId,
        likeId: like.id,
      });
    }
  }

  revalidatePath('/blog');
  revalidatePath(`/blog/${postId}`);
  revalidatePath('/posts');
}
