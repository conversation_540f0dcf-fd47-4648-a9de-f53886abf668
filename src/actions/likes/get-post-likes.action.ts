'use server';

import { db } from '@/lib/db';

export async function getPostLikes(postId: string) {
  try {
    const likes = await db.like.findMany({
      where: {
        postId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return likes;
  } catch (error) {
    console.error('Error fetching likes:', error);
    throw new Error('Failed to fetch likes');
  }
}
