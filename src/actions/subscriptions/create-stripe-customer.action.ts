'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { stripe } from '@/lib/stripe';

export async function createStripeCustomer(
  userId: string,
  email: string,
  name: string
) {
  try {
    if (!userId || !email || !name) {
      throw Errors.ValidationError('User ID, email, and name are required');
    }

    const existingUser = await db.user.findUnique({
      where: { id: userId },
      select: { stripeCustomerId: true },
    });

    if (existingUser?.stripeCustomerId) {
      return existingUser.stripeCustomerId;
    }

    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        userId,
      },
    });

    await db.user.update({
      where: { id: userId },
      data: { stripeCustomerId: customer.id },
    });

    return customer.id;
  } catch (error) {
    throw handleServerError(error, { userId, email, name });
  }
}
