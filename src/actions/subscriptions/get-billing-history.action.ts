'use server';

import Stripe from 'stripe';

import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';
import { stripe } from '@/lib/stripe';

export type BillingHistoryItem = {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'open' | 'void' | 'uncollectible' | 'draft';
  invoiceUrl: string | null;
};

export type BillingHistorySortField = 'date' | 'amount' | 'status';

export type GetBillingHistoryResult = {
  items: BillingHistoryItem[];
  hasMore: boolean;
  totalCount: number;
};

export async function getBillingHistory({
  page = 1,
  itemsPerPage = 10,
  sortBy,
  sortOrder,
}: {
  page?: number;
  itemsPerPage?: number;
  sortBy?: BillingHistorySortField;
  sortOrder?: 'asc' | 'desc';
} = {}): Promise<GetBillingHistoryResult> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const stripeCustomer = await stripe.customers.list({
      email: user.email,
      limit: 1,
    });

    if (stripeCustomer.data.length === 0) {
      return {
        items: [],
        hasMore: false,
        totalCount: 0,
      };
    }

    // First get total count
    const allInvoices = await stripe.invoices.list({
      customer: stripeCustomer.data[0].id,
      limit: 100, // Maximum allowed by Stripe
    });

    const totalCount = allInvoices.data.length || 0;

    // Calculate the correct starting position
    const startingIndex = (page - 1) * itemsPerPage;
    const relevantInvoice = allInvoices.data[startingIndex - 1];

    // Build sorting options
    let listOptions: Stripe.InvoiceListParams = {
      customer: stripeCustomer.data[0].id,
      limit: itemsPerPage,
      starting_after: page > 1 ? relevantInvoice?.id : undefined,
    };

    // Add sorting
    if (sortBy) {
      switch (sortBy) {
        case 'date':
          listOptions = {
            ...listOptions,
            created: sortOrder === 'desc' ? { lt: Date.now() } : { gt: 0 },
          };
          break;
        case 'amount':
          // For amount, we'll need to sort in memory since Stripe doesn't support amount sorting
          break;
        case 'status':
          // For status, we'll need to sort in memory since Stripe doesn't support status sorting
          break;
      }
    }

    const invoices = await stripe.invoices.list(listOptions);
    let items = invoices.data.map((invoice) => ({
      id: invoice.number || invoice.id,
      date: new Date(invoice.created * 1000).toLocaleDateString(),
      amount: invoice.amount_paid / 100,
      status: invoice.status || 'draft',
      invoiceUrl: invoice.hosted_invoice_url || null,
    }));

    // Handle in-memory sorting for fields not supported by Stripe
    if (sortBy && ['amount', 'status'].includes(sortBy)) {
      items = items.sort((a, b) => {
        if (sortBy === 'amount') {
          return sortOrder === 'desc'
            ? b.amount - a.amount
            : a.amount - b.amount;
        } else if (sortBy === 'status') {
          return sortOrder === 'desc'
            ? b.status.localeCompare(a.status)
            : a.status.localeCompare(b.status);
        }
        return 0;
      });
    }

    return {
      items,
      hasMore: invoices.has_more,
      totalCount,
    };
  } catch (error) {
    throw handleServerError(error);
  }
}
