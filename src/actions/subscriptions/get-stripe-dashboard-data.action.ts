'use server';

import { DashboardData } from '@/types/dashboard.types';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';
import { stripe } from '@/lib/stripe';

export async function getStripeDashboardData(): Promise<DashboardData> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    const [
      balanceResponse,
      subscriptions,
      charges,
      customers,
      paymentIntents,
      paymentMethods,
    ] = await Promise.all([
      stripe.balance.retrieve(),
      stripe.subscriptions.list({
        limit: 100,
        expand: ['data.items.data.price'],
      }),
      stripe.charges.list({
        limit: 100,
        created: {
          gte: Math.floor(Date.now() / 1000) - 365 * 24 * 60 * 60,
        },
      }),
      stripe.customers.list({
        limit: 100,
        expand: ['data.address'],
      }),
      stripe.paymentIntents.list({ limit: 100 }),
      stripe.paymentMethods.list({ limit: 100 }),
    ]);

    // Calculate payment method breakdown
    const paymentMethodBreakdown = paymentMethods.data.reduce(
      (acc, method) => {
        const type = method.type;
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // Format payment method data for the graph
    // const paymentMethodsData = Object.entries(paymentMethodBreakdown).map(
    //   ([name, value]) => ({
    //     name,
    //     value,
    //   })
    // );

    // Calculate basic metrics
    const totalRevenue =
      balanceResponse.available.reduce(
        (acc, balance) => acc + balance.amount,
        0
      ) / 100;

    const recentSales = charges.data.slice(0, 10).map((charge) => ({
      amount: charge.amount / 100,
      email: charge.billing_details.email,
      date: new Date(charge.created * 1000).toLocaleString(),
    }));

    // Calculate subscription metrics
    const thirtyDaysAgo = Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60;
    const subscriptionGrowth = subscriptions.data.filter(
      (sub) => sub.created >= thirtyDaysAgo
    ).length;

    // Calculate payment success rate
    const successfulPayments = paymentIntents.data.filter(
      (pi) => pi.status === 'succeeded'
    ).length;
    const paymentSuccessRate =
      (successfulPayments / paymentIntents.data.length) * 100;

    // Calculate popular plans
    const planCounts = subscriptions.data.reduce(
      (acc, sub) => {
        const planId = sub.items.data[0].price.id;
        acc[planId] = (acc[planId] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const popularPlans = Object.entries(planCounts)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate recurring revenue
    const recurringRevenue = subscriptions.data.reduce(
      (acc, sub) => acc + (sub.items.data[0].price.unit_amount || 0) / 100,
      0
    );

    // Calculate geographic distribution
    const geographicDistribution = customers.data.reduce(
      (acc, customer) => {
        const country = customer.address?.country;
        if (country) {
          acc[country] = (acc[country] || 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>
    );

    // Calculate monthly sales
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const monthlySales = monthNames.map((name) => ({ name, total: 0 }));

    charges.data.forEach((charge) => {
      const monthIndex = new Date(charge.created * 1000).getMonth();
      monthlySales[monthIndex].total += charge.amount / 100;
    });

    monthlySales.forEach((month) => {
      month.total = Math.round(month.total);
    });

    // Calculate trends
    const subscriptionTrends = monthNames.map((name, index) => {
      const monthStart = new Date();
      monthStart.setMonth(monthStart.getMonth() - (11 - index));
      monthStart.setDate(1);
      monthStart.setHours(0, 0, 0, 0);

      const monthEnd = new Date(monthStart);
      monthEnd.setMonth(monthEnd.getMonth() + 1);

      const monthSubscriptions = subscriptions.data.filter(
        (sub) =>
          sub.created * 1000 >= monthStart.getTime() &&
          sub.created * 1000 < monthEnd.getTime()
      ).length;

      const monthChurn = subscriptions.data.filter(
        (sub) =>
          sub.canceled_at &&
          sub.canceled_at * 1000 >= monthStart.getTime() &&
          sub.canceled_at * 1000 < monthEnd.getTime()
      ).length;

      return { name, subscriptions: monthSubscriptions, churn: monthChurn };
    });

    return {
      totalRevenue,
      totalSubscriptions: subscriptions.data.length,
      recentSales,
      activeNow: 'N/A',
      subscriptionGrowth,
      paymentSuccessRate,
      popularPlans,
      recurringRevenue,
      geographicDistribution,
      monthlySales,
      paymentMethodBreakdown,
      metrics: {
        revenueChange: '0.0',
        salesChange: '0.0',
        subscriptionTrends,
      },
    };
  } catch (error) {
    throw handleServerError(error);
  }
}
