'use server';

import { auth } from '@/auth';

import { Errors, handleServerError } from '@/lib/error-utils';
import { stripe } from '@/lib/stripe';
import { getUserSubscriptions } from '@/lib/subscription';
import { absoluteUrl } from '@/lib/utils';

export const manageStripeSubscription = async ({
  priceId,
  isCurrentPlan,
}: {
  priceId: string;
  isCurrentPlan: boolean;
}) => {
  try {
    const billingUrl = absoluteUrl('/billing');
    const successUrl = '/subscription';
    const session = await auth();

    if (!session?.user || !session?.user.email) {
      throw Errors.Unauthorized();
    }

    // Get user subscription plan
    const subscriptionPlan = await getUserSubscriptions();

    if (
      subscriptionPlan.isSubscribed &&
      subscriptionPlan.stripeCustomerId &&
      isCurrentPlan &&
      priceId === subscriptionPlan.stripePriceId
    ) {
      // Redirect to billing portal for managing subscription if already subscribed
      const stripeSession = await stripe.billingPortal.sessions.create({
        customer: subscriptionPlan.stripeCustomerId,
        return_url: billingUrl,
      });
      return stripeSession.url;
    }

    if (
      subscriptionPlan.isSubscribed &&
      subscriptionPlan.stripeSubscriptionId
    ) {
      // Retrieve the subscription to get the subscription item ID
      const subscription = await stripe.subscriptions.retrieve(
        subscriptionPlan.stripeSubscriptionId
      );

      const subscriptionItemId = subscription.items.data[0].id;

      // Upgrade/downgrade existing subscription with proration
      const updatedSubscription = await stripe.subscriptions.update(
        subscriptionPlan.stripeSubscriptionId,
        {
          items: [
            {
              id: subscriptionItemId, // Make sure this is stored properly in your database
              price: priceId,
            },
          ],
          proration_behavior: 'create_prorations',
        }
      );
      return updatedSubscription; // Or handle it based on success
    } else {
      // Create a new checkout session for users without any subscription
      const finalSuccessUrl =
        absoluteUrl(successUrl || '/billing') + '?success=true';
      const stripeSession = await stripe.checkout.sessions.create({
        mode: 'subscription',
        customer_email: session.user.email,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        metadata: {
          userId: session.user.id as string,
        },
        success_url: finalSuccessUrl,
        cancel_url: billingUrl,
      });

      return stripeSession.url;
    }
  } catch (error) {
    throw handleServerError(error, { priceId, isCurrentPlan });
  }
};
