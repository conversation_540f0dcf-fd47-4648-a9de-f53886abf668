'use server';

import { revalidatePath } from 'next/cache';
import {
  updateTenantSchema,
  type UpdateTenantValues,
} from '@/schemas/tenant.schemas';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function updateTenant(id: string, input: UpdateTenantValues) {
  const user = await getCurrentUser();

  if (!user?.id) {
    throw new Error('Unauthorized');
  }
  const validatedFields = updateTenantSchema.parse(input);

  if (validatedFields.subdomain) {
    // Check if subdomain is already taken by another tenant
    const existingTenant = await db.tenant.findFirst({
      where: {
        subdomain: validatedFields.subdomain,
        NOT: {
          id: id, // Exclude current tenant from the check
        },
      },
    });

    if (existingTenant) {
      throw new Error('Subdomain is already taken');
    }
  }

  try {
    const updatedTenant = await db.tenant.update({
      where: {
        id: id, // Use the tenant ID for updating
      },
      data: validatedFields,
    });

    revalidatePath('/organizations');
    return updatedTenant;
  } catch (error) {
    console.error('Error updating tenant', error);
    throw error;
  }
}
