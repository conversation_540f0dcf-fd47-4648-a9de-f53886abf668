'use server';

import { revalidatePath } from 'next/cache';

import { Tenant } from '@/types/tenant.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getTenantsByUser(): Promise<Tenant[]> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const tenants = await db.tenant.findMany({
      where: {
        users: {
          some: {
            userId: user.id,
          },
        },
      },
      include: {
        users: true,
      },
    });

    revalidatePath('/organizations');
    return tenants;
  } catch (error) {
    throw handleServerError(error);
  }
}
