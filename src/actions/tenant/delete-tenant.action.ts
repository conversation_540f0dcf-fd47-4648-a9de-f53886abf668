'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function deleteTenant(id: string) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const tenant = await db.tenant.findUnique({
      where: { id },
    });

    if (!tenant) {
      throw Errors.NotFound('Tenant');
    }

    const canDeleteTenant = permissionService.can({
      user,
      resource: 'tenant',
      action: 'delete',
    });

    if (!canDeleteTenant) {
      throw Errors.Forbidden('You do not have permission to delete tenants');
    }

    // if this is this is the last tenant of the user, deny deletion
    const userTenants = await db.tenant.findMany({
      where: {
        ownerId: user.id,
      },
    });

    if (userTenants.length === 1) {
      throw Errors.ValidationError('Cannot delete last tenant');
    }

    await db.tenant.delete({
      where: { id, ownerId: user.id },
    });
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
