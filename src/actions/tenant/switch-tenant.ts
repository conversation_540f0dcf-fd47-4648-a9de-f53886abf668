'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function switchTenant(tenantId: string) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw new Error('Unauthorized');
    }

    // Verify user has access to this tenant
    const tenantUser = await db.tenantsOnUsers.findFirst({
      where: {
        userId: user.id,
        tenantId: tenantId,
      },
      include: {
        tenant: true,
      },
    });

    if (!tenantUser) {
      throw new Error('User does not have access to this tenant');
    }

    // Update the session with the new tenant
    await db.user.update({
      where: { id: user.id },
      data: {
        currentTenantId: tenantId,
      },
    });

    revalidatePath('/users');
    revalidatePath('/invitations');
    return { success: true, tenant: tenantUser.tenant };
  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}
