'use server';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export type TenantMember = {
  id: string;
  email: string;
  name?: string | null;
  image?: string | null;
};

export async function getTenantMembers(
  tenantId: string
): Promise<TenantMember[]> {
  const currentUser = await getCurrentUser();

  if (!currentUser?.id) {
    throw new Error('Unauthorized');
  }

  try {
    const members = await db.tenantsOnUsers.findMany({
      where: {
        tenantId,
        userId: {
          not: currentUser.id, // Exclude current user
        },
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return members.map((m) => ({
      id: m.user.id,
      email: m.user.email!,
      name: m.user.name,
      image: m.user.image,
    }));
  } catch (error) {
    console.error('Error fetching tenant members:', error);
    throw new Error('Failed to fetch tenant members');
  }
}
