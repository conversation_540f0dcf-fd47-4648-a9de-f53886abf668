'use server';

import { revalidatePath } from 'next/cache';
import {
  createTenantSchema,
  type CreateTenantValues,
} from '@/schemas/tenant.schemas';
import { Role } from '@prisma/client';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function createTenant(input: CreateTenantValues) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw new Error('Unauthorized');
    }

    const validatedFields = createTenantSchema.parse(input);

    // Check if subdomain is already taken
    const existingTenant = await db.tenant.findUnique({
      where: {
        subdomain: validatedFields.subdomain,
      },
    });

    if (existingTenant) {
      throw new Error('Subdomain is already taken');
    }

    // Create tenant and associate it with the user
    const tenant = await db.tenant.create({
      data: {
        ...validatedFields,
        ownerId: user.id,
        users: {
          create: [
            {
              userId: user.id,
              role: Role.ADMIN,
            },
          ],
        },
      },
      include: {
        users: true,
      },
    });

    if (!user.currentTenantId) {
      await db.user.update({
        where: {
          id: user.id,
        },
        data: {
          currentTenantId: tenant.id,
        },
      });
    }

    revalidatePath('/organizations');
    revalidatePath('/', 'layout');
    return tenant;
  } catch (error) {
    console.error(error);
    throw error;
  }
}
