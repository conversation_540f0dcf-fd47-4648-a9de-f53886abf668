'use server';

import { Role } from '@prisma/client';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function transferTenantOwnership(
  tenantId: string,
  newOwnerId: string
) {
  const currentUser = await getCurrentUser();

  if (!currentUser?.id) {
    throw new Error('Unauthorized');
  }

  // Find the tenant and verify current user is the owner
  const tenant = await db.tenant.findFirst({
    where: {
      id: tenantId,
      ownerId: currentUser.id,
    },
  });

  if (!tenant) {
    throw new Error('Tenant not found or you are not the owner');
  }

  // Verify the new owner exists and is a member of the tenant
  const newOwnerMembership = await db.tenantsOnUsers.findFirst({
    where: {
      tenantId: tenantId,
      userId: newOwnerId,
    },
  });

  if (!newOwnerMembership) {
    throw new Error('New owner must be a member of the tenant');
  }

  try {
    // Use a transaction to ensure all updates succeed or fail together
    await db.$transaction(async (tx) => {
      // Update the tenant's owner
      await tx.tenant.update({
        where: { id: tenantId },
        data: { ownerId: newOwnerId },
      });

      // Update the new owner's role to ADMIN (tenant owner)
      await tx.tenantsOnUsers.update({
        where: {
          userId_tenantId: {
            userId: newOwnerId,
            tenantId: tenantId,
          },
        },
        data: { role: Role.ADMIN },
      });
    });

    return { success: true };
  } catch (error) {
    console.error('Error transferring tenant ownership:', error);
    throw new Error('Failed to transfer tenant ownership');
  }
}
