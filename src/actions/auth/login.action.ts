'use server';

import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';
import { SigninSchema, SigninValues } from '@/schemas/auth.schemas';

import { Errors, handleServerError } from '@/lib/error-utils';

export async function login(values: SigninValues, redirectUrl?: string) {
  try {
    const isValid = SigninSchema.safeParse(values);

    if (!isValid.success) {
      throw Errors.ValidationError('Email is not valid!');
    }

    return await signIn('email', {
      email: values.email,
      redirectTo: redirectUrl || REDIRECT_URL,
    });
  } catch (error) {
    throw handleServerError(error, { email: values.email });
  }
}
