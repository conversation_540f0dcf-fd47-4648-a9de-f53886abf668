'use server';

import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';

import { handleServerError } from '@/lib/error-utils';

export async function loginOAuth(
  provider: 'google' | 'github',
  redirectUrl?: string
) {
  try {
    const res = await signIn(provider, {
      redirectTo: redirectUrl || REDIRECT_URL,
    });

    return res;
  } catch (error) {
    handleServerError(error, { provider });
  }
}
