'use server';

import { compressImage } from '@/lib/image-utils/image-optimizer';
import { bucketName, client, PutObjectCommand, uploadPath } from '@/lib/S3';
import { getCurrentUser } from '@/lib/session';

type UploadFileResponse = {
  url: string;
};

export const uploadFile = async (
  formData: FormData
): Promise<UploadFileResponse> => {
  const file = formData.get('file') as File;
  const filename = file.name;
  const contentType = file.type || 'application/octet-stream';

  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Not authenticated');
  }

  if (
    !bucketName ||
    !process.env.AWS_REGION ||
    !process.env.AWS_ACCESS_KEY_ID ||
    !process.env.AWS_SECRET_ACCESS_KEY
  ) {
    throw new Error(
      "Missing AWS_BUCKET_NAME, AWS_REGION, AWS_ACCESS_KEY_ID, or AWS_SECRET_ACCESS_KEY. Don't forget to add that to your .env file."
    );
  }

  let imageBuffer: Buffer;

  // if image compress is needed
  if (contentType.startsWith('image/')) {
    imageBuffer = await compressImage(file, 1200);
  }
  // else just convert to buffer
  imageBuffer = Buffer.from(await file.arrayBuffer());

  const uniqueId = new Date().getTime().toString();
  const finalName = `${uniqueId}_${filename}`;

  const params = {
    Bucket: bucketName,
    Key: `${uploadPath}/${finalName}`,
    Body: imageBuffer,
    ContentType: contentType,
  };

  const command = new PutObjectCommand(params);

  try {
    await client.send(command);
    const url = `https://${bucketName}.s3.amazonaws.com/${uploadPath}/${finalName}`;
    return { url };
  } catch (error) {
    console.error(error);
    throw new Error('Error uploading file');
  }
};
