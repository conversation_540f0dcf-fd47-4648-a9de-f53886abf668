'use server';

import { Errors, handleServerError } from '@/lib/error-utils';
import { bucketName, client, DeleteObjectCommand } from '@/lib/S3';
import { getCurrentUser } from '@/lib/session';

export async function deleteFile(fileName: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!fileName) {
      throw Errors.ValidationError('File name is required');
    }

    const params = {
      Bucket: bucketName,
      Key: fileName,
    };

    const command = new DeleteObjectCommand(params);
    await client.send(command);

    return { success: true };
  } catch (error) {
    throw handleServerError(error, { fileName });
  }
}
