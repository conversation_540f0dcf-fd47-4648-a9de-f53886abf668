'use server';

import { compressImage } from '@/lib/image-utils/image-optimizer';
import {
  bucketName,
  client,
  DeleteObjectCommand,
  PutObjectCommand,
  uploadPath,
} from '@/lib/S3';
import { getCurrentUser } from '@/lib/session';

export const updateFile = async (oldFileUrl: string, formData: FormData) => {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Not authenticated');
  }

  if (
    !process.env.AWS_BUCKET_NAME ||
    !process.env.AWS_REGION ||
    !process.env.AWS_ACCESS_KEY ||
    !process.env.AWS_SECRET_ACCESS_KEY
  ) {
    throw new Error(
      "Missing AWS_BUCKET_NAME, AWS_REGION, AWS_ACCESS_KEY, or AWS_SECRET_ACCESS_KEY. Don't forget to add that to your .env file."
    );
  }

  // Delete the old file
  const oldKey = oldFileUrl.split(bucketName + '.s3.amazonaws.com/')[1];
  const deleteParams = {
    Bucket: bucketName,
    Key: `${uploadPath}/${oldKey}`,
  };

  try {
    const deleteCommand = new DeleteObjectCommand(deleteParams);
    await client.send(deleteCommand);
  } catch (error) {
    console.error(`Error deleting old file: ${oldKey}`, error);
    throw error;
  }

  // Upload the new file
  const file = formData.get('file') as File;
  const filename = file.name;
  const contentType = file.type || 'application/octet-stream';

  let imageBuffer: Buffer;

  // if image compress is needed
  if (contentType.startsWith('image/')) {
    imageBuffer = await compressImage(file, 1200);
  }
  // else just convert to buffer
  imageBuffer = Buffer.from(await file.arrayBuffer());

  const uniqueId = new Date().getTime().toString();
  const finalName = `${uniqueId}_${filename}`;

  const imageParams = {
    Bucket: bucketName,
    Key: `${uploadPath}/${finalName}`,
    Body: imageBuffer,
    ContentType: contentType,
  };

  try {
    const command = new PutObjectCommand(imageParams);
    await client.send(command);
    const url = `https://${bucketName}.s3.amazonaws.com/${uploadPath}/${finalName}`;
    return { url };
  } catch (error) {
    console.error(`Error uploading new file: ${finalName}`, error);
    throw error;
  }
};
