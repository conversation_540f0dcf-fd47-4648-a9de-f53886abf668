'use server';

import { revalidatePath } from 'next/cache';
import { createNotification } from '@/actions/notifications/create-notification.action';
import { NotificationType } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function createComment(
  postId: string,
  content: string,
  parentId?: string
) {
  try {
    const user = await getCurrentUser();

    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    if (!parentId) {
      throw Errors.ValidationError('Parent comment ID is required');
    }

    // If parentId is provided, verify the parent comment exists

    const parentComment = await db.comment.findUnique({
      where: { id: parentId },
    });

    if (!parentComment) {
      throw Errors.NotFound('Parent comment');
    }

    const newComment = await db.comment.create({
      data: {
        content,
        author: {
          connect: { id: user.id },
        },
        post: {
          connect: { id: postId },
        },
        ...(parentId && {
          parent: {
            connect: { id: parentId },
          },
        }),
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        post: {
          select: {
            id: true,
            authorId: true,
            title: true,
            slug: true,
          },
        },
      },
    });

    // Create notification for the post author
    if (newComment.post && newComment.post.authorId !== user.id) {
      await createNotification({
        type: NotificationType.COMMENT,
        content: content.substring(0, 200), // Limit content length
        userId: newComment.post.authorId,
        fromId: user.id,
        postId: newComment.post.id,
        commentId: newComment.id,
      });
    }

    // If it's a reply, create a notification for the parent comment author
    if (parentId && newComment.post) {
      const parentComment = await db.comment.findUnique({
        where: { id: parentId },
        select: { authorId: true, content: true },
      });

      if (parentComment && parentComment.authorId !== user.id) {
        await createNotification({
          type: NotificationType.REPLY,
          content: content.substring(0, 200), // Limit content length
          userId: parentComment.authorId,
          fromId: user.id,
          postId: newComment.post.id,
          commentId: newComment.id,
        });
      }
    }

    revalidatePath(`/blog/${newComment.post.slug}`);
    revalidatePath('/blog');
    revalidatePath('/posts');
    return newComment;
  } catch (error) {
    throw handleServerError(error, { postId, content, parentId });
  }
}
