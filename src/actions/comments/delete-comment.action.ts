'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function deleteComment(commentId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!commentId) {
      throw Errors.ValidationError('Comment ID is required');
    }

    const comment = await db.comment.findUnique({
      where: { id: commentId },
      select: {
        authorId: true,
        post: { select: { slug: true } },
      },
    });

    if (!comment) {
      throw Errors.NotFound('Comment');
    }

    if (comment.authorId !== user.id) {
      throw Errors.Forbidden('You can only delete your own comments');
    }

    await db.comment.delete({
      where: { id: commentId },
    });

    revalidatePath(`/blog/${comment.post.slug}`);
    revalidatePath('/blog');
    revalidatePath('/posts');

    return { success: true };
  } catch (error) {
    throw handleServerError(error, { commentId });
  }
}
