'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function updateComment(commentId: string, content: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!commentId || !content) {
      throw Errors.ValidationError('Comment ID and content are required');
    }

    const updatedComment = await db.comment.update({
      where: { id: commentId, authorId: user.id },
      data: {
        content,
        updatedAt: new Date(),
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        post: {
          select: { slug: true },
        },
      },
    });

    revalidatePath(`/blog/${updatedComment.post.slug}`);
    revalidatePath('/blog');
    revalidatePath('/posts');

    return updatedComment;
  } catch (error) {
    throw handleServerError(error, { commentId, content });
  }
}
