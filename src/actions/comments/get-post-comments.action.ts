'use server';

import { Comment } from '@/types/post.types';
import { structureComments } from '@/lib/comment-utils';
import { db } from '@/lib/db';
import { handleServerError } from '@/lib/error-utils';

export async function getPostComments(postId: string) {
  try {
    const comments = await db.comment.findMany({
      where: {
        postId,
        parentId: null, // Only fetch top-level comments
      },
      include: {
        likes: {
          select: {
            authorId: true,
          },
        },
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
        replies: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
                email: true,
                username: true,
                bio: true,
                urls: true,
              },
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    image: true,
                    email: true,
                    username: true,
                    bio: true,
                    urls: true,
                  },
                },
              },
            },
            likes: {
              select: {
                authorId: true,
              },
            },
            _count: {
              select: {
                likes: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return structureComments(comments as Comment[]);
  } catch (error) {
    throw handleServerError(error, { postId });
  }
}
