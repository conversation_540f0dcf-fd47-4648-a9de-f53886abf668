'use server';

import { revalidatePath } from 'next/cache';
import { createNotification } from '@/actions/notifications/create-notification.action';
import { NotificationType } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function toggleCommentLike(commentId: string) {
  try {
    const user = await getCurrentUser();
    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const existingLike = await db.like.findFirst({
      where: {
        authorId: user.id,
        commentId: commentId,
      },
    });

    let likeAction: 'created' | 'deleted' = 'created';

    if (existingLike) {
      // Unlike
      await db.like.delete({
        where: {
          id: existingLike.id,
        },
      });
      likeAction = 'deleted';
    } else {
      // Like
      await db.like.create({
        data: {
          authorId: user.id,
          commentId: commentId,
        },
      });
    }

    // Get the updated comment
    const updatedComment = await db.comment.findUnique({
      where: {
        id: commentId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        post: {
          select: {
            id: true,
            title: true,
            slug: true,
          },
        },
        _count: {
          select: {
            likes: true,
          },
        },
      },
    });

    // Create notification if a like was created
    if (
      likeAction === 'created' &&
      updatedComment &&
      updatedComment.author.id !== user.id
    ) {
      await createNotification({
        type: NotificationType.LIKE_COMMENT,
        content: '', // No additional content needed for likes
        userId: updatedComment.author.id,
        fromId: user.id,
        postId: updatedComment.post?.id,
        commentId: commentId,
        likeId: existingLike ? existingLike.id : undefined,
      });
    }

    revalidatePath(`/blog/${updatedComment?.post.slug}`);
    revalidatePath('/blog');
    revalidatePath('/posts');
    return updatedComment?._count.likes || 0;
  } catch (error) {
    throw handleServerError(error, { commentId });
  }
}
