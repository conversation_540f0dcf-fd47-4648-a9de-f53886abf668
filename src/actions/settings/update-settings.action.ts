'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function updateUserSettings(data: {
  blogNotifications?: boolean;
  subscriptionNotifications?: boolean;
  marketingNotifications?: boolean;
  marketingEmails?: boolean;
  securityEmails?: boolean;
}) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    const updatedSettings = await db.settings.upsert({
      where: { userId: user.id },
      update: data,
      create: {
        userId: user.id,
        ...data,
      },
    });

    revalidatePath('/settings');
    revalidatePath('/', 'layout');
    return updatedSettings;
  } catch (error) {
    throw handleServerError(error, { data });
  }
}
