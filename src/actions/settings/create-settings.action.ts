'use server';

import { db } from '@/lib/db';
import { handleServerError } from '@/lib/error-utils';

export async function createDefaultUserSettings(userId: string) {
  try {
    const defaultSettings = await db.settings.create({
      data: {
        userId,
        blogNotifications: true,
        subscriptionNotifications: false,
        marketingNotifications: false,
        marketingEmails: true,
        securityEmails: true,
      },
    });
    return defaultSettings;
  } catch (error) {
    handleServerError(error, { userId });
  }
}
