'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getUserSettings() {
  try {
    const user = await getCurrentUser();
    if (!user?.id) {
      throw Errors.Unauthorized();
    }

    const settings = await db.settings.findUnique({
      where: { userId: user.id },
    });

    return settings;
  } catch (error) {
    throw handleServerError(error);
  }
}
