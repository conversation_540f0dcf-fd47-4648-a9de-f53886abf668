'use server';

import { Plan } from '@/types/plan.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';
import { stripe } from '@/lib/stripe';

type PlanInput = Omit<
  Plan,
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'stripePriceIdMonthly'
  | 'stripePriceIdYearly'
  | 'stripeProductId'
>;

export async function createPlan(plan: PlanInput): Promise<Plan> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    const tenant = user?.currentTenantId;

    if (!tenant) {
      throw Errors.ValidationError('No tenant selected');
    }

    const canCreatePlan = permissionService.can({
      user,
      resource: 'plan',
      action: 'create',
    });

    if (!canCreatePlan) {
      throw Errors.Forbidden('You do not have permission to create plans');
    }

    // Create a product in Stripe
    const stripeProduct = await stripe.products.create({
      name: plan.name,
      description: plan.description,
      metadata: {
        tier: plan.tier,
      },
    });

    // Create monthly price in Stripe
    const monthlyPrice = await stripe.prices.create({
      product: stripeProduct.id,
      unit_amount: Math.round(plan.monthlyPrice * 100), // Stripe uses cents
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      lookup_key: `${plan.name}_monthly`,
      metadata: {
        tier: plan.tier,
      },
    });

    // Create yearly price in Stripe
    const yearlyPrice = await stripe.prices.create({
      product: stripeProduct.id,
      unit_amount: Math.round(plan.yearlyPrice * 100), // Stripe uses cents
      currency: 'usd',
      recurring: {
        interval: 'year',
      },
      lookup_key: `${plan.name}_yearly`,
      metadata: {
        tier: plan.tier,
      },
    });

    // Create plan in database
    const newPlan = await db.plan.create({
      data: {
        ...plan,
        stripePriceIdMonthly: monthlyPrice.id,
        stripePriceIdYearly: yearlyPrice.id,
        stripeProductId: stripeProduct.id,
      },
    });

    return newPlan;
  } catch (error) {
    throw handleServerError(error, { plan });
  }
}
