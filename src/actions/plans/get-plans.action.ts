'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma } from '@prisma/client';

import { Plan } from '@/types/plan.types';
import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { db } from '@/lib/db';
import { handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';

export async function getPlans(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<Plan>> {
  try {
    const {
      take = ITEMS_PER_PAGE,
      page = 1,
      orderBy = {},
      filter = {},
    } = params;
    const { searchQuery } = filter;

    const filterWhere: Prisma.PlanWhereInput = {};

    if (searchQuery) {
      filterWhere.OR = [
        {
          name: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
      ];
    }

    const paginationParams: PaginationParams<'plan'> = {
      page,
      take,
      orderBy,
      where: filterWhere,
    };

    const result = await pagePagination(db, 'plan', paginationParams);

    return {
      items: result.items,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
