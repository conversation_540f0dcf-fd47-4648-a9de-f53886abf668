'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';
import { stripe } from '@/lib/stripe';

export async function deletePlan(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    const canDeletePlan = permissionService.can({
      user,
      resource: 'plan',
      action: 'delete',
    });

    if (!canDeletePlan) {
      throw Errors.Forbidden('You do not have permission to delete plans');
    }

    if (!id) {
      throw Errors.ValidationError('Plan ID is required');
    }

    const plan = await db.plan.findUnique({ where: { id } });
    if (!plan) {
      throw Errors.NotFound('Plan');
    }

    // Stripe API doesn't allow to delete resources, only deactivate them
    await stripe.prices.update(plan.stripePriceIdMonthly, {
      active: false,
    });
    await stripe.prices.update(plan.stripePriceIdYearly, {
      active: false,
    });
    await stripe.products.update(plan.stripeProductId, {
      active: false,
    });

    // Delete plan from database
    await db.plan.delete({ where: { id } });

    return { success: true };
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
