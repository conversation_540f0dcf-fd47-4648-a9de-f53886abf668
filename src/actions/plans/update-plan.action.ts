'use server';

import { revalidatePath } from 'next/cache';

import { Plan } from '@/types/plan.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { stripe } from '@/lib/stripe';

type PlanInput = Omit<
  Plan,
  | 'id'
  | 'createdAt'
  | 'updatedAt'
  | 'stripePriceIdMonthly'
  | 'stripePriceIdYearly'
  | 'stripeProductId'
>;

type PlanUpdateInput = Partial<PlanInput>;

export async function updatePlan(
  id: string,
  plan: PlanUpdateInput
): Promise<Plan> {
  try {
    const existingPlan = await db.plan.findUnique({ where: { id } });
    if (!existingPlan) {
      throw Errors.NotFound('Plan');
    }

    // Update the Stripe product if name or description changed
    if (plan.name || plan.description) {
      await stripe.products.update(existingPlan.stripeProductId, {
        name: plan.name || existingPlan.name,
        description: plan.description || existingPlan.description,
      });
    }

    const updatedData: Partial<Plan> = { ...plan };

    // Update monthly price if it's changed
    if (
      plan.monthlyPrice !== undefined &&
      plan.monthlyPrice !== existingPlan.monthlyPrice
    ) {
      const newMonthlyPrice = await stripe.prices.create({
        product: existingPlan.stripeProductId,
        unit_amount: Math.round(plan.monthlyPrice * 100),
        currency: 'usd',
        recurring: { interval: 'month' },
        lookup_key: `${plan.name}_monthly`,
      });
      updatedData.stripePriceIdMonthly = newMonthlyPrice.id;
    }

    // Update yearly price if it's changed
    if (
      plan.yearlyPrice !== undefined &&
      plan.yearlyPrice !== existingPlan.yearlyPrice
    ) {
      const newYearlyPrice = await stripe.prices.create({
        product: existingPlan.stripeProductId,
        unit_amount: Math.round(plan.yearlyPrice * 100),
        currency: 'usd',
        recurring: { interval: 'year' },
        lookup_key: `${plan.name}_yearly`,
      });
      updatedData.stripePriceIdYearly = newYearlyPrice.id;
    }

    // Update plan in database
    const updatedPlan = await db.plan.update({
      where: { id },
      data: updatedData,
    });

    revalidatePath('/plans');
    return updatedPlan;
  } catch (error) {
    throw handleServerError(error, { id, plan });
  }
}
