'use server';

import { AddSubscriptionSchema } from '@/schemas/subscription.schemas';

import { Subscriber } from '@/types/subscriber.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function addSubscriber(email: string): Promise<Subscriber> {
  const isValid = AddSubscriptionSchema.safeParse(email);

  if (!isValid.success) {
    throw Errors.ValidationError('Invalid email');
  }

  const user = await getCurrentUser();
  if (!user) {
    throw Errors.Unauthorized();
  }

  const tenant = user?.currentTenantId;
  if (!tenant) {
    throw Errors.ValidationError('No tenant selected');
  }

  try {
    const newSubscriber = await db.subscriber.create({
      data: {
        email,
        tenant: {
          connect: { id: tenant },
        },
      },
    });

    return newSubscriber;
  } catch (error) {
    throw handleServerError(error, { email });
  }
}
