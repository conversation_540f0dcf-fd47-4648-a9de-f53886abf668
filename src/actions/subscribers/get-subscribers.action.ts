'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma } from '@prisma/client';

import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { Subscriber } from '@/types/subscriber.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';
import { getCurrentUser } from '@/lib/session';

export async function getSubscribers(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<Subscriber>> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const {
      take = ITEMS_PER_PAGE,
      page = 1,
      orderBy = {},
      filter = {},
    } = params;

    const { searchQuery } = filter;

    const filterWhere: Prisma.SubscriberWhereInput = {};

    if (searchQuery) {
      filterWhere.OR = [
        {
          email: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
      ];
    }

    const paginationParams: PaginationParams<'subscriber'> = {
      page,
      take,
      orderBy: orderBy,
      where: filterWhere,
    };

    const result = await pagePagination(db, 'subscriber', paginationParams);

    return {
      items: result.items,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
