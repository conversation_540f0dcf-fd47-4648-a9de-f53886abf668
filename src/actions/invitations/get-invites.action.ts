'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma } from '@prisma/client';

import { Invite } from '@/types/invite.types';
import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function getInvites(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<Invite>> {
  const user = await getCurrentUser();
  const tenant = user?.currentTenantId;

  if (!user) {
    throw Errors.Unauthorized();
  }

  if (!tenant) {
    throw Errors.ValidationError('No tenant selected');
  }

  const canViewInvites = permissionService.can({
    user,
    resource: 'invite',
    action: 'read',
  });

  if (!canViewInvites) {
    throw Errors.Forbidden('You do not have permission to view invites');
  }

  const { take = ITEMS_PER_PAGE, page = 1, orderBy = {}, filter = {} } = params;

  const { searchQuery } = filter;

  const filterWhere: Prisma.InviteWhereInput = {
    tenantId: tenant,
  };

  if (searchQuery) {
    filterWhere.OR = [
      {
        email: {
          contains: searchQuery,
          mode: 'insensitive',
        },
      },
    ];
  }

  const paginationParams: PaginationParams<'invite'> = {
    page,
    take,
    orderBy: orderBy,
    where: filterWhere,
  };

  try {
    const result = await pagePagination(db, 'invite', paginationParams);

    return {
      items: result.items,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
