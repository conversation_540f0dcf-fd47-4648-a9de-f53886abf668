'use server';

import { revalidatePath } from 'next/cache';
import { InviteStatus } from '@prisma/client';

import { Invite } from '@/types/invite.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function updateInviteStatus(
  token: string,
  status: InviteStatus
): Promise<Invite> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const tenant = user?.currentTenantId;
    if (!tenant) {
      throw Errors.ValidationError('No tenant selected');
    }

    const invite = await db.invite.findFirst({
      where: {
        token,
        tenantId: tenant,
      },
    });

    if (!invite) {
      throw Errors.NotFound('Invite');
    }

    if (invite.expiresAt < new Date()) {
      throw Errors.ValidationError('This invitation has expired');
    }

    const updatedInvite = await db.invite.update({
      where: {
        id: invite.id,
      },
      data: { status },
    });

    revalidatePath('/invitations');
    return updatedInvite;
  } catch (error) {
    throw handleServerError(error, { token, status });
  }
}
