'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';

export async function deleteInvite(inviteId: string) {
  try {
    const invite = await db.invite.findUnique({
      where: { id: inviteId },
    });

    if (!invite) {
      throw Errors.NotFound('Invite');
    }

    // Delete the invite
    const deletedInvite = await db.invite.delete({
      where: { id: inviteId },
    });

    return deletedInvite;
  } catch (error) {
    throw handleServerError(error, { inviteId });
  }
}
