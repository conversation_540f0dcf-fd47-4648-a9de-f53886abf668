'use server';

import { sendEmail } from '@/actions/email/resend/send-email.action';
import { InviteStatus, Role } from '@prisma/client';
import { add } from 'date-fns';
import { v4 as uuidv4 } from 'uuid';

import { Invite } from '@/types/invite.types';
import { db } from '@/lib/db';
import { generateInvitationEmail } from '@/lib/emails';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

type InviteInput = {
  email: string;
  role: Role;
};

export async function sendInvites(invites: InviteInput[]): Promise<Invite[]> {
  try {
    const currentUser = await getCurrentUser();
    const tenant = currentUser?.currentTenantId;

    if (!currentUser) {
      throw Errors.Unauthorized();
    }

    if (!tenant) {
      throw Errors.ValidationError('No tenant selected');
    }

    const canSendInvite = permissionService.can({
      user: currentUser,
      resource: 'invite',
      action: 'create',
    });

    if (!canSendInvite) {
      throw new Error('You do not have permission to send invites');
    }

    const hasOwnerRole = invites.some((invite) => invite.role === Role.OWNER);
    if (hasOwnerRole) {
      throw Errors.ValidationError('Cannot send an invite with OWNER role');
    }

    // Check for existing invites in bulk
    const emails = invites.map((invite) => invite.email);
    const existingInvites = await db.invite.findMany({
      where: {
        email: { in: emails },
        senderId: currentUser.id,
        tenantId: tenant,
      },
    });

    if (existingInvites.length > 0) {
      const existingEmails = existingInvites.map((invite) => invite.email);
      throw Errors.ValidationError(
        `Invites already exist for the following emails: ${existingEmails.join(
          ', '
        )}`
      );
    }

    // Create all invites in a single transaction
    const createdInvites = await db.$transaction(
      invites.map(({ email, role }) => {
        const token = uuidv4();
        const expiresAt = add(new Date(), { days: 7 });

        return db.invite.create({
          data: {
            email,
            status: InviteStatus.PENDING,
            token,
            expiresAt,
            role,
            sender: {
              connect: { id: currentUser.id },
            },
            tenant: {
              connect: { id: tenant },
            },
          },
        });
      })
    );

    // Send emails in parallel
    await Promise.all(
      createdInvites.map(async (invite) => {
        const body = await generateInvitationEmail({
          userEmail: invite.email,
          sender: currentUser.name || currentUser.email || '',
          inviteLink: `${process.env.NEXT_PUBLIC_DOMAIN}/invite/accept/${invite.token}`,
        });

        return sendEmail({
          to: [invite.email],
          subject: "You've been invited!",
          body,
        });
      })
    );

    return createdInvites;
  } catch (error) {
    throw handleServerError(error, { invites });
  }
}
