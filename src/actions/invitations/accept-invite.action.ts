'use server';

import { revalidatePath } from 'next/cache';
import { InviteStatus, Role } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';

export async function acceptInvite(
  token: string
): Promise<{ success: boolean; message: string; email?: string }> {
  try {
    // Find the invite
    const invite = await db.invite.findUnique({
      where: { token },
    });

    if (!invite) {
      throw Errors.ValidationError('Invalid invitation token');
    }

    if (invite.expiresAt < new Date()) {
      throw Errors.ValidationError('This invitation has expired');
    }

    if (invite.status !== InviteStatus.PENDING) {
      throw Errors.ValidationError('This invitation has already been used');
    }

    if (invite.role === Role.OWNER) {
      throw Errors.ValidationError('Cannot accept an invite with OWNER role');
    }

    // Update the invite status
    await db.invite.update({
      where: { token },
      data: { status: InviteStatus.ACCEPTED },
    });

    // Check if the user already exists
    let user = await db.user.findUnique({
      where: { email: invite.email },
    });

    if (!user) {
      // Create a new user with the role specified in the invite
      user = await db.user.create({
        data: {
          email: invite.email,
          role: invite.role || Role.COLLABORATOR, // Use invite role or fallback to COLLABORATOR
          currentTenantId: invite.tenantId,
        },
      });
    }

    if (!user.email) {
      throw Errors.ValidationError('User email is missing');
    }

    revalidatePath('/invitations');

    return {
      success: true,
      message: 'Invite accepted successfully. Please sign in to continue.',
      email: user.email,
    };
  } catch (error) {
    throw handleServerError(error, { token });
  }
}
