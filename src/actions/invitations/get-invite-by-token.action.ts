'use server';

import { Invite } from '@/types/invite.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function getInviteByToken(token: string): Promise<Invite> {
  try {
    const user = await getCurrentUser();
    const tenant = user?.currentTenantId;
    if (!tenant) {
      throw Errors.ValidationError('No tenant selected');
    }

    const invite = await db.invite.findFirst({
      where: {
        token,
        tenantId: tenant,
      },
    });

    if (!invite) {
      throw Errors.NotFound('Invite');
    }

    if (invite.expiresAt < new Date()) {
      throw Errors.ValidationError('This invitation has expired');
    }

    return invite;
  } catch (error) {
    throw handleServerError(error, { token });
  }
}
