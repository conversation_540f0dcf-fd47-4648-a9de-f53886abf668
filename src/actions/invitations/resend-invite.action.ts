'use server';

import { revalidatePath } from 'next/cache';
import { sendEmail } from '@/actions/email/resend/send-email.action';
import { InviteStatus } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function resendInvite(inviteId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const invite = await db.invite.findUnique({
      where: { id: inviteId },
    });

    if (!invite) {
      throw Errors.NotFound('Invite');
    }

    if (invite.status !== InviteStatus.PENDING) {
      throw Errors.ValidationError('This invitation has already been used');
    }

    // Update the invite with a new creation date and ensure status is PENDING
    const updatedInvite = await db.invite.update({
      where: { id: inviteId },
      data: {
        createdAt: new Date(),
        status: 'PENDING',
      },
    });

    // Generate an invitation link
    const invitationLink = `${process.env.NEXT_PUBLIC_DOMAIN}/invite?token=${updatedInvite.token}`;

    // Send the invite email
    await sendEmail({
      to: [updatedInvite.email],
      subject: 'Your invitation has been resent',
      body: `Your invitation to join our platform has been resent. Please use the following link to accept the invitation: <a href="${invitationLink}">${invitationLink}</a>`,
    });

    revalidatePath('/invitations');
    return updatedInvite;
  } catch (error) {
    throw handleServerError(error, { inviteId });
  }
}
