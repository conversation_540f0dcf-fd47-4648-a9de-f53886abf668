'use server';

import { nanoid } from 'nanoid';

import { Api<PERSON><PERSON> } from '@/types/api-keys.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

type ApiKeyReturnType = ApiKey & { maskedKey: string };

export async function createApiKey(name: string): Promise<ApiKeyReturnType> {
  const user = await getCurrentUser();
  const tenant = user?.currentTenantId;

  if (!user?.id || !tenant) {
    throw Errors.Unauthorized();
  }

  const canCreateApiKey = await permissionService.can({
    user,
    resource: 'apiKey',
    action: 'create',
  });

  if (!canCreateApiKey) {
    throw Errors.Forbidden('You do not have permission to create API keys');
  }

  const fullKey = `sk_${nanoid(32)}`;

  try {
    const apiKey = await db.apiKey.create({
      data: {
        name,
        key: fullKey,
        user: {
          connect: { id: user.id },
        },
        tenant: {
          connect: { id: tenant },
        },
      },
    });

    return {
      ...apiKey,
      maskedKey: `${apiKey.key.slice(0, 8)}...${apiKey.key.slice(-4)}`,
    };
  } catch (error) {
    throw handleServerError(error, { name });
  }
}
