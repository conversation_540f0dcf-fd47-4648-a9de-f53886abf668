'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma } from '@prisma/client';

import { ApiKey } from '@/types/api-keys.types';
import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function getApiKeys(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<ApiKey>> {
  const user = await getCurrentUser();

  if (!user) {
    throw Errors.Unauthorized();
  }

  const canViewApiKeys = await permissionService.can({
    user,
    resource: 'apiKey',
    action: 'read',
  });

  if (!canViewApiKeys) {
    throw new Error('You do not have permission to view API keys');
  }

  const { take = ITEMS_PER_PAGE, page = 1, orderBy = {}, filter = {} } = params;
  const { searchQuery } = filter;

  const filterWhere: Prisma.ApiKeyWhereInput = {
    userId: user.id,
  };

  if (searchQuery) {
    filterWhere.OR = [
      {
        name: {
          contains: searchQuery,
          mode: 'insensitive',
        },
      },
    ];
  }

  const paginationParams: PaginationParams<'apiKey'> = {
    page,
    take,
    orderBy,
    where: filterWhere,
  };

  try {
    const result = await pagePagination(db, 'apiKey', paginationParams);

    // We should mask the keys here before returning them
    const maskedItems = result.items.map((item: ApiKey) => ({
      ...item,
      key: `${item.key.slice(0, 8)}...${item.key.slice(-4)}`,
    }));

    return {
      items: maskedItems,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
