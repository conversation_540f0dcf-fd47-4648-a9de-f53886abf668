'use server';

import { revalidatePath } from 'next/cache';
import { nanoid } from 'nanoid';

import { ApiKey } from '@/types/api-keys.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

type ApiKeyReturnType = ApiKey & { maskedKey: string };

export async function regenerateApiKey(id: string): Promise<ApiKeyReturnType> {
  const user = await getCurrentUser();
  const tenant = user?.currentTenantId;

  if (!user) {
    throw Errors.Unauthorized();
  }

  if (!tenant) {
    throw Errors.ValidationError('A tenant must be selected');
  }

  const canRegenerateApiKey = await permissionService.can({
    user,
    resource: 'apiKey',
    action: 'update',
  });

  if (!canRegenerateApiKey) {
    throw Errors.Forbidden('You do not have permission to regenerate API keys');
  }

  const fullKey = `sk_${nanoid(32)}`;

  try {
    const updatedApiKey = await db.apiKey.update({
      where: { id, userId: user.id },
      data: { key: fullKey },
    });

    revalidatePath('/api-keys');
    return {
      ...updatedApiKey,
      maskedKey: `${updatedApiKey.key.slice(0, 8)}...${updatedApiKey.key.slice(-4)}`,
    };
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
