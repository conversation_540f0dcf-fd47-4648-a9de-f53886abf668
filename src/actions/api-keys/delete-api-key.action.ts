'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function deleteApiKey(id: string): Promise<void> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!id) {
      throw Errors.ValidationError('API key ID is required');
    }

    const canDeleteApiKey = await permissionService.can({
      user,
      resource: 'apiKey',
      action: 'delete',
    });

    if (!canDeleteApiKey) {
      throw Errors.Forbidden('You do not have permission to delete API keys');
    }

    await db.apiKey.delete({
      where: { id, userId: user.id },
    });
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
