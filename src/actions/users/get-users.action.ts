'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma, Role } from '@prisma/client';

import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { User } from '@/types/user.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function getUsers(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<User>> {
  try {
    const user = await getCurrentUser();
    const tenant = user?.currentTenantId;

    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!tenant) {
      throw Errors.ValidationError('No tenant selected');
    }

    const canManageUsers = permissionService.can({
      user,
      resource: 'user',
      action: 'read',
    });

    if (!canManageUsers) {
      throw Errors.Forbidden('You do not have permission to manage users');
    }

    const {
      take = ITEMS_PER_PAGE,
      page = 1,
      orderBy = {},
      filter = {},
    } = params;

    const { searchQuery } = filter;

    const filterWhere: Prisma.UserWhereInput = {
      id: { not: user.id },
    };

    // If not an owner, filter by tenant
    if (tenant && user.role !== Role.OWNER) {
      filterWhere.tenants = {
        some: {
          tenantId: tenant,
        },
      };
    }

    if (searchQuery) {
      filterWhere.OR = [
        {
          name: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
      ];
    }

    const paginationParams: PaginationParams<'user'> = {
      page,
      take,
      orderBy,
      where: filterWhere,
    };

    const result = await pagePagination(db, 'user', paginationParams);

    return {
      items: result.items,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
