'use server';

import { revalidatePath } from 'next/cache';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function toggleUserStatus(userId: string) {
  try {
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw Errors.Unauthorized();
    }

    if (!userId) {
      throw Errors.ValidationError('User ID is required');
    }

    const canToggleUserStatus = permissionService.can({
      user: currentUser,
      resource: 'user',
      action: 'activate',
    });

    if (!canToggleUserStatus) {
      throw Errors.Forbidden(
        'You do not have permission to change user status'
      );
    }

    const userTenants = await db.tenant.findMany({
      where: {
        ownerId: userId,
      },
    });

    if (userTenants.length > 0) {
      throw Errors.Forbidden('Cannot change status of user with a tenant');
    }

    const user = await db.user.findUnique({
      where: { id: userId },
      select: { isActive: true },
    });

    if (!user) {
      throw Errors.NotFound('User');
    }

    const updatedUser = await db.user.update({
      where: { id: userId },
      data: { isActive: !user.isActive },
    });

    revalidatePath('/users');
    return updatedUser;
  } catch (error) {
    throw handleServerError(error, { userId });
  }
}
