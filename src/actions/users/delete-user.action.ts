'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function deleteUser(userId: string) {
  try {
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw Errors.Unauthorized();
    }

    if (!userId) {
      throw Errors.ValidationError('User ID is required');
    }

    const canDeleteUser = permissionService.can({
      user: currentUser,
      resource: 'user',
      action: 'delete',
    });

    if (!canDeleteUser) {
      throw Errors.Forbidden('You do not have permission to delete users');
    }

    const ownedTenants = await db.tenant.findMany({
      where: { ownerId: userId },
    });

    if (ownedTenants.length > 0) {
      throw Errors.ValidationError('Cannot delete user who owns tenants');
    }

    const deletedUser = await db.user.delete({
      where: { id: userId },
    });

    return deletedUser;
  } catch (error) {
    throw handleServerError(error, { userId });
  }
}
