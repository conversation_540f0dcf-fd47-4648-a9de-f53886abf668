'use server';

import {
  UpdateProfileSchema,
  UpdateProfileValues,
} from '@/schemas/users.schema';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function updateUser(data: UpdateProfileValues) {
  try {
    const isValid = UpdateProfileSchema.safeParse(data);

    if (!isValid.success) {
      throw Errors.ValidationError('Invalid user data');
    }

    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const canUpdateUser = permissionService.can({
      user,
      resource: 'user',
      action: 'update',
    });

    if (!canUpdateUser) {
      throw Errors.Forbidden('You do not have permission to update users');
    }

    const { urls, ...restData } = data;

    const updatedUser = await db.user.update({
      where: { id: user.id },
      data: {
        ...restData,
        urls: urls?.map((url) => url.value),
      },
    });

    return updatedUser;
  } catch (error) {
    throw handleServerError(error, { data });
  }
}
