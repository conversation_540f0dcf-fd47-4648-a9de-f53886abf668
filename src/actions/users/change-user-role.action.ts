'use server';

import { Role } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function changeUserRole(userId: string, newRole: Role) {
  try {
    if (!userId) {
      throw Errors.ValidationError('User ID is required');
    }

    if (!newRole) {
      throw Errors.ValidationError('New role is required');
    }

    const currentUser = await getCurrentUser();

    if (!currentUser) {
      throw Errors.Unauthorized();
    }

    const canChangeRole = permissionService.can({
      user: currentUser,
      resource: 'user',
      action: 'update',
    });

    if (!canChangeRole) {
      throw Errors.Forbidden('You do not have permission to change user roles');
    }

    if (newRole === Role.OWNER) {
      throw Errors.Forbidden('Cannot change user role to OWNER');
    }

    // Check if user has any tenants
    const userTenants = await db.tenant.findMany({
      where: {
        ownerId: userId,
      },
    });

    // If user has any tenants, deny role change
    if (userTenants.length > 0) {
      throw new Error('Cannot change role of user with a tenant');
    }

    const updatedUser = await db.user.update({
      where: { id: userId },
      data: { role: newRole },
    });

    return updatedUser;
  } catch (error) {
    throw handleServerError(error, { userId, newRole });
  }
}
