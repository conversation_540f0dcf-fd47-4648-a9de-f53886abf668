'use server';

import { ITEMS_PER_PAGE } from '@/constants';
import { Prisma } from '@prisma/client';

import { Post } from '@/types/post.types';
import { GetPaginatedParams, GetPaginatedResult } from '@/types/shared.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { pagePagination, PaginationParams } from '@/lib/pagination';
import { getCurrentUser } from '@/lib/session';

export async function getPostsByAuthor(
  params: GetPaginatedParams = {}
): Promise<GetPaginatedResult<Post>> {
  try {
    const {
      take = ITEMS_PER_PAGE,
      page = 1,
      orderBy = {},
      filter = {},
    } = params;
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const { authors, tags, searchQuery } = filter;

    const filterWhere: Prisma.PostWhereInput = {
      authorId: user.id,
    };

    if (authors && authors.length > 0) {
      filterWhere.author = {
        name: {
          in: authors,
        },
      };
    }

    if (tags && tags.length > 0) {
      filterWhere.tags = {
        some: {
          AND: [{ name: { in: tags } }],
        },
      };
    }

    if (searchQuery) {
      filterWhere.OR = [
        {
          title: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          content: {
            path: ['$.content'],
            string_contains: searchQuery,
          },
        },
        {
          excerpt: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          tags: {
            some: {
              AND: [
                {
                  name: {
                    contains: searchQuery,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          },
        },
      ];
    }

    let sortOrder: Prisma.PostOrderByWithRelationInput = {};

    if (typeof orderBy === 'object' && Object.keys(orderBy).length > 0) {
      const [key, direction] = Object.entries(orderBy)[0];
      if (key === 'author.name') {
        sortOrder = { author: { name: direction } };
      } else if (key === '_count.likes' || key === '_count_likes') {
        sortOrder = { likes: { _count: direction } };
      } else if (key === '_count.comments' || key === '_count_comments') {
        sortOrder = { comments: { _count: direction } };
      } else if (key === '_count.bookmarks' || key === '_count_bookmarks') {
        sortOrder = { bookmarks: { _count: direction } };
      } else {
        sortOrder = { [key]: direction };
      }
    }

    const paginationParams: PaginationParams<'post'> = {
      page,
      take,
      orderBy: sortOrder,
      where: filterWhere,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            comments: true,
            likes: true,
            bookmarks: true,
          },
        },
      },
    };

    const result = await pagePagination(db, 'post', paginationParams);

    return {
      items: result.items,
      page: result.page,
      totalPages: result.totalPages,
      totalCount: result.totalCount,
      orderBy: result.orderBy,
      order: result.order,
      filter: searchQuery || '',
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
