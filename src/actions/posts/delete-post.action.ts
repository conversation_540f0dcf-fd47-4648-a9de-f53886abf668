'use server';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';

export async function deletePost(id: string) {
  try {
    if (!id) {
      throw Errors.ValidationError('Post ID is required');
    }

    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const deletedPost = await db.post.delete({
      where: { id, authorId: user.id },
    });

    return deletedPost;
  } catch (error) {
    throw handleServerError(error, { id });
  }
}
