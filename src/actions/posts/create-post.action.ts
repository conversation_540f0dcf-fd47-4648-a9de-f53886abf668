'use server';

import { revalidatePath } from 'next/cache';
import { CreatePostSchema, CreatePostValues } from '@/schemas/posts.schemas';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';
import { safeParse, slugify } from '@/lib/utils';

export async function createPost(data: CreatePostValues) {
  const user = await getCurrentUser();

  if (!user) {
    throw new Error('Unauthorized');
  }

  const isValid = CreatePostSchema.safeParse(data);
  if (!isValid.success) {
    throw Errors.ValidationError('Invalid post data');
  }

  const { tags, ...postData } = data;

  // if no title provided, generate a unique placeholder title
  const title = data.title || `untitled-${Date.now()}`;
  const slug = slugify(title);

  try {
    // First create or get the tags for the current tenant
    const tagConnections = tags
      ? await Promise.all(
          tags.map(async (tag) => {
            const existingTag = await db.tag.upsert({
              where: {
                slug,
              },
              create: {
                name: tag.name,
                slug: tag.slug,
              },
              update: {}, // No updates needed if tag exists
            });
            return existingTag;
          })
        )
      : [];

    const newPost = await db.post.create({
      data: {
        ...postData,
        content: safeParse(data.content),
        title,
        slug,

        author: {
          connect: { id: user.id },
        },
        tags: {
          connect: tagConnections.map((tag) => ({
            id: tag.id,
          })),
        },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
        tags: true,
      },
    });

    revalidatePath('/posts');
    revalidatePath('/blog');
    return newPost;
  } catch (error) {
    console.error('Error creating post:', error);
    throw handleServerError(error, { data });
  }
}
