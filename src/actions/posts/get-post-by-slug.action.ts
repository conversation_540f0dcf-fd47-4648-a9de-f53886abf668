'use server';

import { GetPostResult } from '@/types/post.types';
import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';

export async function getPostBySlug(slug: string): Promise<GetPostResult> {
  try {
    const post = await db.post.findFirst({
      where: {
        slug,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
        tags: true,
        comments: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
                email: true,
                username: true,
                bio: true,
                urls: true,
              },
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    image: true,
                    email: true,
                    username: true,
                    bio: true,
                    urls: true,
                  },
                },
                _count: {
                  select: {
                    likes: true,
                  },
                },
              },
            },
            _count: {
              select: {
                likes: true,
              },
            },
          },
          where: {
            parentId: null, // Only fetch top-level comments
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        likes: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
                email: true,
                username: true,
                bio: true,
                urls: true,
              },
            },
          },
        },
        bookmarks: true,
        _count: {
          select: {
            comments: true,
            likes: true,
            bookmarks: true,
          },
        },
      },
    });

    if (!post) {
      throw Errors.NotFound('Post');
    }

    return post;
  } catch (error) {
    throw handleServerError(error, { slug });
  }
}
