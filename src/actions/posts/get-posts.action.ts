'use server';

import { Prisma } from '@prisma/client';

import { GetPostsParams, Post } from '@/types/post.types';
import { GetCursorPaginationResult } from '@/types/shared.types';
import { db } from '@/lib/db';
import { handleServerError } from '@/lib/error-utils';
import { cursorPagination, PaginationParams } from '@/lib/pagination';

export async function getPosts(
  params: GetPostsParams = {}
): Promise<GetCursorPaginationResult<Post>> {
  try {
    const {
      cursor,
      take = 10,
      orderBy = { createdAt: 'desc' as const },
      filter = {},
    } = params;

    const { authors, tags, searchQuery } = filter;

    const filterWhere: Prisma.PostWhereInput = {
      published: true,
    };

    if (authors && authors.length > 0) {
      filterWhere.author = {
        name: {
          in: authors,
        },
      };
    }

    if (tags && tags.length > 0) {
      filterWhere.tags = {
        some: {
          AND: [{ name: { in: tags } }],
        },
      };
    }

    if (searchQuery) {
      filterWhere.OR = [
        {
          title: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          content: {
            path: ['$.content'],
            string_contains: searchQuery,
          },
        },
        {
          excerpt: {
            contains: searchQuery,
            mode: 'insensitive',
          },
        },
        {
          tags: {
            some: {
              AND: [
                {
                  name: {
                    contains: searchQuery,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          },
        },
      ];
    }

    let sortOrder: Prisma.PostOrderByWithRelationInput = {};

    if (typeof orderBy === 'object' && Object.keys(orderBy).length > 0) {
      const [key, direction] = Object.entries(orderBy)[0];
      if (key === 'author.name') {
        sortOrder = { author: { name: direction } };
      } else if (key === '_count.likes' || key === '_count_likes') {
        sortOrder = { likes: { _count: direction } };
      } else if (key === '_count.comments' || key === '_count_comments') {
        sortOrder = { comments: { _count: direction } };
      } else if (key === '_count.bookmarks' || key === '_count_bookmarks') {
        sortOrder = { bookmarks: { _count: direction } };
      } else {
        sortOrder = { [key]: direction };
      }
    }

    const paginationParams: PaginationParams<'post'> = {
      cursor,
      take,
      orderBy: sortOrder,
      where: filterWhere,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
        tags: true,
        _count: {
          select: {
            comments: true,
            likes: true,
            bookmarks: true,
          },
        },
        bookmarks: true,
      },
    };

    const result = await cursorPagination(db, 'post', paginationParams);

    return {
      items: result.items,
      nextCursor: result.nextCursor,
      totalCount: result.totalCount,
      hasMore: result.hasMore,
    };
  } catch (error) {
    throw handleServerError(error, { params });
  }
}
