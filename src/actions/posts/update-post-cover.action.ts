'use server';

import { updateFile } from '@/actions/upload';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

export async function updateBlogCover(postId: string, formData: FormData) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    // Check if the post belongs to the current user and tenant
    const post = await db.post.findFirst({
      where: {
        id: postId,
        authorId: user.id,
      },
      select: {
        authorId: true,
        cover: true,
      },
    });

    if (!post) {
      throw Errors.NotFound('Post');
    }

    const canUpdatePost = permissionService.can({
      user,
      resource: 'post',
      action: 'update',
    });

    if (!canUpdatePost) {
      throw Errors.Forbidden('You do not have permission to update this post');
    }

    // Update the file
    const { url } = await updateFile(post.cover || '', formData);

    // Update the post with the new cover image URL
    const updatedPost = await db.post.update({
      where: { id: postId, authorId: user.id },
      data: {
        cover: url,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return updatedPost;
  } catch (error) {
    throw handleServerError(error, { postId, formData });
  }
}
