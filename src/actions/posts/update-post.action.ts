'use server';

import { revalidatePath } from 'next/cache';
import { CreatePostSchema, CreatePostValues } from '@/schemas/posts.schemas';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';
import { getCurrentUser } from '@/lib/session';
import { safeParse, slugify } from '@/lib/utils';

export async function updatePost(slug: string, data: CreatePostValues) {
  try {
    const isValid = CreatePostSchema.safeParse(data);
    if (!isValid.success) {
      throw Errors.ValidationError('Invalid post data');
    }

    if (!slug) {
      throw Errors.ValidationError('Slug not found in the URL');
    }

    const user = await getCurrentUser();

    if (!user) {
      throw Errors.Unauthorized();
    }

    const existingPost = await db.post.findFirst({
      where: {
        slug,
      },
      include: {
        author: true,
        tags: true,
      },
    });

    if (!existingPost) {
      throw Errors.NotFound('Post');
    }

    const {
      tags,
      cover: newCover,
      title: newTitle,
      content: newContent,
      ...rest
    } = data;

    // Handle cover update, keep existing if not provided
    const cover = newCover || existingPost.cover;

    // Handle title and slug update
    const title = newTitle || existingPost.title;
    let newSlug = existingPost.slug;
    if (newTitle && newTitle !== existingPost.title) {
      newSlug = slugify(title);
      // Check if the new slug is already taken within the same tenant
      const slugExists = await db.post.findFirst({
        where: {
          slug: newSlug,
          id: { not: existingPost.id },
        },
      });
      if (slugExists) {
        newSlug = `${newSlug}-${Date.now()}`;
      }
    }

    // Prepare data object for update, only update fields that are provided
    const updateData: any = {};

    if (title) updateData.title = title;
    if (newSlug) updateData.slug = newSlug;
    if (newContent) updateData.content = safeParse(newContent);
    if (cover) updateData.cover = cover;

    // Ensure additional fields (rest) are updated only if provided
    Object.keys(rest).forEach((key) => {
      if (rest[key as keyof typeof rest] !== undefined) {
        updateData[key as keyof typeof rest] = rest[key as keyof typeof rest];
      }
    });

    // Handle tags, remove existing and connect new ones if provided
    if (tags) {
      // First, create or update tags within the current tenant
      const tagConnections = await Promise.all(
        tags.map(async (tag) => {
          const existingTag = await db.tag.upsert({
            where: {
              slug: tag.slug,
            },
            create: {
              name: tag.name,
              slug: tag.slug,
            },
            update: {}, // No updates needed if tag exists
          });
          return existingTag;
        })
      );

      updateData.tags = {
        set: [], // Clear existing tags
        connect: tagConnections.map((tag) => ({
          id: tag.id,
        })),
      };
    }

    const updatedPost = await db.post.update({
      where: { id: existingPost.id, authorId: user.id }, // Use ID instead of slug since slug might change
      data: { ...updateData, updatedAt: new Date() },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
            username: true,
            bio: true,
            urls: true,
          },
        },
        tags: true,
      },
    });

    revalidatePath('/posts');
    revalidatePath('/blog');
    return updatedPost;
  } catch (error) {
    throw handleServerError(error, { slug, data });
  }
}
