'use server';

import { Post } from '@prisma/client';

import { db } from '@/lib/db';
import { Errors, handleServerError } from '@/lib/error-utils';

export async function getRelatedPosts(
  postId: string,
  tagIds: string[],
  limit: number = 2
): Promise<Post[]> {
  try {
    if (!postId || !tagIds || tagIds.length === 0) {
      throw Errors.ValidationError('Post ID and tag IDs are required');
    }

    const relatedPosts = await db.post.findMany({
      where: {
        id: { not: postId },
        tags: {
          some: {
            id: { in: tagIds },
          },
        },
        published: true,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            comments: true,
            likes: true,
            bookmarks: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    return relatedPosts;
  } catch (error) {
    throw handleServerError(error, { postId, tagIds });
  }
}
