'use server';

import { Errors, handleServerError } from '@/lib/error-utils';
import { fromEmail, resend } from '@/lib/resend';
import { getCurrentUser } from '@/lib/session';

type SendEmailResult = {
  success: boolean;
  messageId?: string;
};

export const sendEmail = async ({
  to,
  subject,
  body,
}: {
  body: string;
  subject: string;
  to: string[];
}): Promise<SendEmailResult> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (!process.env.EMAIL_FROM || !process.env.AUTH_RESEND_KEY) {
      throw Errors.ValidationError(
        "Missing EMAIL_FROM or AUTH_RESEND_KEY. Don't forget to add that to your .env file."
      );
    }

    // In development, only log the email
    if (process.env.NODE_ENV === 'development') {
      console.log('\n=== Email Details ===');
      console.log('To:', to);
      console.log('From:', fromEmail);
      console.log('Subject:', subject);
      console.log('Body:', body);
      console.log('=====================\n');

      return {
        success: true,
        messageId: 'dev-mode-fake-id',
      };
    }

    // In production, send the actual email

    const result = await resend.emails.send({
      from: fromEmail as string,
      to,
      subject,
      html: body,
    });

    return {
      success: true,
      messageId: result.data?.id,
    };
  } catch (error) {
    throw handleServerError(error, { to, subject, body });
  }
};
