'use server';

import { Errors, handleServerError } from '@/lib/error-utils';
import { client, fromEmail, SendEmailCommand } from '@/lib/SES';
import { getCurrentUser } from '@/lib/session';

type SendEmailResult = {
  success: boolean;
  messageId?: string;
};

export const sendEmail = async ({
  to,
  subject,
  body,
}: {
  to: string;
  subject: string;
  body: string;
}): Promise<SendEmailResult> => {
  try {
    const user = await getCurrentUser();
    if (!user) {
      throw Errors.Unauthorized();
    }

    if (
      !process.env.AWS_ACCESS_KEY_ID ||
      !process.env.AWS_SECRET_ACCESS_KEY ||
      !process.env.AWS_REGION ||
      !process.env.EMAIL_FROM
    ) {
      throw Errors.ValidationError(
        "Missing  AWS_REGION, AWS_ACCESS_KEY_ID, EMAIL_FROM, or AWS_SECRET_ACCESS_KEY. Don't forget to add that to your .env file."
      );
    }

    const params = {
      Source: fromEmail,
      Destination: {
        ToAddresses: [to],
      },
      Message: {
        Subject: {
          Data: subject,
        },
        Body: {
          Html: {
            Charset: 'UTF-8',
            Data: body,
          },
        },
      },
    };

    const command = new SendEmailCommand(params);

    const result = await client.send(command);
    console.log(`Email sent successfully to ${to}`, {
      messageId: result.MessageId,
    });
    return { success: true, messageId: result.MessageId };
  } catch (error) {
    throw handleServerError(error, { to, subject, body });
  }
};
