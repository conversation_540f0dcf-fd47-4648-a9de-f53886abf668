export const REDIRECT_URL = '/dashboard';
export const REDIRECT_ONBOARDING_URL = '/onboarding';

export const PUBLIC_ROUTES = [
  '/blog',
  '/blog/[slug]',
  '/docs',
  '/docs/[[...slug]]',
  '/privacy',
  '/terms',
  '/api/webhooks/stripe',
];

export const PRIVATE_ROUTES = [
  '/dashboard',
  '/posts',
  '/settings',
  '/notifications',
  '/users',
  '/invitations',
  '/api-keys',
  '/billing',
  '/subscribers',
  '/onboarding', // Added onboarding as a private route
];

export const AUTH_ROUTES = ['/signin', '/invite', '/verify-request'];

export const API_AUTH_PREFIX = '/api/auth';

export enum Role {
  OWNER = 'OWNER',
  USER = 'USER',
  ADMIN = 'ADMIN',
  COLLABORATOR = 'COLLABORATOR',
  GUEST = 'GUEST',
}

export const FREE_PLAN = {
  name: 'Free',
  description:
    'This is perfect for testing out the app. No credit card required. ',
  features: [],
  monthlyPrice: 0,
  yearlyPrice: 0,
  stripePriceIdMonthly: '',
  stripePriceIdYearly: '',
  stripeProductId: '',
  stripeCustomerId: null,
  stripeSubscriptionId: null,
  stripePriceId: null,
  stripeCurrentPeriodEnd: null,
  isSubscribed: false,
  isCanceled: false,
};

export const ITEMS_PER_PAGE = 6;
