'use client';

import React, { createContext, useContext } from 'react';
import { getNotifications } from '@/actions/notifications/get-notifications.action';
import { useQuery } from '@tanstack/react-query';

import { Notification } from '@/types/notification.types';

type Props = {
  children: React.ReactNode;
};

type NotificationContextTypes = {
  notifications: Notification[] | undefined;
  refetch: () => void;
  error: Error | null;
  isLoading: boolean;
};

const defaultValues: NotificationContextTypes = {
  notifications: [],
  refetch: () => {},
  error: null,
  isLoading: false,
};
const NotificationContext =
  createContext<NotificationContextTypes>(defaultValues);

export const NotificationProvider = ({ children }: Props) => {
  const {
    data: notifications,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => getNotifications(),
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const value: NotificationContextTypes = {
    notifications,
    refetch,
    error,
    isLoading,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);
