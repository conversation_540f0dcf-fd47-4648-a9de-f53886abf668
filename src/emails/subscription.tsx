import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

export interface SubscriptionEmailProps {
  userName: string;
  planName: string;
  billingPeriod: string;
  amount: string;
  nextBillingDate: string;
}

const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'https://saastarter.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';

const manageBillingUrl = `${baseUrl}/billing`;
export const SubscriptionEmail: React.FC<SubscriptionEmailProps> = ({
  userName,
  planName,
  billingPeriod,
  amount,
  nextBillingDate,
}) => {
  const previewText = `Thank you for your subscription to ${planName}!`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 my-auto mx-auto font-sans px-2">
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px] bg-white">
            <Section className="mt-[32px]">
              <Img
                src={logoUrl}
                width="140"
                height="20"
                alt="Saastarter Logo"
                className="my-0 mx-auto"
              />
            </Section>
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Subscription <strong>Confirmed</strong>
            </Heading>
            <Text className="text-black text-[14px] leading-[24px]">
              Hello {userName},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              Thank you for subscribing to our <strong>{planName}</strong> plan.
              Your subscription is now active!
            </Text>
            <Text className="text-black text-[14px] leading-[24px] mt-[24px]">
              Here are the details of your subscription:
            </Text>
            <Section className="bg-gray-50 rounded p-[12px] mt-[12px]">
              <Text className="text-black text-[14px] leading-[24px] m-0">
                <strong>Plan:</strong> {planName}
              </Text>
              <Text className="text-black text-[14px] leading-[24px] m-0">
                <strong>Billing Period:</strong> {billingPeriod}
              </Text>
              <Text className="text-black text-[14px] leading-[24px] m-0">
                <strong>Amount:</strong> {amount}
              </Text>
              <Text className="text-black text-[14px] leading-[24px] m-0">
                <strong>Next Billing Date:</strong> {nextBillingDate}
              </Text>
            </Section>
            <Text className="text-black text-[14px] leading-[24px] mt-[24px]">
              You can manage your subscription, update your payment method, or
              cancel at any time by visiting your account settings.
            </Text>
            <Section className="text-center mt-[32px] mb-[32px]">
              <Button
                className="bg-[#000000] rounded text-white text-[12px] font-semibold no-underline text-center px-5 py-3"
                href={manageBillingUrl}
              >
                Manage Billing
              </Button>
            </Section>
            <Hr className="border border-solid border-[#eaeaea] my-[26px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you have any questions or need assistance, please don&apos;t
              hesitate to{' '}
              <Link
                href={`${baseUrl}/contact`}
                className="text-blue-600 no-underline"
              >
                contact our support team
              </Link>
              .
            </Text>
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              Thank you for choosing our service!
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};
