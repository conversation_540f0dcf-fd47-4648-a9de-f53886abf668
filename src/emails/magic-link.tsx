import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

export interface MagicLinkEmailProps {
  userEmail: string;
  magicLink: string;
  expirationTime?: string;
}

const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'https://saastarter.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';
export const MagicLinkEmail: React.FC<MagicLinkEmailProps> = ({
  userEmail,
  magicLink,
  expirationTime = '24 hours',
}) => {
  const previewText = `Your magic link to sign in`;
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 my-auto mx-auto font-sans px-2">
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px] bg-white">
            <Section className="mt-[32px]">
              <Img
                src={logoUrl}
                height="20"
                alt="Saastarter Logo"
                className="my-0 mx-auto"
              />
            </Section>
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Sign in to <strong>Your Account</strong>
            </Heading>
            <Text className="text-black text-[14px] leading-[24px]">
              Hello {userEmail},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              You requested a magic link to sign in to your account. Click the
              button below to sign in:
            </Text>
            <Section className="text-center mt-[32px] mb-[32px]">
              <Button
                className="bg-[#000000] rounded text-white text-[12px] font-semibold no-underline text-center px-5 py-3"
                href={magicLink}
              >
                Sign In
              </Button>
            </Section>

            <Hr className="border border-solid border-[#eaeaea] my-[26px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              This magic link was intended for{' '}
              <span className="text-black">{userEmail}</span>. If you
              didn&apos;t request this link, you can safely ignore this email.
              The link will expire in {expirationTime} for security reasons.
            </Text>
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you have any questions, please{' '}
              <Link
                href={`${baseUrl}/contact`}
                className="text-blue-600 no-underline"
              >
                contact our support team
              </Link>
              .
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default MagicLinkEmail;
