import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

export interface NewsletterConfirmationEmailProps {
  userEmail: string;
}

const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'https://yourdomain.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';

export const NewsletterConfirmationEmail: React.FC<
  NewsletterConfirmationEmailProps
> = ({ userEmail }) => {
  const previewText = `Confirm your newsletter subscription`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 my-auto mx-auto font-sans px-2">
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px] bg-white">
            <Section className="mt-[32px]">
              <Img
                src={logoUrl}
                width="140"
                height="20"
                alt="Saastarter Logo"
                className="my-0 mx-auto"
              />
            </Section>
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Confirm Your <strong>Subscription</strong>
            </Heading>
            <Text className="text-black text-[14px] leading-[24px]">
              Hello {userEmail},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              Thank you for subscribing to our newsletter! We&apos;re excited to
              have you join our community. To start receiving updates, news, and
              exclusive content, please confirm your subscription by clicking
              the button below:
            </Text>
            <Section className="text-center mt-[32px] mb-[32px]">
              <Button
                className="bg-[#000000] rounded text-white text-[12px] font-semibold no-underline text-center px-5 py-3"
                href={`${baseUrl}/confirm-subscription?email=${userEmail}`}
              >
                Confirm Subscription
              </Button>
            </Section>
            <Text className="text-black text-[14px] leading-[24px]">
              If you didn&apos;t request this subscription, you can safely
              ignore this email.
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              If you ever want to unsubscribe, you can do so by clicking this
              link:{' '}
              <Link
                href={`${baseUrl}/unsubscribe?email=${userEmail}`}
                className="text-blue-600 no-underline"
              >
                Unsubscribe
              </Link>
            </Text>
            <Hr className="border border-solid border-[#eaeaea] my-[26px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              This email was sent to{' '}
              <span className="text-black">{userEmail}</span>. If you have any
              questions or concerns, please{' '}
              <Link
                href={`${baseUrl}/contact`}
                className="text-blue-600 no-underline"
              >
                contact our support team
              </Link>
              .
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};
