import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

export interface UserInvitationEmailProps {
  userEmail: string;
  sender: string;
  inviteLink: string;
}

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://saastarter.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';
export const UserInvitationEmail: React.FC<UserInvitationEmailProps> = ({
  userEmail,
  sender,
  inviteLink,
}) => {
  const previewText = `You've been invited to join our app!`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 my-auto mx-auto font-sans px-2">
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px] bg-white">
            <Section className="mt-[32px]">
              <Img
                src={logoUrl}
                width="140"
                height="20"
                alt="Saastarter Logo"
                className="my-0 mx-auto"
              />
            </Section>
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              You&apos;re <strong>Invited</strong>
            </Heading>
            <Text className="text-black text-[14px] leading-[24px]">
              Hello {userEmail},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              You&apos;ve been invited by <strong>{sender}</strong> to join our
              app. We&apos;re excited to have you on board!
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              To get started, please click the button below to log in:
            </Text>
            <Section className="text-center mt-[32px] mb-[32px]">
              <Button
                className="bg-[#000000] rounded text-white text-[12px] font-semibold no-underline text-center px-5 py-3"
                href={inviteLink}
              >
                Log In
              </Button>
            </Section>

            <Hr className="border border-solid border-[#eaeaea] my-[26px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you have any questions or need assistance, please don&apos;t
              hesitate to{' '}
              <Link
                href={`${baseUrl}/contact`}
                className="text-blue-600 no-underline"
              >
                contact our support team
              </Link>
              .
            </Text>
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              Welcome aboard!
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default UserInvitationEmail;
