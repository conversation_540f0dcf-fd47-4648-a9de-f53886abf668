import * as React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

export interface NewsletterEmailProps {
  userEmail: string;
  subscriptionDate?: string;
}

const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'https://saastarter.com';
const logoUrl = 'https://saasboilerplate.s3.amazonaws.com/logo.png';

export const NewsletterEmail: React.FC<NewsletterEmailProps> = ({
  userEmail,
  subscriptionDate = new Date().toLocaleDateString(),
}) => {
  const previewText = `Thank you for subscribing to our newsletter!`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 my-auto mx-auto font-sans px-2">
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px] bg-white">
            <Section className="mt-[32px]">
              <Img
                src={logoUrl}
                width="140"
                height="20"
                alt="Saastarter Logo"
                className="my-0 mx-auto"
              />
            </Section>
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Welcome to Our <strong>Newsletter</strong>
            </Heading>
            <Text className="text-black text-[14px] leading-[24px]">
              Hello {userEmail},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              Thank you for subscribing to our newsletter! We&apos;re excited to
              have you join our community. You&apos;ll now receive updates,
              news, and exclusive content directly to your inbox.
            </Text>

            <Text className="text-black text-[14px] leading-[24px]">
              If you ever want to unsubscribe, you can do so by clicking this
              link:{' '}
              <Link
                href={`${baseUrl}/unsubscribe?email=${userEmail}`}
                className="text-blue-600 no-underline"
              >
                Unsubscribe
              </Link>
            </Text>
            <Hr className="border border-solid border-[#eaeaea] my-[26px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              This email was sent to{' '}
              <span className="text-black">{userEmail}</span>. You received this
              email because you subscribed to our newsletter on{' '}
              {subscriptionDate}. If you didn&apos;t subscribe to this
              newsletter, please ignore this email or{' '}
              <Link
                href={`${baseUrl}/contact`}
                className="text-blue-600 no-underline"
              >
                contact our support team
              </Link>
              .
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};
