import Image, { ImageProps } from 'next/image';
import Link, { LinkProps } from 'next/link';
import type { MDXComponents } from 'mdx/types';

import CopyCodeSnippet from '@/components/docs/custom/copy-code-snippet';

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    img: ({ alt, ...props }) => (
      <Image
        sizes="100vw"
        style={{ width: '100%', height: 'auto' }}
        {...(props as ImageProps)}
        alt={alt as string}
      />
    ),
    a: ({ href, ...props }) => (
      <Link {...(props as LinkProps)} href={href as LinkProps['href']} />
    ),
    pre: ({ children, ...props }) => {
      return (
        <CopyCodeSnippet>
          <pre {...props}>{children}</pre>
        </CopyCodeSnippet>
      );
    },

    ...components,
  };
}
