import { createDefaultUserSettings } from '@/actions/settings';
import { PrismaAdapter } from '@auth/prisma-adapter';
import { Role } from '@prisma/client';
import NextAuth from 'next-auth';

import { db } from '@/lib/db';

import authConfig from './auth.config';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  // debug: process.env.NODE_ENV === 'development',
  pages: {
    signIn: '/signin',
    error: '/error',
    verifyRequest: '/verify-request',
    // newUser: '/onboarding',
  },
  adapter: PrismaAdapter(db),

  callbacks: {
    async session({ session, user }) {
      if (session.user) {
        session.user.currentTenantId = session.user.currentTenantId;
        session.user.id = user.id;
        session.user.role = user.role;
      }
      return session;
    },
  },

  events: {
    async linkAccount({ user }) {
      // set emailVerified to true
      if (user.id) {
        await db.user.update({
          where: { id: user.id },
          data: {
            emailVerified: new Date(),
          },
        });
      }
    },
    async signIn({ user, isNewUser }) {
      if (user.id) {
        if (isNewUser) {
          // Check if it's the first user
          const userCount = await db.user.count();

          await db.user.update({
            where: { id: user.id },
            data: {
              // If it's the first user, make them OWNER, otherwise use the session role or default to ADMIN
              role: userCount === 1 ? Role.OWNER : Role.USER,
            },
          });
          await createDefaultUserSettings(user.id);
        }
      }
    },
  },
  trustHost: true,
  ...authConfig,
});
