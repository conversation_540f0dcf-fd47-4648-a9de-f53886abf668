import type { Metadata } from 'next';
import { Inter, League_Spartan } from 'next/font/google';

import { Toaster } from '@/components/ui/sonner';
import Providers from '@/components/providers';

import '@/styles/globals.css';
import '@/styles/prosemirror.css';
import 'highlight.js/styles/tokyo-night-dark.css';

import { getUserSettings } from '@/actions/settings';
import { getTenantsByUser } from '@/actions/tenant';
import { getLocale, getMessages } from 'next-intl/server';

import { getCurrentUser } from '@/lib/session';

const inter = Inter({ subsets: ['latin'], variable: '--inter-font' });
const leagueSpartan = League_Spartan({
  subsets: ['latin', 'latin-ext'],
  variable: '--league-spartan-font',
});

export const metadata: Metadata = {
  title: 'saastarter',
  description: 'A saas starter boilerplate for Next.js',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  ),
};
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [messages, locale, user] = await Promise.all([
    getMessages(),
    getLocale(),
    getCurrentUser(),
  ]);

  const tenants = user ? await getTenantsByUser() : [];
  const settings = user ? await getUserSettings() : null;

  const initialTenant =
    tenants.find((t) => t.id === user?.currentTenantId) || null;

  return (
    <html
      className={`${inter.className} ${leagueSpartan.variable}`}
      lang={locale}
      suppressHydrationWarning
    >
      <body>
        <Providers
          settings={settings}
          messages={messages}
          locale={locale}
          initialTenant={initialTenant}
          tenants={tenants}
        >
          {children}
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}
