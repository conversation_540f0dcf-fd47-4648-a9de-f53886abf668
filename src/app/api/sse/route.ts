import { NextResponse } from 'next/server';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function GET(req: Request) {
  const encoder = new TextEncoder();
  const user = await getCurrentUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Connection timeout after 30 minutes
  const connectionTimeout = 30 * 60 * 1000;

  // Max concurrent connections per user
  const MAX_CONNECTIONS = 3;
  let activeConnections = 0;

  const stream = new ReadableStream({
    async start(controller) {
      if (activeConnections >= MAX_CONNECTIONS) {
        controller.enqueue(
          encoder.encode(
            `data: ${JSON.stringify({
              type: 'ERROR',
              message: 'Too many connections',
            })}\n\n`
          )
        );
        controller.close();
        return;
      }

      activeConnections++;
      let isStreamClosed = false;

      // Setup connection timeout with proxy compatibility
      const proxyTimeout = 90 * 1000; // 90 seconds (common proxy timeout)
      const timeoutId = setTimeout(
        () => {
          isStreamClosed = true;
          controller.enqueue(
            encoder.encode(
              `data: ${JSON.stringify({
                type: 'TIMEOUT',
                message: 'Connection timeout',
              })}\n\n`
            )
          );
          controller.close();
          clearInterval(intervalId);
        },
        Math.min(connectionTimeout, proxyTimeout)
      );

      // Send keepalive every 30 seconds
      const keepaliveInterval = setInterval(() => {
        if (!isStreamClosed) {
          try {
            controller.enqueue(encoder.encode(':keepalive\n\n'));
          } catch (error) {
            console.error('Error sending keepalive:', error);
            clearInterval(keepaliveInterval);
          }
        }
      }, 30000);

      function sendEvent(data: unknown) {
        if (!isStreamClosed) {
          try {
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(data)}\n\n`)
            );
          } catch (error) {
            console.error('Error sending SSE event:', error);
            isStreamClosed = true;
            controller.close();
            clearInterval(intervalId);
          }
        }
      }

      let lastNewNotificationTime = new Date(0);
      let lastReadNotificationTime = new Date(0);

      const intervalId = setInterval(async () => {
        if (isStreamClosed) {
          clearInterval(intervalId);
          return;
        }
        try {
          // Fetch new notifications
          const newNotifications = await db.notification.findMany({
            where: {
              userId: user.id,
              createdAt: {
                gt: lastNewNotificationTime,
              },
            },
            orderBy: {
              createdAt: 'asc',
            },
            include: {
              from: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  email: true,
                  username: true,
                },
              },
              post: {
                select: {
                  id: true,
                  slug: true,
                  title: true,
                },
              },
            },
          });

          if (newNotifications.length > 0) {
            const transformedNotifications = newNotifications.map((notif) => ({
              ...notif,
              createdAt: notif.createdAt.toISOString(),
              updatedAt: notif.updatedAt.toISOString(),
              post: notif.post || null,
            }));

            sendEvent({
              type: 'NEW_NOTIFICATIONS',
              data: transformedNotifications,
            });
            lastNewNotificationTime = new Date(
              newNotifications[newNotifications.length - 1].createdAt
            );
          }

          // Fetch notifications marked as read since last check
          const readNotifications = await db.notification.findMany({
            where: {
              userId: user.id,
              isRead: true,
              updatedAt: {
                gt: lastReadNotificationTime,
              },
            },
            orderBy: {
              updatedAt: 'asc',
            },
            select: {
              id: true,
              updatedAt: true,
            },
          });

          if (readNotifications.length > 0) {
            const transformedReadNotifications = readNotifications.map(
              (notif) => ({
                id: notif.id,
              })
            );

            sendEvent({
              type: 'READ_NOTIFICATIONS',
              data: transformedReadNotifications,
            });
            lastReadNotificationTime = new Date(
              readNotifications[readNotifications.length - 1].updatedAt
            );
          }
        } catch (error) {
          console.error('Error fetching notifications:', error);
        }
      }, 5000);

      // Handle client disconnects
      req.signal.addEventListener('abort', () => {
        console.log('Client disconnected');
        isStreamClosed = true;
        activeConnections--;
        clearTimeout(timeoutId);
        clearInterval(keepaliveInterval);
        controller.close();
        clearInterval(intervalId);
      });

      // Cleanup on stream close
      const originalClose = controller.close.bind(controller);
      controller.close = () => {
        isStreamClosed = true;
        activeConnections--;
        clearTimeout(timeoutId);
        clearInterval(keepaliveInterval);
        originalClose();
        clearInterval(intervalId);
      };

      // Log connection metrics
      console.log(
        `New SSE connection established. Active connections: ${activeConnections}`
      );
    },
  });

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
    },
  });
}
