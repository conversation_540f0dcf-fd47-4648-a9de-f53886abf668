import { NextResponse } from 'next/server';

import { db } from '@/lib/db';
import { getCurrentUser } from '@/lib/session';

export async function GET() {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenants = await db.tenantsOnUsers.findMany({
      where: {
        userId: user.id,
      },
      include: {
        tenant: true,
      },
    });

    return NextResponse.json({ tenants });
  } catch (error) {
    console.error('[TENANTS_GET]', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
