import { headers } from 'next/headers';
import { createSubscriptionNotification } from '@/actions/notifications/create-subscription-notification.action';
import { Role } from '@prisma/client';
import <PERSON><PERSON> from 'stripe';

import { db } from '@/lib/db';
import { stripe } from '@/lib/stripe';

async function handleCheckoutSessionCompleted(event: Stripe.Event) {
  const session = event.data.object as Stripe.Checkout.Session;
  const subscription = await stripe.subscriptions.retrieve(
    session.subscription as string
  );

  const user = await db.user.update({
    where: { id: session.metadata?.userId },
    data: {
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: subscription.customer as string,
      stripePriceId: subscription.items.data[0].price.id,
      stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      subscriptionStatus: 'active',
      role: Role.ADMIN, // Update user role to ADMIN when they subscribe
    },
  });

  // Create notification for owner users
  const planName = subscription.items.data[0].price.nickname || 'Unknown Plan';
  await createSubscriptionNotification(
    user.id,
    user.username || user.name || user.email || 'A user',
    planName
  );
}

async function handleInvoicePaymentSucceeded(event: Stripe.Event) {
  const invoice = event.data.object as Stripe.Invoice;
  const subscription = await stripe.subscriptions.retrieve(
    invoice.subscription as string
  );

  await db.user.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      stripePriceId: subscription.items.data[0].price.id,
      stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      subscriptionStatus: 'active',
    },
  });
}

async function handleCustomerSubscriptionUpdated(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;

  await db.user.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      stripePriceId: subscription.items.data[0].price.id,
      stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      subscriptionStatus: subscription.status,
      // Keep ADMIN role if subscription is active, revert to USER if not
      role: subscription.status === 'active' ? Role.ADMIN : Role.USER,
    },
  });
}

async function handleCustomerSubscriptionDeleted(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;

  await db.user.update({
    where: { stripeSubscriptionId: subscription.id },
    data: {
      stripeSubscriptionId: null,
      stripePriceId: null,
      stripeCurrentPeriodEnd: null,
      subscriptionStatus: 'canceled',
      role: Role.USER, // Revert back to USER role when subscription is canceled
    },
  });
}

export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get('Stripe-Signature') as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET as string
    );
    console.log('Webhook received:', event);
  } catch (error) {
    return new Response(`Webhook Error: ${(error as Error).message}`, {
      status: 400,
    });
  }

  // Idempotency check
  const eventId = event.id;
  const existingEvent = await db.processedEvent.findUnique({
    where: { id: eventId },
  });
  if (existingEvent) {
    console.log(`Event ${eventId} already processed`);
    return new Response('Event already processed', { status: 200 });
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event);
        break;
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event);
        break;
      case 'customer.subscription.updated':
        await handleCustomerSubscriptionUpdated(event);
        break;
      case 'customer.subscription.deleted':
        await handleCustomerSubscriptionDeleted(event);
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    // Mark event as processed
    await db.processedEvent.create({ data: { id: eventId } });

    console.log(`Webhook processed successfully: ${event.type}`);
    return new Response('Received', { status: 200 });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response('Webhook handler failed', { status: 500 });
  }
}
