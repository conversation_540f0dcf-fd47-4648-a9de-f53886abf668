import { NextRequest, NextResponse } from 'next/server';
import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';
import { SigninSchema, SigninValues } from '@/schemas/auth.schemas';

export async function POST(request: NextRequest) {
  try {
    const body: SigninValues = await request.json();
    const isValid = SigninSchema.safeParse(body);

    if (!isValid.success) {
      return NextResponse.json(
        { error: 'Email is not valid!' },
        { status: 400 }
      );
    }

    const redirectUrl =
      request.nextUrl.searchParams.get('redirectUrl') || REDIRECT_URL;

    await signIn('email', {
      email: body.email,
      redirectTo: redirectUrl,
    });

    return NextResponse.json({ success: `Email sent to ${body.email}!` });
  } catch (error) {
    console.error('Error signing in:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
