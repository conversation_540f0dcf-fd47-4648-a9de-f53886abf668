import { NextRequest, NextResponse } from 'next/server';
import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';

export async function POST(request: NextRequest) {
  try {
    const { provider } = await request.json();

    if (provider !== 'google' && provider !== 'github') {
      return NextResponse.json({ error: 'Invalid provider' }, { status: 400 });
    }

    const redirectUrl =
      request.nextUrl.searchParams.get('redirectUrl') || REDIRECT_URL;

    await signIn(provider, {
      redirectTo: redirectUrl,
    });

    return NextResponse.json({ success: `${provider} sign in successful!` });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error', message: (error as Error).message },
      { status: 500 }
    );
  }
}
