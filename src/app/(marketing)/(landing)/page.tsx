import { getPlans } from '@/actions/plans/get-plans.action';

import ContactSection from '@/components/landing/contact';
import FeaturesSection from '@/components/landing/features';
import HeroSection from '@/components/landing/hero';
import { PricingSection } from '@/components/landing/pricing';
import TestimonialSection from '@/components/landing/testimonials';

export default async function Home() {
  const plansResult = await getPlans();

  // Convert features string to array if needed
  const plans = plansResult.items.map((plan) => ({
    ...plan,
    features:
      typeof plan.features === 'string'
        ? (plan.features as string).split('\n')
        : plan.features,
  }));

  return (
    <main>
      <HeroSection />
      <FeaturesSection />
      <PricingSection plans={plans} />
      <TestimonialSection />
      <ContactSection />
    </main>
  );
}
