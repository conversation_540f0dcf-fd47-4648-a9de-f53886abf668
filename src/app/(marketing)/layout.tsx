// import { SidebarTrigger } from '@/components/ui/sidebar';
// import FooterSection from '@/components/landing/footer';
import Page from '@/components/layout/page';
import Navbar from '@/components/navigation/navbar';

export default async function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Page className="relative">
      {/* <SidebarTrigger /> */}
      <Navbar />
      {children}
      {/* <FooterSection /> */}
    </Page>
  );
}
