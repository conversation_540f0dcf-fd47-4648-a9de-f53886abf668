import { Suspense } from 'react';
import { Metada<PERSON> } from 'next';

import Container from '@/components/layout/container';
import { PageHeader } from '@/components/layout/page-header';
import PostsList from '@/components/posts/post-list';
import PostsListPageSkeleton from '@/components/skeletons/posts-list-page.skeleton';

export const metadata: Metadata = {
  title: 'Blog',
  description: 'Explore all the blog posts',
};

export default async function BlogPage() {
  return (
    <section>
      <Container narrow className="py-0">
        <div className="px-4">
          <PageHeader
            title="Blog"
            subtitle="Explore all the blog posts and stay updated with the latest news and trends."
          />
        </div>
      </Container>
      <Suspense fallback={<PostsListPageSkeleton />}>
        <PostsList />
      </Suspense>
    </section>
  );
}
