import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getBookmarkStatus } from '@/actions/bookmarks';
import { getPostComments } from '@/actions/comments';
import { getLikeStatus } from '@/actions/likes';
import { getPostBySlug } from '@/actions/posts';

import { getCurrentUser } from '@/lib/session';
import { PostDetail } from '@/components/posts/post-detail';

interface PostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({
  params,
}: PostPageProps): Promise<Metadata> {
  const post = await getPostBySlug(params.slug);

  return {
    title: post.title,
    description: post.excerpt,
  };
}

export default async function PostPage({ params }: PostPageProps) {
  const post = await getPostBySlug(params.slug);

  if (!post) {
    notFound();
  }

  const comments = await getPostComments(post.id);
  const [likeStatus, bookmarkStatus] = await getCurrentUser().then((user) =>
    user
      ? Promise.all([getLikeStatus(post.id), getBookmarkStatus(post.id)])
      : [false, false]
  );

  return (
    <PostDetail
      post={post}
      isLiked={likeStatus}
      isBookmarked={bookmarkStatus}
      comments={comments}
    />
  );
}
