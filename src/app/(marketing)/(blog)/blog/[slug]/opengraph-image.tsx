/* eslint-disable @next/next/no-img-element */

import { ImageResponse } from 'next/og';
import { getPostBySlug } from '@/actions/posts';

export const alt = 'Saastarter';
export const size = {
  width: 1200,
  height: 630,
};
export const contentType = 'image/png';

export default async function Image({ params }: { params: { slug: string } }) {
  const post = await getPostBySlug(params.slug);

  return new ImageResponse(
    (
      <img
        alt={post.excerpt || 'Saastarter Blog'}
        src={post.cover || 'placeholder.png'}
        style={{
          objectFit: 'cover',
          fontSize: 48,
          background: 'white',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
    ),
    {
      ...size,
    }
  );
}
