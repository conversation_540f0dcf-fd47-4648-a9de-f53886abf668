import React from 'react';
import { Metadata } from 'next';
import { MDXRemote } from 'next-mdx-remote/rsc';
import rehypePrettyCode from 'rehype-pretty-code';
import rehypeSlug from 'rehype-slug';
import remarkGfm from 'remark-gfm';

import {
  generateSidebar,
  getDocBySlug,
  getDocsStaticParams,
  getNavigation,
} from '@/lib/docs-utils';
import { getTableOfContents } from '@/lib/docs-utils/table-of-contents-generator';
import { rehypePrettyCodeOptions } from '@/lib/mdx-theme-config';
import {
  DocsBreadcrumbs,
  // DocsFooter,
  DocsSidebar,
  DocsTableOfContents,
} from '@/components/docs';
import DocsNavigationButtons from '@/components/docs/navigation-buttons';
import { customMDXComponents } from '@/components/mdx/custom-components';
import { Typography } from '@/components/typography';

// Import the function to get navigation data

export async function generateStaticParams() {
  return await getDocsStaticParams('src/content/docs');
}

type Props = {
  params: { slug: string[] | undefined };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const slug = params.slug || [];
  const { data } = await getDocBySlug(slug);

  return {
    title: data.title,
    description: data.description,
  };
}

export default async function DocPage({ params }: Props) {
  const slug = params.slug || [];
  const { content, data } = await getDocBySlug(slug);

  const toc = await getTableOfContents(content);

  // Fetch the navigation data
  const { nextPage, prevPage } = await getNavigation(slug);
  const items = generateSidebar();

  return (
    <>
      <div className="relative max-w-full flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
        <DocsSidebar items={items} />
        <main className="relative lg:gap-10 xl:grid xl:grid-cols-[1fr_300px] mb-12">
          <div className="tiptap prose prose-md dark:prose-invert prose-headings:font-title font-default focus:outline-none">
            <DocsBreadcrumbs />
            {data.title && (
              <Typography as="h1" className="mb-0">
                {data.title}
              </Typography>
            )}
            {data.description && (
              <p className="text-gray-500">{data.description}</p>
            )}
            <MDXRemote
              source={content}
              components={customMDXComponents}
              options={{
                mdxOptions: {
                  remarkPlugins: [remarkGfm],
                  rehypePlugins: [
                    rehypeSlug,
                    [rehypePrettyCode, rehypePrettyCodeOptions],
                  ],
                },
              }}
            />
            <DocsNavigationButtons nextPage={nextPage} prevPage={prevPage} />
          </div>
          <DocsTableOfContents {...toc} />
        </main>
      </div>
      {/* <DocsFooter /> */}
    </>
  );
}
