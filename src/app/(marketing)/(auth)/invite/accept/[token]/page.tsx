import { redirect } from 'next/navigation';
import { acceptInvite } from '@/actions/invitations';

export default async function AcceptInvitePage({
  params,
}: {
  params: { token: string };
}) {
  const { token } = params;
  const result = await acceptInvite(token);

  if (result.success) {
    // Redirect to sign-in page with email parameter
    redirect(`/signin?email=${encodeURIComponent(result.email || '')}`);
  }

  return null; // Redirecting, so no need to render anything
}
