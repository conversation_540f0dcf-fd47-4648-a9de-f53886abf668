import { Suspense } from 'react';
import { Mail } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function VerifyRequestPage() {
  return (
    <div className="container flex min-h-screen items-center justify-center">
      <Suspense fallback={null}>
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-primary/10 p-3">
                <Mail className="h-6 w-6 text-primary" />
              </div>
            </div>
            <CardTitle className="text-2xl text-center">
              Check your email
            </CardTitle>
            <CardDescription className="text-center">
              We&apos;ve sent you a magic link to sign in
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-muted-foreground">
              Click the link in your email to continue. If you don&apos;t see
              it, check your spam folder.
            </p>
            <p className="text-xs text-center text-muted-foreground">
              You can close this window if you&apos;ve already verified your
              email.
            </p>
          </CardContent>
        </Card>
      </Suspense>
    </div>
  );
}
