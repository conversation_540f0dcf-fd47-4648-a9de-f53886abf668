'use client';

import { useEffect } from 'react';

import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n').map((line) => line.trim()),
      digest: error.digest,
    });
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center flex-1 bg-background text-foreground">
      <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
      <p className="text-xl mb-8">
        We&lsquo;re sorry, but an unexpected error{' '}
        <span className="font-semibold">{error.message}</span> occurred.
      </p>

      <div className="flex space-x-4">
        <EnhancedButton onClick={() => reset()} variant="default">
          Try again
        </EnhancedButton>
        <EnhancedButton
          onClick={() => (window.location.href = '/')}
          variant="outline"
        >
          Return to Home
        </EnhancedButton>
      </div>
    </div>
  );
}
