import { Suspense } from 'react';
import { Metada<PERSON> } from 'next';
import { getBookmarkedPosts } from '@/actions/bookmarks';

import BookmarksGrid from '@/components/bookmarks/bookmarks-grid';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Bookmarks',
  description: 'Your bookmarked posts',
};

export default async function BookmarksPage() {
  const posts = await getBookmarkedPosts();
  return (
    <section>
      <PageHeader
        title="Bookmarks"
        subtitle="View your bookmarked posts from here."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <BookmarksGrid posts={posts} />
      </Suspense>
    </section>
  );
}
