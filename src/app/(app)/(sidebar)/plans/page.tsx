import { Suspense } from 'react';
import { Metadata } from 'next';
import { getPlans } from '@/actions/plans';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { PageHeader } from '@/components/layout/page-header';
import { PlansTable } from '@/components/plans/plans-table';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Plans',
  description: 'Manage your subscription plans',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);
export default async function PlansPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const plans = await getPlans({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="Subscription Plans"
        subtitle="Manage your subscription plans. You can view, edit, or delete plans from here."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <PlansTable initialData={plans} />
      </Suspense>
    </section>
  );
}
