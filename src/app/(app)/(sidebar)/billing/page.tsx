import { Suspense } from 'react';
import { getPlans } from '@/actions/plans';
import { getBillingHistory } from '@/actions/subscriptions';
import { parseAsStringLiteral } from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { getUserSubscriptions } from '@/lib/subscription';
import BillingTabs from '@/components/billing/billing-tabs';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata = {
  title: 'Billing',
  description:
    'Manage your subscription, view past invoices and make payments.',
};

const tabs = ['subscription', 'history'] as const;
const tabParser = parseAsStringLiteral(tabs).withDefault(tabs[0]);
export default async function BillingPage({ searchParams }: PageProps) {
  const tab = tabParser.parseServerSide(searchParams.tab);

  // await for promises to resolve
  const [subscription, billingHistory, plans] = await Promise.all([
    getUserSubscriptions(),
    getBillingHistory(),
    getPlans(),
  ]);

  return (
    <section>
      <PageHeader
        title="Billing"
        subtitle="Manage your subscription, view past invoices and make payments."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <BillingTabs
          subscription={subscription}
          billingHistory={billingHistory}
          plans={plans}
          initialTab={tab}
        />
      </Suspense>
    </section>
  );
}
