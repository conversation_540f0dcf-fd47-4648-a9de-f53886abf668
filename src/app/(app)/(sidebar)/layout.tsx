import { getPlans } from '@/actions/plans';
import { getTenantsByUser } from '@/actions/tenant';

import { SessionUser } from '@/types/user.types';
import { getCurrentUser } from '@/lib/session';
import { SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import Container from '@/components/layout/container';
import TopBarButtons from '@/components/navigation/top-bar-buttons';
import { AppSidebar } from '@/components/shared/app-sidebar';

export default async function SidebarLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, plans, tenants] = await Promise.all([
    getCurrentUser(),
    getPlans(),
    getTenantsByUser(),
  ]);

  return (
    <>
      <AppSidebar
        user={user as SessionUser}
        plans={plans.items}
        tenants={tenants}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 justify-between">
          <SidebarTrigger className="-ml-1" />
          <div className="flex items-center gap-2">
            <TopBarButtons user={user as SessionUser} />
          </div>
        </header>
        <Container hasVerticalPadding>{children}</Container>
      </SidebarInset>
    </>
  );
}
