import { Suspense } from 'react';
import { getSubscribers } from '@/actions/subscribers';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';
import { SubscribersTable } from '@/components/subscribers/subscribers-table';

export const metadata = {
  title: 'Subscribers',
  description: 'Manage your newsletter subscribers',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);

export default async function SubscribersPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const subscribers = await getSubscribers({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="Subscribers"
        subtitle="View and manage your subscribers from here. You can filter, delete or export subscribers."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <SubscribersTable initialData={subscribers} />
      </Suspense>
    </section>
  );
}
