import { Suspense } from 'react';
import { Metadata } from 'next';
import { getPlans } from '@/actions/plans';

import { getUserSubscriptions } from '@/lib/subscription';
import SubscriptionPlans from '@/components/billing/subscription-plans';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Upgrade',
  description: 'view your plans and upgrade your subscription',
};

export default async function UpgradePage() {
  const [subscription, plans] = await Promise.all([
    getUserSubscriptions(),
    getPlans(),
  ]);

  return (
    <section>
      <PageHeader title="Billing" subtitle="Manage your subscription" />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <SubscriptionPlans
          subscription={subscription as any}
          plans={plans.items}
        />
      </Suspense>
    </section>
  );
}
