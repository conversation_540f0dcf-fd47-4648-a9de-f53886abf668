import { Suspense } from 'react';
import { Metadata } from 'next';
import { getPostsByAuthor } from '@/actions/posts';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { PageHeader } from '@/components/layout/page-header';
import { PostsTable } from '@/components/posts/posts-table';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Posts',
  description: 'Manage your blog posts',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);

export default async function PostsPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const posts = await getPostsByAuthor({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="My Blog Posts"
        subtitle="Explore you latest articles and manage your blog posts. You can view, edit, or delete posts from here."
      />
      <Suspense fallback={<SubpageContentSkeleton />}>
        <PostsTable initialData={posts} />
      </Suspense>
    </section>
  );
}
