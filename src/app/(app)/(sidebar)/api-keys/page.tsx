import { Suspense } from 'react';
import { Metadata } from 'next';
import { getApiKeys } from '@/actions/api-keys';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import ApiKeysTable from '@/components/api-keys/api-keys-table';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'API Keys',
  description: 'Manage your API keys',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);

export default async function ApiKeysPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const apiKeys = await getApiKeys({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="API Keys"
        subtitle="Manage your API keys from here. You can generate new keys, revoke existing ones, or regenerate keys to use in your integrations."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <ApiKeysTable initialData={apiKeys} />
      </Suspense>
    </section>
  );
}
