import { Suspense } from 'react';
import { Metada<PERSON> } from 'next';

import { PageHeader } from '@/components/layout/page-header';
import { OrganizationGrid } from '@/components/organizations/organization-grid';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Organizations',
  description: 'View and manage all your organizations and teams',
};
export default async function OrganizationsPage() {
  return (
    <section>
      <PageHeader
        title="Organizations"
        subtitle="View and manage all your organizations and teams"
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <OrganizationGrid />
      </Suspense>
    </section>
  );
}
