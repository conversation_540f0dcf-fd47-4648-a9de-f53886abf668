import { Suspense } from 'react';
import { getInvites } from '@/actions/invitations/get-invites.action';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { InvitesTable } from '@/components/invites/invites-table';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata = {
  title: 'Invites',
  description:
    'Explor and manage invitations from here. You can send invitations, manage existing invites, and more.',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);

export default async function InvitesPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const invites = await getInvites({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="Invitations"
        subtitle="Manage your invitations from here."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <InvitesTable initialData={invites} />
      </Suspense>
    </section>
  );
}
