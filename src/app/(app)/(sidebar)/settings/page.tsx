import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { parseAsStringLiteral } from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { getCurrentUser } from '@/lib/session';
import { PageHeader } from '@/components/layout/page-header';
import SettingsTabs from '@/components/settings/settings-tabs';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata = {
  title: 'Settings',
  description: 'Customize your account settings and preferences.',
};
const tabs = ['profile', 'notifications', 'appearance'] as const;
const tabParser = parseAsStringLiteral(tabs).withDefault(tabs[0]);

export default async function ProfilePage({ searchParams }: PageProps) {
  const user = await getCurrentUser();

  // Redirect to sign-in page if not logged in this should essentially never happen as the middleware will handle this
  if (!user) {
    redirect('/signin');
  }

  const tab = tabParser.parseServerSide(searchParams.tab);
  return (
    <section>
      <PageHeader
        title="Settings"
        subtitle="Customize your account settings and preferences."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <SettingsTabs user={user} initialTab={tab} />
      </Suspense>
    </section>
  );
}
