import { Suspense } from 'react';
import { Metada<PERSON> } from 'next';
import { getNotifications } from '@/actions/notifications/get-notifications.action';

import { PageHeader } from '@/components/layout/page-header';
import Notifications from '@/components/navigation/notifications';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Notifications',
  description: 'View your notifications',
};

export default async function NotificationsPage() {
  const notifications = await getNotifications();

  return (
    <section>
      <PageHeader
        title="Notifications"
        subtitle="Stay updated with your latest activities"
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <Notifications initialData={notifications} />
      </Suspense>
    </section>
  );
}
