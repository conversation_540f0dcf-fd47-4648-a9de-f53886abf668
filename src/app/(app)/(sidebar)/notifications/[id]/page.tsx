import { Metadata } from 'next';
import { getNotification } from '@/actions/notifications/get-notification.action';

import { Notification } from '@/types/notification.types';
import NotificationDetail from '@/components/notifications/notification-detail';

export const metadata: Metadata = {
  title: 'Notification Details',
  description: 'View details of a specific notification',
};

export default async function NotificationPage({
  params,
}: {
  params: { id: string };
}) {
  const notification = await getNotification(params.id);

  return <NotificationDetail notification={notification as Notification} />;
}
