import { Suspense } from 'react';
import { Metada<PERSON> } from 'next';
import { getStripeDashboardData } from '@/actions/subscriptions';

import { OverviewTab } from '@/components/dashboard/overview-tab';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';

export const metadata: Metadata = {
  title: 'Dashboard',
  description: 'View your subscription analytics and reports',
};

export default async function DashboardPage() {
  const dashboardData = await getStripeDashboardData();

  return (
    <section>
      <PageHeader
        title="Dashboard"
        subtitle="View your subscription analytics and reports"
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <OverviewTab dashboardData={dashboardData} />
      </Suspense>
    </section>
  );
}
