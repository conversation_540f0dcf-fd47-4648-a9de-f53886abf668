import { Suspense } from 'react';
import { getUsers } from '@/actions/users';
import {
  parseAsInteger,
  parseAsString,
  parseAsStringLiteral,
} from 'nuqs/server';

import { PageProps } from '@/types/shared.types';
import { PageHeader } from '@/components/layout/page-header';
import SubpageContentSkeleton from '@/components/skeletons/subpage-content.skeleton';
import { UsersTable } from '@/components/users/users-table';

export const metadata = {
  title: 'Users',
  description:
    'Explore and manage your users from here. You can edit user roles, delete users.',
};

const order = ['asc', 'desc'] as const;

const qParser = parseAsString.withDefault('');
const pageParser = parseAsInteger.withDefault(1);
const orderByParser = parseAsString;
const orderParser = parseAsStringLiteral(order);

export default async function UserPage({ searchParams }: PageProps) {
  const searchQuery = qParser.parseServerSide(searchParams.q);
  const page = pageParser.parseServerSide(searchParams.page);
  const orderBy = orderByParser.parseServerSide(searchParams.orderBy);
  const sortOrder = orderParser.parseServerSide(searchParams.order);

  const users = await getUsers({
    take: 6,
    page,
    ...(orderBy && sortOrder && { orderBy: { [orderBy]: sortOrder } }),
    ...(searchQuery && { filter: { searchQuery } }),
  });

  return (
    <section>
      <PageHeader
        title="Users"
        subtitle="Explore and manage your users from here. You can edit user roles, delete users."
      />

      <Suspense fallback={<SubpageContentSkeleton />}>
        <UsersTable initialData={users} />
      </Suspense>
    </section>
  );
}
