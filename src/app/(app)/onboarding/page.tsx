import { SessionUser } from '@/types/user.types';
import { getCurrentUser } from '@/lib/session';
import Container from '@/components/layout/container';
import { PageHeader } from '@/components/layout/page-header';
import { ProfileForm } from '@/components/profile/profile-form';

export const metadata = {
  title: 'Create Organization',
  description: 'Create your organization to get started',
};

export default async function OnboardingPage() {
  const user = await getCurrentUser();

  return (
    <section>
      <Container narrow className="py-6">
        <PageHeader
          title="Complete your profile"
          subtitle="You're almost there! Complete your profile to get started."
        />

        <ProfileForm data={user as SessionUser} isOnboarding={true} />
      </Container>
    </section>
  );
}
