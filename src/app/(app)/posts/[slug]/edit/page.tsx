import { Metadata } from 'next';
import { getPostBySlug } from '@/actions/posts';
import { getTags } from '@/actions/tags/get-tags.action';

import { PostForm } from '@/components/posts/post-form';

export const metadata: Metadata = {
  title: 'Edit Post',
  description: 'Edit your blog post',
};

export default async function CreatePostPage({
  params,
}: {
  params: { slug: string };
}) {
  const [post, tags] = await Promise.all([
    getPostBySlug(params.slug),
    getTags(),
  ]);

  return <PostForm initialValues={post} tags={tags} />;
}
