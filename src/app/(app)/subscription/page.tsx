import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { getTenantsByUser } from '@/actions/tenant';
import { CheckCircle } from 'lucide-react';

import { getUserSubscriptions } from '@/lib/subscription';
import { Button } from '@/components/ui/button';
import { SubscriptionSuccess } from '@/components/billing/subscription-success';

export const metadata: Metadata = {
  title: 'Subscription Successful',
  description: 'Your subscription has been successfully activated',
};

export default async function SubscriptionSuccessPage() {
  const subscription = await getUserSubscriptions();
  const tenants = await getTenantsByUser();

  if (!subscription.isSubscribed) {
    redirect('/billing');
  }

  const hasOrganizations = tenants.length > 0;

  return (
    <div className="container max-w-3xl py-16">
      <SubscriptionSuccess />
      <div className="mt-8 text-center">
        <div className="mb-8 flex justify-center">
          <div className="rounded-ful">
            <CheckCircle className="h-16 w-16" />
          </div>
        </div>

        <h1 className="mb-4 text-4xl font-bold tracking-tight">
          Welcome to {subscription.name}!
        </h1>
        <p className="mx-auto mb-10 max-w-2xl text-lg text-muted-foreground">
          Your subscription is now active.
          {hasOrganizations
            ? " You're ready to take full advantage of all features with your existing organizations or create a new one."
            : ' To get started, create your first organization and unlock all premium features.'}
        </p>

        <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
          {hasOrganizations ? (
            <>
              <Button size="lg" className="gap-2" asChild>
                <Link href="/organizations">View Organizations</Link>
              </Button>
              <Button size="lg" variant="outline" className="gap-2" asChild>
                <Link href="/billing">Subscription Details</Link>
              </Button>
            </>
          ) : (
            <>
              <Button size="lg" className="gap-2" asChild>
                <Link href="/organizations">Create Organization</Link>
              </Button>
              <Button size="lg" variant="outline" className="gap-2" asChild>
                <Link href="/billing">Subscription Details</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
