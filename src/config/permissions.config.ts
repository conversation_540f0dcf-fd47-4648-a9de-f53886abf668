// Import role and tier enums from Prisma schema
import { Role, Tier } from '@prisma/client';

// Define all possible resources that can be protected in the application
export type Resource =
  | 'post' // Blog posts
  | 'comment' // Post comments
  | 'like' // Post likes
  | 'user' // User accounts
  | 'apiKey' // API authentication keys
  | 'invite' // User invitations
  | 'tenant' // Multi-tenant spaces
  | 'organization' // Organization settings
  | 'billing' // Billing information
  | 'plan' // Subscription plans
  | 'subscriber' // Newsletter subscribers
  | 'bookmark' // Saved posts
  | 'settings' // Application settings
  | '*'; // Wildcard for all resources

// Define all possible actions that can be performed on resources
export type Action =
  | 'create' // Create new resource
  | 'read' // View resource
  | 'update' // Modify existing resource
  | 'delete' // Remove resource
  | 'publish' // Make resource public
  | 'unpublish' // Make resource private
  | 'like' // Like/unlike resource
  | 'bookmark' // Bookmark/unbookmark resource
  | 'comment' // Comment on resource
  | 'activate' // Activate/deactivate resource
  | 'invite' // Invite users
  | '*'; // Wildcard for all actions

// Permission format: either '*' for full access or 'resource:action' combination
export type Permission = '*' | `${Resource}:${Action}`;

// Map each role to its allowed permissions
export const rolePermissions: Record<Role, Permission[]> = {
  [Role.OWNER]: [
    // System owner - has complete access to everything
    '*', // Single wildcard grants all permissions
  ],
  [Role.ADMIN]: [
    // Administrative staff - broad access except for plan creation
    'post:*', // Full control over posts
    'comment:*', // Full control over comments
    'like:*', // Full control over likes
    'bookmark:*', // Full control over bookmarks
    'user:*', // Full control over users
    'apiKey:*', // Full control over API keys
    'invite:*', // Full control over invitations
    'tenant:*', // Full control over tenants
    'billing:*', // Full control over billing info
    'subscriber:*', // Full control over subscribers
  ],
  [Role.COLLABORATOR]: [
    // Content creators and editors
    'post:*', // Full control over posts
    'comment:*', // Full control over comments
    'like:*', // Full control over likes
    'bookmark:*', // Full control over bookmarks

    //TODO: add more permissions as needed
  ],
  [Role.USER]: [
    // Regular users - basic access
    'post:*', // Full control over posts
    'comment:*', // Full control over comments
    'like:*', // Full control over likes
    'bookmark:*', // Full control over bookmarks

    //TODO: add more permissions as needed
  ],
};

// Define features available for each subscription tier with proper typing
export type TierFeature = `max_tenants:${number}` | 'unlimited_tenants';

export const tierFeatures: Record<Tier, TierFeature[]> = {
  [Tier.STARTER]: [
    'max_tenants:1', // Limited to 1 tenant
  ],
  [Tier.PRO]: [
    'max_tenants:5', // Limited to 5 tenants
  ],
  [Tier.ENTERPRISE]: [
    'unlimited_tenants', // Unlimited tenants
  ],
};
