import { Role } from '@prisma/client';

import { AppConfig } from '../types';

export const appConfig: AppConfig = {
  sidebarNav: [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: 'charts',
    },
    {
      title: 'Posts',
      href: '/posts',
      icon: 'post',
      roles: [Role.ADMIN, Role.COLLABORATOR, Role.USER],
    },
    {
      title: 'Calendar',
      href: '/calendar',
      icon: 'calendar',
    },

    {
      title: 'Bookmarks',
      href: '/bookmarks',
      icon: 'bookmark',
      roles: [Role.ADMIN, Role.COLLABORATOR, Role.USER],
    },
    {
      title: 'Subscribers',
      href: '/subscribers',
      icon: 'subscriber',
      roles: [Role.ADMIN],
    },
    {
      title: 'Settings',
      href: '/settings',
      icon: 'settings',
    },
  ],

  admin: [
    {
      title: 'Users',
      href: '/users',
      icon: 'users',
      roles: [Role.ADMIN, Role.OWNER],
    },
    {
      title: 'Invites',
      href: '/invitations',
      icon: 'invite',
      roles: [Role.ADMIN],
    },
    {
      title: 'Plans',
      href: '/plans',
      icon: 'plans',
      roles: [Role.OWNER],
    },
    {
      title: 'Billing',
      href: '/billing',
      icon: 'billing',
      roles: [Role.ADMIN],
    },
    {
      title: 'API Keys',
      href: '/api-keys',
      icon: 'key',
      roles: [Role.ADMIN],
    },
    {
      title: 'Organizations',
      href: '/organizations',
      icon: 'organizations',
      roles: [Role.ADMIN],
    },
  ],
};
