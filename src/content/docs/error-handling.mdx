---
title: <PERSON>rror Handling
description: Learn about the error handling system and how to use it effectively in your application.
position: 5
---

## Overview

The error handling system provides a standardized way to handle and communicate errors across your application. It includes predefined error types, HTTP status codes, and utilities for both server and client-side error handling.

## Error Types

### AppError

The base error class that all application errors extend from:

```typescript
class AppError extends Error {
  constructor(
    message: string,
    code: ErrorCode,
    statusCode: HttpStatusCode,
    context?: ErrorContext
  );
}
```

### ErrorContext

The `ErrorContext` interface provides structured error context:

```typescript
interface ErrorContext {
  description?: string; // User-friendly error description
  field?: string; // Field that caused the error
  value?: unknown; // Invalid value
  target?: string[]; // Often from Prisma P2002 meta
  prismaCode?: string; // Specific Prisma error code
  issues?: any; // For Zod validation issues
  cause?: unknown; // Original error cause
  originalError?: unknown; // For unknown errors
  [key: string]: unknown; // Additional flexible context
}
```

### Predefined Errors

Common error scenarios are available through the `Errors` object:

```typescript
// Unauthorized access
throw Errors.Unauthorized('Custom message', { field: 'token' });

// Resource not found
throw Errors.NotFound('User', { target: ['userId'] });

// Validation errors
throw Errors.ValidationError('Invalid email format', {
  field: 'email',
  value: 'invalid@email',
});

// Rate limiting
throw Errors.RateLimitExceeded({ description: 'Try again in 5 minutes' });

// Database errors
throw Errors.DatabaseError('Failed to create user', {
  prismaCode: 'P2002',
  target: ['email'],
});

// Network errors
throw Errors.NetworkError('API unavailable');

// Forbidden access
throw Errors.Forbidden('Access denied', {
  description: 'Requires admin role',
});

// Server errors
throw Errors.ServerError('Processing failed');

// Resource conflicts
throw Errors.Conflict('User already exists');

// Bad requests
throw Errors.BadRequest('Invalid parameters');
```

## Default Error Behaviors

When using `Errors` without parameters, each error type has default messages and behaviors:

### Default Usage Examples

```typescript
// Unauthorized
throw Errors.Unauthorized();
// Results in:
{
  message: "Unauthorized access",
  code: "UNAUTHORIZED",
  statusCode: 401,
  context: undefined
}

// Not Found
throw Errors.NotFound();
// Results in:
{
  message: "Resource not found",
  code: "NOT_FOUND",
  statusCode: 404,
  context: undefined
}

// Validation Error
throw Errors.ValidationError();  // ❌ Won't compile - message is required

// Rate Limit
throw Errors.RateLimitExceeded();
// Results in:
{
  message: "Rate limit exceeded. Please try again later.",
  code: "RATE_LIMIT_EXCEEDED",
  statusCode: 429,
  context: undefined
}

// Database Error
throw Errors.DatabaseError();
// Results in:
{
  message: "A database error occurred",
  code: "DATABASE_ERROR",
  statusCode: 500,
  context: undefined
}

// Network Error
throw Errors.NetworkError();
// Results in:
{
  message: "A network error occurred",
  code: "NETWORK_ERROR",
  statusCode: 503,
  context: undefined
}

// Forbidden
throw Errors.Forbidden();
// Results in:
{
  message: "Permission denied",
  code: "FORBIDDEN",
  statusCode: 403,
  context: undefined
}

// Server Error
throw Errors.ServerError();
// Results in:
{
  message: "An internal server error occurred",
  code: "SERVER_ERROR",
  statusCode: 500,
  context: undefined
}

// Conflict
throw Errors.Conflict();
// Results in:
{
  message: "Resource conflict",
  code: "ALREADY_EXISTS",
  statusCode: 409,
  context: undefined
}

// Bad Request
throw Errors.BadRequest();
// Results in:
{
  message: "Bad request",
  code: "INVALID_INPUT",
  statusCode: 400,
  context: undefined
}
```

### Client-Side Toast Output for Default Errors

When these default errors are handled by `handleClientError`, they produce the following toast notifications:

```typescript
// Using Errors.Unauthorized()
handleClientError(Errors.Unauthorized());
// Toast shows:
// Title: "Unauthorized access"
// Description: "Please sign in to continue."

// Using Errors.NotFound()
handleClientError(Errors.NotFound());
// Toast shows:
// Title: "Resource not found"
// Description: "The requested resource could not be found."

// Using Errors.RateLimitExceeded()
handleClientError(Errors.RateLimitExceeded());
// Toast shows:
// Title: "Rate limit exceeded. Please try again later."
// Description: "Too many requests. Please try again later."

// Using Errors.Forbidden()
handleClientError(Errors.Forbidden());
// Toast shows:
// Title: "Permission denied"
// Description: "You don't have permission to perform this action."

// Using Errors.ServerError()
handleClientError(Errors.ServerError());
// Toast shows:
// Title: "An internal server error occurred"
// Description: "Something went wrong on our end. Please try again later."
```

### API Response Format for Default Errors

When these errors are returned through the API:

```typescript
// Example API response for Errors.Unauthorized()
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Unauthorized access",
    "statusCode": 401
  }
}

// Example API response for Errors.NotFound()
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Resource not found",
    "statusCode": 404
  }
}
```

Note: Some error types (like `ValidationError`) require parameters and cannot be used with empty constructors. Always provide a message for validation errors to ensure proper error communication.

## HTTP Status Codes

The system includes standard HTTP status codes for different scenarios:

| Code | Constant                | Description                   |
| ---- | ----------------------- | ----------------------------- |
| 200  | `OK`                    | Request succeeded             |
| 201  | `CREATED`               | Resource created successfully |
| 400  | `BAD_REQUEST`           | Invalid request               |
| 401  | `UNAUTHORIZED`          | Authentication required       |
| 403  | `FORBIDDEN`             | Permission denied             |
| 404  | `NOT_FOUND`             | Resource not found            |
| 409  | `CONFLICT`              | Resource conflict             |
| 422  | `UNPROCESSABLE_ENTITY`  | Validation failed             |
| 429  | `TOO_MANY_REQUESTS`     | Rate limit exceeded           |
| 500  | `INTERNAL_SERVER_ERROR` | Server error                  |
| 503  | `SERVICE_UNAVAILABLE`   | Service unavailable           |

## Error Codes

Internal error codes for better error categorization:

| Code                  | Description                         |
| --------------------- | ----------------------------------- |
| `UNAUTHORIZED`        | Authentication/authorization errors |
| `INVALID_INPUT`       | Invalid input data                  |
| `NOT_FOUND`           | Resource not found                  |
| `ALREADY_EXISTS`      | Resource already exists             |
| `FORBIDDEN`           | Permission denied                   |
| `SERVER_ERROR`        | Internal server errors              |
| `RATE_LIMIT_EXCEEDED` | Rate limiting                       |
| `VALIDATION_ERROR`    | Data validation errors              |
| `DATABASE_ERROR`      | Database operation errors           |
| `NETWORK_ERROR`       | Network-related errors              |

## Server-Side Error Handling

### In Server Actions

```typescript
import { Errors, handleServerError } from '@/lib/error-utils';

export async function createUser(data: CreateUserInput) {
  try {
    // Validate input
    if (!isValid(data)) {
      throw Errors.ValidationError('Invalid user data', {
        issues: validationResult.errors,
      });
    }

    // Check permissions
    if (!hasPermission()) {
      throw Errors.Unauthorized('Access denied', {
        description: 'Please sign in with admin privileges',
      });
    }

    // Create user
    const user = await db.users.create(data);
    return user;
  } catch (error) {
    // handleServerError now includes better error transformation
    return handleServerError(error, { userData: data });
  }
}
```

### Error Handling Features

The `handleServerError` utility now includes:

- NextAuth/Next.js redirect handling
- Comprehensive error logging
- Prisma error transformation
- Zod validation error handling
- Network error detection
- Generic error wrapping

### In API Routes

```typescript
import { ApiResponse } from '@/lib/api-response';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const user = await createUser(data);
    return ApiResponse.success(user, HttpStatusCode.CREATED);
  } catch (error) {
    return ApiResponse.error(error);
  }
}
```

## Client-Side Error Handling

There are two main utilities for handling client-side errors:

1. `handleClientError`: For simple error handling with toast notifications
2. `withToastFeedback`: For complex async operations with loading states

### Using handleClientError

The simplest way to handle errors with toast notifications:

```typescript
import { handleClientError } from '@/lib/error-utils';

// Basic usage
try {
  await api.post('/data', formData);
} catch (error) {
  handleClientError(error);
}

// With custom message
try {
  await api.post('/data', formData);
} catch (error) {
  handleClientError(error, 'Failed to save data');
}

// Example in a form submit handler
const handleSubmit = async (data: FormData) => {
  try {
    const response = await fetch('/api/users', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new AppError(error.message, error.code, response.status);
    }

    return await response.json();
  } catch (error) {
    handleClientError(error);
  }
};
```

### Using withToastFeedback

For more complex scenarios where you need loading states and success messages:

```typescript
import { withToastFeedback } from '@/lib/error-utils';

// Basic usage - only error handling
await withToastFeedback(async () => {
  const response = await api.post('/data', formData);
  return response.data;
});

// With loading and success states
await withToastFeedback(
  async () => {
    const response = await api.post('/data', formData);
    return response.data;
  },
  {
    loading: 'Saving changes...',
    success: 'Changes saved successfully',
  }
);

// With button loading state
const [isLoading, setIsLoading] = useState(false);

await withToastFeedback(
  async () => {
    const response = await api.post('/data', formData);
    return response.data;
  },
  {
    loading: 'Saving changes...',
    success: 'Changes saved successfully',
    onLoadingChange: setIsLoading,
  }
);
```

## Best Practices

1. **Choose the Right Error Handler**:

```typescript
// Use handleClientError for simple error cases
try {
  await api.delete(`/users/${userId}`);
} catch (error) {
  handleClientError(error);
}

// Use withToastFeedback for complex operations with loading states
const result = await withToastFeedback(
  async () => {
    const response = await api.post('/users', userData);
    return response.data;
  },
  {
    loading: 'Creating user...',
    success: 'User created successfully',
  }
);
```

2. **Form Submissions**:

```typescript
// Simple form
const handleSimpleForm = async (data: FormData) => {
  try {
    await api.post('/contact', data);
    toast.success('Message sent!');
  } catch (error) {
    handleClientError(error);
  }
};

// Complex form with loading state
const handleComplexForm = async (data: FormData) => {
  const result = await withToastFeedback(
    async () => {
      const response = await api.post('/users', data);
      return response.data;
    },
    {
      loading: 'Creating account...',
      success: 'Account created successfully',
    }
  );

  if (result) {
    router.push(`/users/${result.id}`);
  }
};
```

3. **API Calls in Components**:

```typescript
// Simple data fetching
const fetchData = async () => {
  try {
    const response = await api.get('/data');
    return response.data;
  } catch (error) {
    handleClientError(error);
    return null;
  }
};

// Complex data operations
const processData = async () => {
  return withToastFeedback(
    async () => {
      const response = await api.post('/data/process', { id });
      return response.data;
    },
    {
      loading: 'Processing data...',
      success: 'Data processed successfully',
    }
  );
};
```

4. **React Query Integration**:

```typescript
// Simple query
const useUsers = () => {
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      try {
        const response = await api.get('/users');
        return response.data;
      } catch (error) {
        handleClientError(error);
        throw error;
      }
    },
  });
};

// Complex query with loading feedback
const useProcessData = () => {
  return useQuery({
    queryKey: ['process-data'],
    queryFn: () =>
      withToastFeedback(
        async () => {
          const response = await api.get('/process');
          return response.data;
        },
        {
          loading: 'Processing data...',
          error: 'Failed to process data',
        }
      ),
  });
};
```

## Error Response Format

API error responses now include detailed context:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "context": {
      "field": "email",
      "value": "invalid-email",
      "description": "Please enter a valid email address",
      "issues": [
        {
          "code": "invalid_string",
          "path": ["email"],
          "message": "Invalid email format"
        }
      ]
    }
  }
}
```

Success responses:

```json
{
  "success": true,
  "data": {
    // Response data
  }
}
```

<Callout>
  Remember to always handle errors appropriately in your application. Unhandled
  errors can lead to poor user experience and difficult-to-debug issues.
</Callout>
