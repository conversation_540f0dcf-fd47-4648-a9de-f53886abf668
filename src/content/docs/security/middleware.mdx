---
title: Middleware & Security
description: Learn about the middleware implementation, CORS, rate limiting, and security features.
position: 7
---

## Middleware Overview

The application uses Next.js middleware to handle authentication, authorization, CORS, and routing logic. This middleware runs before any request is processed, providing a secure layer of protection across your application.

### Authentication Flow

The middleware uses NextAuth v5's `auth()` middleware wrapper to handle authentication state:

```typescript
import { auth } from '@/auth';

export default auth((req) => {
  const { auth } = req;
  const isLoggedIn = !!auth;
  // ... middleware logic
});
```

### Route Protection

Routes are categorized into several types:

- **Public Routes**: Accessible without authentication
- **Auth Routes**: Login, signup, etc.
- **API Auth Routes**: Authentication-related API endpoints
- **Protected Routes**: Require authentication
- **Role-Protected Routes**: Require specific user roles

```typescript
// Examples of route types
const PUBLIC_ROUTES = ['/about', '/pricing'];
const AUTH_ROUTES = ['/signin', '/signup'];
const API_AUTH_PREFIX = '/api/auth';
```

### Profile Completion Flow

The middleware enforces profile completion by redirecting users to the onboarding page if their profile is incomplete:

```typescript
if (isLoggedIn && !hasProfile && pathname !== REDIRECT_ONBOARDING_URL) {
  return NextResponse.redirect(new URL(REDIRECT_ONBOARDING_URL, nextUrl));
}
```

## CORS Configuration

Cross-Origin Resource Sharing (CORS) is handled by a dedicated middleware:

```typescript
const allowedOrigins = [
  'http://localhost:3000',
  process.env.NEXT_PUBLIC_DOMAIN,
];

// CORS headers for regular requests
response.headers.set('Access-Control-Allow-Origin', origin);
response.headers.set('Access-Control-Allow-Credentials', 'true');

// Additional headers for preflight requests
response.headers.set(
  'Access-Control-Allow-Methods',
  'GET, POST, PUT, DELETE, OPTIONS'
);
response.headers.set(
  'Access-Control-Allow-Headers',
  'Content-Type, Authorization'
);
```

## Rate Limiting

Rate limiting is implemented using Upstash Redis with different limits for various operations:

```typescript
const limiters = {
  default: {
    requests: 50,
    duration: '1 d', // 50 requests per day
  },
  auth: {
    requests: 5,
    duration: '5 m', // 5 requests per 5 minutes
  },
  api: {
    requests: 100,
    duration: '1 h', // 100 requests per hour
  },
};
```

### Usage Examples

#### 1. API Routes

```typescript
import { rateLimit } from '@/lib/rate-limit';
import { Errors } from '@/lib/errors';
import { ApiResponse } from '@/lib/api-response';

// Basic API endpoint with rate limiting
export async function POST(req: Request) {
  try {
    const ip = req.headers.get('x-forwarded-for') ?? 'anonymous';
    const { remaining, reset } = await rateLimit(ip, 'api');

    // Your handler logic here
    const result = await processRequest(req);

    return ApiResponse.success(result, 200, {
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': reset.toString(),
    });

  } catch (error) {
    return ApiResponse.error(error);
  }
}

// Authentication endpoint with stricter limits
export async function POST(req: Request) {
  try {
    const ip = req.headers.get('x-forwarded-for') ?? 'anonymous';
    await rateLimit(ip, 'auth');

    // Authentication logic here
    const user = await authenticateUser(req);
    return ApiResponse.success(user);

  } catch (error) {
    return ApiResponse.error(error);
  }
}
```

#### 2. Server Actions

```typescript
import { handleServerError } from '@/lib/error-utils';
import { Errors } from '@/lib/errors';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting based on user ID
export async function createComment(data: CommentData) {
  try {
    const user = await getCurrentUser();
    const { remaining } = await rateLimit(user.id, 'default');

    // Comment creation logic
    const comment = await db.comments.create(data);

    return { comment, remainingComments: remaining };
  } catch (error) {
    return handleServerError(error, {
      action: 'createComment',
      userId: user?.id,
    });
  }
}

// Rate limiting with feedback
export async function submitFeedback(data: FeedbackData) {
  try {
    const user = await getCurrentUser();
    const { remaining, reset } = await rateLimit(user.id, 'default');

    // Feedback submission logic
    const feedback = await db.feedback.create(data);

    return {
      feedback,
      limits: { remaining, reset },
    };
  } catch (error) {
    throw handleServerError(error, {
      action: 'submitFeedback',
      userId: user?.id,
    });
  }
}
```

#### 3. API Endpoints with Multiple Limits

```typescript
import { ApiResponse } from '@/lib/api-response';
import { Errors } from '@/lib/errors';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(req: Request) {
  try {
    const ip = req.headers.get('x-forwarded-for') ?? 'anonymous';
    const user = await getCurrentUser();

    // Apply both IP-based and user-based rate limits
    const [ipLimit, userLimit] = await Promise.all([
      rateLimit(ip, 'api'),
      rateLimit(`user_${user.id}`, 'default'),
    ]);

    // Your handler logic here
    const result = await processRequest(req);

    return ApiResponse.success(result, 200, {
      'X-RateLimit-Remaining': Math.min(
        ipLimit.remaining,
        userLimit.remaining
      ).toString(),
      'X-RateLimit-Reset': Math.max(ipLimit.reset, userLimit.reset).toString(),
    });
  } catch (error) {
    return ApiResponse.error(error);
  }
}
```

#### 4. React Query Integration

```typescript
import { handleClientError, withToastFeedback } from '@/lib/error-utils';
import { rateLimit } from '@/lib/rate-limit';

export function useRateLimitedQuery(userId: string) {
  return useQuery({
    queryKey: ['data', userId],
    queryFn: () =>
      withToastFeedback(
        async () => {
          await rateLimit(userId, 'api');
          const response = await fetch('/api/data');
          if (!response.ok) throw new Error('Failed to fetch data');
          return response.json();
        },
        {
          loading: 'Fetching data...',
          success: 'Data loaded successfully',
        }
      ),
    onError: handleClientError,
  });
}
```

#### 5. Webhook Rate Limiting

```typescript
import { ApiResponse } from '@/lib/api-response';
import { Errors } from '@/lib/errors';
import { rateLimit } from '@/lib/rate-limit';

export async function POST(req: Request) {
  try {
    const webhookId = req.headers.get('x-webhook-id');
    if (!webhookId) {
      throw Errors.BadRequest('Webhook ID required');
    }

    const { remaining, reset } = await rateLimit(`webhook_${webhookId}`, 'api');

    // Process webhook
    const result = await processWebhook(req);

    return ApiResponse.success(result, 200, {
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': reset.toString(),
    });
  } catch (error) {
    return ApiResponse.error(error);
  }
}
```

### Best Practices

1. **Error Handling**:

   - Use `ApiResponse.error()` for consistent API responses
   - Use `handleServerError()` for server-side error handling
   - Use `handleClientError()` for client-side error handling
   - Use `withToastFeedback()` for operations with loading states

2. **Rate Limit Headers**:

   - Always include rate limit information in successful responses
   - Use the error context from `RateLimitExceeded` for detailed feedback

3. **Client Feedback**:

   - Show remaining limits to users when appropriate
   - Provide clear reset times when limits are exceeded
   - Use toast notifications for rate limit warnings

4. **Security**:
   - Use appropriate error contexts without exposing sensitive data
   - Implement progressive delays for repeated violations
   - Monitor rate limit violations through error tracking

## Input Validation & Sanitization

The application combines Zod validation with DOMPurify sanitization:

```typescript
// Validation schema example
const schema = z.object({
  email: ValidationSchemas.email,
  content: ValidationSchemas.safeHtml,
});

// API route with validation
export const POST = withApiValidation({
  schema,
  async handler(data, req) {
    // Handle validated and sanitized data
  },
});
```

## API Route Protection

API routes can be protected using the `withApiValidation` helper:

```typescript
import { withApiValidation } from '@/lib/api-utils';

export const POST = withApiValidation({
  schema: yourZodSchema,
  handler: async (data, req) => {
    // Your handler logic
  },
  rateLimitType: 'api',
});
```

This wrapper provides:

- Rate limiting
- Input validation
- Data sanitization
- Error handling

## Role-Based Access Control

Routes can be protected based on user roles:

```typescript
// In your route configuration
const routes = {
  href: '/admin',
  roles: ['ADMIN']
};

// Middleware checks
if (matchingRoute?.roles && !matchingRoute.roles.includes(userRole)) {
  return NextResponse.redirect(new URL(REDIRECT_URL, nextUrl));
}
```

## HTML Sanitization

The application uses DOMPurify to sanitize HTML content, preventing XSS attacks and ensuring only safe HTML elements and attributes are allowed.

### Configuration

The sanitizer is configured to allow only specific HTML tags and attributes:

```typescript
const sanitizeConfig = {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
  ALLOWED_ATTR: ['href', 'target'],
  ALLOW_DATA_ATTR: false,
  RETURN_DOM_FRAGMENT: false,
  RETURN_DOM: false,
  SANITIZE_DOM: true,
};
```

### Usage Examples

1. Sanitize a single HTML string:

```typescript
import { sanitizeHtml } from '@/lib/sanitize';

const dirty = '<p>Hello <script>alert("xss")</script></p>';
const clean = sanitizeHtml(dirty);
// Result: '<p>Hello </p>'
```

2. Sanitize specific fields in an object:

```typescript
import { sanitizeFields } from '@/lib/sanitize';

const data = {
  title: '<p>Safe title</p>',
  content: '<p>Content with <script>alert("xss")</script></p>',
  metadata: { author: 'John' },
};

const clean = sanitizeFields(data, ['title', 'content']);
```

3. Sanitize all string fields in an object:

```typescript
import { sanitizeObject } from '@/lib/sanitize';

const data = {
  title: '<p>Title</p>',
  content: '<p>Content</p>',
  views: 100,
};

const clean = sanitizeObject(data);
// Only string fields are sanitized, numbers remain unchanged
```

### Error Handling

The sanitization functions will throw a `ValidationError` if the input cannot be sanitized:

```typescript
try {
  const clean = sanitizeHtml(dirtyInput);
} catch (error) {
  // Error will include:
  // - message: "Invalid HTML content"
  // - description: "The provided HTML content could not be sanitized"
  // - cause: Original error
}
```

### Best Practices

1. Always sanitize user-provided HTML content before storing or displaying it
2. Use `sanitizeFields` when you need to sanitize specific fields only
3. Use `sanitizeObject` when all string fields in an object need sanitization
4. Handle sanitization errors appropriately in your application logic

## Best Practices

1. **Always validate input**: Use the provided validation schemas for consistent validation across the application.

2. **Rate limiting**: Apply appropriate rate limits based on the endpoint's sensitivity.

3. **CORS**: Only allow necessary origins and methods.

4. **Sanitization**: Always sanitize user-provided HTML content using the `sanitizeHtml` function.

5. **Error handling**: Use the predefined error types from `@/lib/errors` for consistent error responses.

## Configuration

The middleware configuration can be adjusted in several files:

- `src/middleware.ts`: Main middleware logic
- `src/middleware/cors.ts`: CORS settings
- `src/lib/rate-limit.ts`: Rate limiting configuration
- `src/constants.ts`: Route definitions and constants

## Error Handling

The middleware provides consistent error responses:

```typescript
// Example error response
{
  error: {
    message: "Rate limit exceeded",
    code: "RATE_LIMIT_EXCEEDED",
    details: {
      reset: 3600,
      limit: 100
    }
  }
}
```

For more information about specific error types and handling, see the [Error Handling](/docs/error-handling) documentation.
