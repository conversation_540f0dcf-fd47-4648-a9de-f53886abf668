import { NextAuthConfig } from 'next-auth';
import Github from 'next-auth/providers/github';
import Google from 'next-auth/providers/google';
import Resend from 'next-auth/providers/resend';

import { sendVerificationRequest } from './lib/send-verification-request';

export default {
  providers: [
    Github,
    Google,
    Resend({
      id: 'email',
      name: 'email',
      server: {
        host: process.env.EMAIL_HOST as string,
        port: parseInt(process.env.EMAIL_PORT as string),
        auth: {
          user: process.env.EMAIL_USER as string,
          pass: process.env.EMAIL_PASS as string,
        },
      },
      from: process.env.EMAIL_FROM as string,
      sendVerificationRequest,
    }),
  ],
} satisfies NextAuthConfig;
