import { login, loginOAuth, logOut } from '@/actions/auth';
import { signIn, signOut } from '@/auth';
import { REDIRECT_URL } from '@/constants';

// Mock the auth functions
jest.mock('@/auth', () => ({
  signIn: jest.fn(),
  signOut: jest.fn(),
}));

describe('Auth Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should call signIn with email provider and correct parameters', async () => {
      const email = '<EMAIL>';
      const customRedirect = '/dashboard';

      await login({ email }, customRedirect);

      expect(signIn).toHaveBeenCalledWith('email', {
        email,
        redirectTo: customRedirect,
      });
    });

    it('should use default redirect URL when no redirect provided', async () => {
      const email = '<EMAIL>';

      await login({ email });

      expect(signIn).toHaveBeenCalledWith('email', {
        email,
        redirectTo: REDIRECT_URL,
      });
    });

    it('should throw validation error for invalid email', async () => {
      const invalidEmail = 'not-an-email';

      await expect(login({ email: invalidEmail })).rejects.toThrow(
        'Email is not valid!'
      );
    });

    it('should propagate signIn errors', async () => {
      const email = '<EMAIL>';
      const error = new Error('Auth failed');
      (signIn as jest.Mock).mockRejectedValueOnce(error);

      await expect(login({ email })).rejects.toThrow();
    });
  });

  describe('loginOAuth', () => {
    it('should call signIn with github provider and correct parameters', async () => {
      const customRedirect = '/dashboard';

      await loginOAuth('github', customRedirect);

      expect(signIn).toHaveBeenCalledWith('github', {
        redirectTo: customRedirect,
      });
    });

    it('should call signIn with google provider and correct parameters', async () => {
      const customRedirect = '/dashboard';

      await loginOAuth('google', customRedirect);

      expect(signIn).toHaveBeenCalledWith('google', {
        redirectTo: customRedirect,
      });
    });

    it('should use default redirect URL when no redirect provided', async () => {
      await loginOAuth('github');

      expect(signIn).toHaveBeenCalledWith('github', {
        redirectTo: REDIRECT_URL,
      });
    });

    it('should propagate signIn errors', async () => {
      const error = new Error('OAuth failed');
      (signIn as jest.Mock).mockRejectedValueOnce(error);

      await expect(loginOAuth('github')).rejects.toThrow();
    });
  });

  describe('logOut', () => {
    it('should call signOut with correct redirect parameter', async () => {
      await logOut();

      expect(signOut).toHaveBeenCalledWith({
        redirectTo: '/signin',
      });
    });

    it('should propagate signOut errors', async () => {
      const error = new Error('Logout failed');
      (signOut as jest.Mock).mockRejectedValueOnce(error);

      await expect(logOut()).rejects.toThrow();
    });
  });
});
