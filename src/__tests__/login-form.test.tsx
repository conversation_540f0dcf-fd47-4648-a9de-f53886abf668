import { useRouter, useSearchParams } from 'next/navigation';
import { login, loginOAuth } from '@/actions/auth';
import userEvent from '@testing-library/user-event';
import { toast } from 'sonner';

import { render, screen, waitFor } from '@/lib/test-utils';
// Import the actual component
import LoginForm from '@/components/auth/login-form';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  })),
  useSearchParams: jest.fn(() => ({
    get: jest.fn((param) => {
      // Default implementation
      if (param === 'callbackUrl') return '';
      if (param === 'error') return null;
      return null;
    }),
  })),
}));

// Mock dependencies
jest.mock('@/actions/auth', () => ({
  login: jest.fn(),
  loginOAuth: jest.fn(),
}));

jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));

describe('LoginForm', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks
    (useRouter as jest.Mock).mockImplementation(() => mockRouter);
    (useSearchParams as jest.Mock).mockImplementation(() => ({
      get: jest.fn((param) => {
        if (param === 'callbackUrl') return '';
        if (param === 'error') return null;
        return null;
      }),
      toString: () => '',
    }));
  });

  it('should render a login form with email input and submit button', () => {
    render(<LoginForm />);

    expect(
      screen.getByRole('heading', { name: /sign in/i })
    ).toBeInTheDocument();
    expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /Send magic link/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /sign in with github/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /sign in with google/i })
    ).toBeInTheDocument();
    expect(screen.getByText(/or continue with/i)).toBeInTheDocument();
    expect(screen.getByText(/this is your email address/i)).toBeInTheDocument();
  });

  describe('Form Validation', () => {
    it('should show error for invalid email format', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      await user.type(screen.getByLabelText(/email/i), 'invalid-email');
      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );

      await waitFor(() => {
        expect(screen.getByText(/invalid email/i)).toBeInTheDocument();
      });
      expect(login).not.toHaveBeenCalled();
    });

    it('should show error for empty email', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );

      await waitFor(() => {
        expect(screen.getByText(/Invalid email/i)).toBeInTheDocument();
      });
      expect(login).not.toHaveBeenCalled();
    });

    it('should clear validation errors when typing valid email', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      // First trigger validation error
      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );
      expect(screen.getByText(/Invalid email/i)).toBeInTheDocument();

      // Then type valid email
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');

      await waitFor(() => {
        expect(screen.queryByText(/Invalid email/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should call login function with email and empty redirect URL when form is submitted', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );

      await waitFor(() => {
        expect(login).toHaveBeenCalledWith({ email: '<EMAIL>' }, '');
      });
    });

    it('should call login function with redirect URL when provided in search params', async () => {
      const user = userEvent.setup();
      (useSearchParams as jest.Mock).mockImplementation(() => ({
        get: jest.fn((param) => {
          if (param === 'callbackUrl') return '/dashboard';
          return null;
        }),
        toString: () => 'callbackUrl=/dashboard',
      }));

      render(<LoginForm />);

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );

      await waitFor(() => {
        expect(login).toHaveBeenCalledWith(
          { email: '<EMAIL>' },
          '/dashboard'
        );
      });
    });
  });

  describe('OAuth Authentication', () => {
    it('should call loginOAuth with github provider and empty redirect URL', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      await user.click(
        screen.getByRole('button', { name: /sign in with github/i })
      );

      expect(loginOAuth).toHaveBeenCalledWith('github', '');
    });

    it('should call loginOAuth with google provider and empty redirect URL', async () => {
      const user = userEvent.setup();
      render(<LoginForm />);

      await user.click(
        screen.getByRole('button', { name: /sign in with google/i })
      );

      expect(loginOAuth).toHaveBeenCalledWith('google', '');
    });

    it('should call loginOAuth with redirect URL when provided', async () => {
      const user = userEvent.setup();
      (useSearchParams as jest.Mock).mockImplementation(() => ({
        get: jest.fn((param) => {
          if (param === 'callbackUrl') return '/dashboard';
          return null;
        }),
        toString: () => 'callbackUrl=/dashboard',
      }));

      render(<LoginForm />);

      await user.click(
        screen.getByRole('button', { name: /sign in with github/i })
      );

      expect(loginOAuth).toHaveBeenCalledWith('github', '/dashboard');
    });
  });

  describe('Error Handling', () => {
    it('should handle and display login error', async () => {
      const user = userEvent.setup();
      const error = new Error('Login failed');
      (login as jest.Mock).mockRejectedValueOnce(error);

      render(<LoginForm />);

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.click(
        screen.getByRole('button', { name: /Send magic link/i })
      );

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'An error occurred during login. Please try again.',
          { description: 'Failed to login. Please try again. Login failed' }
        );
      });
    });

    it('should handle and display OAuth error', async () => {
      const user = userEvent.setup();
      const error = new Error('OAuth failed');
      (loginOAuth as jest.Mock).mockRejectedValueOnce(error);

      render(<LoginForm />);

      await user.click(
        screen.getByRole('button', { name: /sign in with github/i })
      );

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'An error occurred during login. Please try again.',
          { description: 'Failed to login. Please try again. OAuth failed' }
        );
      });
    });

    it('should handle OAuthAccountNotLinked error', async () => {
      (useSearchParams as jest.Mock).mockImplementation(() => ({
        get: jest.fn((param) => {
          if (param === 'error') return 'OAuthAccountNotLinked';
          return null;
        }),
        toString: () => 'error=OAuthAccountNotLinked',
      }));

      render(<LoginForm />);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'This email is already associated with another account. Please use a different login method.'
        );
      });
      expect(mockRouter.replace).toHaveBeenCalled();
    });

    it('should handle CredentialsSignin error', async () => {
      (useSearchParams as jest.Mock).mockImplementation(() => ({
        get: jest.fn((param) => {
          if (param === 'error') return 'CredentialsSignin';
          return null;
        }),
        toString: () => 'error=CredentialsSignin',
      }));

      render(<LoginForm />);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'Invalid credentials. Please check your email and password.'
        );
      });
      expect(mockRouter.replace).toHaveBeenCalled();
    });

    it('should handle unknown error codes', async () => {
      (useSearchParams as jest.Mock).mockImplementation(() => ({
        get: jest.fn((param) => {
          if (param === 'error') return 'UnknownError';
          return null;
        }),
        toString: () => 'error=UnknownError',
      }));

      render(<LoginForm />);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'An error occurred during login. Please try again.'
        );
      });
      expect(mockRouter.replace).toHaveBeenCalled();
    });
  });
});
