import { useParams, usePathname } from 'next/navigation';
import { createPost, updatePost } from '@/actions/posts';
import { uploadFile } from '@/actions/upload';
import userEvent from '@testing-library/user-event';
import { toast } from 'sonner';

import { Post } from '@/types/post.types';
import { render, screen, waitFor } from '@/lib/test-utils';
import PostForm from '@/components/posts/post-form/post-form';

// Mock dependencies
jest.mock('@/actions/posts', () => ({
  createPost: jest.fn(),
  updatePost: jest.fn(),
}));

jest.mock('@/actions/upload', () => ({
  uploadFile: jest.fn(),
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock URL.createObjectURL
const mockCreateObjectURL = jest.fn();
global.URL.createObjectURL = mockCreateObjectURL;

// Mock data
const mockTags = [
  { id: '1', name: 'React', slug: 'react' },
  { id: '2', name: 'TypeScript', slug: 'typescript' },
];

const mockPost = {
  id: '1',
  title: 'Test Post',
  content: {
    type: 'doc',
    content: [
      { type: 'paragraph', content: [{ type: 'text', text: 'Test content' }] },
    ],
  },
  cover: 'https://example.com/image.jpg',
  published: false,
  tags: [{ name: 'React', slug: 'react', id: '1' }],
  excerpt: 'Test excerpt',
  slug: 'test-post',
  createdAt: new Date(),
  updatedAt: new Date(),
  author: {
    id: '1',
    name: 'Test User',
    image: 'https://example.com/user.jpg',
    email: '<EMAIL>',
    username: 'testuser',
    bio: null,
    urls: [],
  },
  _count: {
    comments: 0,
    likes: 0,
    bookmarks: 0,
  },
  authorId: '1',
};

describe('PostForm', () => {
  const renderPostForm = (
    initialValues: Post | null = null,
    tags = mockTags
  ) => {
    // Mock useParams for edit route when initialValues are provided
    if (initialValues) {
      (useParams as jest.Mock).mockReturnValue({ slug: initialValues.slug });
      (usePathname as jest.Mock).mockReturnValue(
        `/posts/${initialValues.slug}/edit`
      );
    } else {
      (useParams as jest.Mock).mockReturnValue({});
      (usePathname as jest.Mock).mockReturnValue('/posts/new');
    }

    return render(<PostForm initialValues={initialValues} tags={tags} />);
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Set up the mock implementation for each test
    mockCreateObjectURL.mockReturnValue('blob:mock-url');
  });

  it('renders empty form when no initial values provided', () => {
    renderPostForm();

    // Check for form elements
    expect(screen.getByText(/Drag and drop to upload/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /add tags/i })
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/untitled/i)).toBeInTheDocument();
  });

  it('renders form with initial values when editing existing post', () => {
    renderPostForm(mockPost);

    // Check if initial values are populated
    expect(screen.getByDisplayValue(mockPost.title)).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument(); // Tag should be visible
    // Check for image with Next.js transformed URL
    const image = screen.getByRole('img');
    expect(image).toHaveAttribute(
      'src',
      expect.stringContaining(encodeURIComponent(mockPost.cover))
    );
  });

  it('handles tag management', async () => {
    const user = userEvent.setup();
    renderPostForm();

    // Add tag button should be visible initially
    const addTagButton = screen.getByRole('button', { name: /add tags/i });
    await user.click(addTagButton);

    // Type and select a tag
    const tagInput = screen.getByPlaceholderText(/add a tag/i);
    await user.type(tagInput, 'React');
    const tagOption = screen.getByText('React');
    await user.click(tagOption);

    // // Tag should be visible and removable
    expect(screen.getByText('React')).toBeInTheDocument();
    const removeButton = screen.getByRole('button', {
      name: /remove react tag/i,
    });
    await user.click(removeButton);
    expect(screen.queryByText('React')).not.toBeInTheDocument();
  });

  it('handles cover image management', async () => {
    const user = userEvent.setup();
    renderPostForm();

    // Mock file upload response
    (uploadFile as jest.Mock).mockResolvedValueOnce({
      url: 'https://example.com/uploaded.jpg',
    });

    // Create a test file
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    // Find the file input and upload
    const input = screen.getByTestId('cover-image-input');
    await user.upload(input, file);

    // First, verify the blob URL is shown
    expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');

    // Remove cover image using the mobile remove button (since it's always visible)
    const removeButton = screen.getByTestId('desktop-remove-cover-button');
    await user.click(removeButton);

    // Verify image is removed
    await waitFor(() => {
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });
  });

  it('handles form submission for new post', async () => {
    const user = userEvent.setup();
    renderPostForm();

    // Fill form
    await user.type(screen.getByPlaceholderText(/untitled/i), 'New Post');

    // Add a tag
    const addTagButton = screen.getByRole('button', { name: /add tags/i });
    await user.click(addTagButton);
    const tagInput = screen.getByPlaceholderText(/add a tag/i);
    await user.type(tagInput, 'React{Enter}');

    // Mock successful post creation
    (createPost as jest.Mock).mockResolvedValueOnce({
      id: 'new-post-id',
      slug: 'new-post',
    });

    // Submit form
    const saveButton = screen.getByRole('button', { name: /save draft/i });
    await user.click(saveButton);

    // Verify submission
    await waitFor(() => {
      expect(createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'New Post',
          tags: [{ name: 'React', slug: 'react' }],
          published: false,
        })
      );
    });

    expect(toast.success).toHaveBeenCalledWith(
      'Your draft has been saved successfully.'
    );
  });

  it('handles form submission for existing post publish', async () => {
    const user = userEvent.setup();
    renderPostForm(mockPost);

    // Update title
    const titleInput = screen.getByDisplayValue(mockPost.title);
    await user.clear(titleInput);
    await user.type(titleInput, 'Updated Post');

    // Mock successful post update
    (updatePost as jest.Mock).mockResolvedValueOnce({
      id: mockPost.id,
      slug: mockPost.slug,
    });

    // Submit form
    const saveButton = screen.getByRole('button', { name: /publish/i });
    await user.click(saveButton);

    // Verify update
    await waitFor(() => {
      expect(updatePost).toHaveBeenCalledWith(
        mockPost.slug,
        expect.objectContaining({
          title: 'Updated Post',
        })
      );
    });

    expect(toast.success).toHaveBeenCalledWith(
      'Your post has been published successfully.'
    );
  });

  it('handles form submission for existing post draft', async () => {
    const user = userEvent.setup();
    renderPostForm(mockPost);

    // Update title
    const titleInput = screen.getByDisplayValue(mockPost.title);
    await user.clear(titleInput);
    await user.type(titleInput, 'Updated Post');

    // Mock successful post update
    (updatePost as jest.Mock).mockResolvedValueOnce({
      id: mockPost.id,
      slug: mockPost.slug,
    });

    // Submit form
    const saveButton = screen.getByRole('button', { name: /save draft/i });
    await user.click(saveButton);

    // Verify update
    await waitFor(() => {
      expect(updatePost).toHaveBeenCalledWith(
        mockPost.slug,
        expect.objectContaining({
          title: 'Updated Post',
        })
      );
    });

    expect(toast.success).toHaveBeenCalledWith(
      'Your draft has been updated successfully.'
    );
  });

  it('handles form submission errors draft', async () => {
    const user = userEvent.setup();
    renderPostForm();

    // Mock error
    const error = new Error('Failed to save post');
    (createPost as jest.Mock).mockRejectedValueOnce(error);

    // Submit form
    const saveButton = screen.getByRole('button', { name: /save draft/i });
    await user.click(saveButton);

    // Verify error handling
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Error saving draft', {
        description: expect.stringContaining(
          'Failed to save draft. Please try again.'
        ),
      });
    });
  });
});
