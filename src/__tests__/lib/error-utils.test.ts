// src/lib/errors.test.ts (or appropriate path)

import {
  AppError,
  DefaultStatusCodes,
  ErrorCodes,
  ErrorContext,
  Errors,
  HttpStatusCode,
} from '@/lib/error-utils';

// Adjust the import path as necessary

describe('Error Definitions and Factory (errors.ts)', () => {
  describe('HttpStatusCode and ErrorCodes', () => {
    it('should have correct numeric values for HttpStatusCode', () => {
      expect(HttpStatusCode.OK).toBe(200);
      expect(HttpStatusCode.NOT_FOUND).toBe(404);
      expect(HttpStatusCode.INTERNAL_SERVER_ERROR).toBe(500);
    });

    it('should have correct string values for ErrorCodes', () => {
      expect(ErrorCodes.UNAUTHORIZED).toBe('UNAUTHORIZED');
      expect(ErrorCodes.NOT_FOUND).toBe('NOT_FOUND');
      expect(ErrorCodes.SERVER_ERROR).toBe('SERVER_ERROR');
    });
  });

  describe('DefaultStatusCodes Mapping', () => {
    it('should map ErrorCodes to correct default HttpStatusCode', () => {
      expect(DefaultStatusCodes[ErrorCodes.NOT_FOUND]).toBe(
        HttpStatusCode.NOT_FOUND
      );
      expect(DefaultStatusCodes[ErrorCodes.VALIDATION_ERROR]).toBe(
        HttpStatusCode.UNPROCESSABLE_ENTITY
      );
      expect(DefaultStatusCodes[ErrorCodes.DATABASE_ERROR]).toBe(
        HttpStatusCode.INTERNAL_SERVER_ERROR
      );
      expect(DefaultStatusCodes[ErrorCodes.ALREADY_EXISTS]).toBe(
        HttpStatusCode.CONFLICT
      );
    });

    it('should cover all ErrorCodes', () => {
      // Ensure every defined ErrorCode has a mapping
      const definedErrorCodes = Object.values(ErrorCodes);
      const mappedErrorCodes = Object.keys(DefaultStatusCodes);
      expect(mappedErrorCodes).toEqual(
        expect.arrayContaining(definedErrorCodes)
      );
      expect(definedErrorCodes).toEqual(
        expect.arrayContaining(mappedErrorCodes)
      );
    });
  });

  describe('AppError Class', () => {
    it('should create an instance with message, code, default statusCode, and no context', () => {
      const error = new AppError('Resource not found', ErrorCodes.NOT_FOUND);

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(AppError);
      expect(error.message).toBe('Resource not found');
      expect(error.code).toBe(ErrorCodes.NOT_FOUND);
      expect(error.statusCode).toBe(HttpStatusCode.NOT_FOUND); // Default mapping
      expect(error.context).toBeUndefined();
      expect(error.name).toBe('AppError');
    });

    it('should allow overriding the default statusCode', () => {
      const error = new AppError(
        'Special case not found',
        ErrorCodes.NOT_FOUND,
        HttpStatusCode.BAD_REQUEST // Override default 404
      );

      expect(error.code).toBe(ErrorCodes.NOT_FOUND);
      expect(error.statusCode).toBe(HttpStatusCode.BAD_REQUEST);
    });

    it('should accept and store context', () => {
      const context: ErrorContext = {
        field: 'email',
        value: '<EMAIL>',
        description: 'User email lookup failed',
      };
      const error = new AppError(
        'User lookup failed',
        ErrorCodes.NOT_FOUND,
        HttpStatusCode.NOT_FOUND,
        context
      );

      expect(error.context).toBeDefined();
      expect(error.context).toEqual(context);
      expect(error.context?.field).toBe('email');
    });

    it('should retain prototype chain for instanceof checks', () => {
      const error = new AppError('Generic Error', ErrorCodes.SERVER_ERROR);
      expect(error instanceof AppError).toBe(true);
      expect(error instanceof Error).toBe(true);
    });
  });

  describe('Errors Factory', () => {
    // Test each factory function
    it('Errors.Unauthorized should create correct AppError', () => {
      const context = { userId: 'user123' };
      const errorDefault = Errors.Unauthorized();
      const errorCustomMsg = Errors.Unauthorized('Custom auth message');
      const errorWithContext = Errors.Unauthorized(undefined, context); // Use default message

      expect(errorDefault.message).toBe('Unauthorized access');
      expect(errorDefault.code).toBe(ErrorCodes.UNAUTHORIZED);
      expect(errorDefault.statusCode).toBe(HttpStatusCode.UNAUTHORIZED);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Custom auth message');
      expect(errorCustomMsg.code).toBe(ErrorCodes.UNAUTHORIZED);

      expect(errorWithContext.message).toBe('Unauthorized access');
      expect(errorWithContext.code).toBe(ErrorCodes.UNAUTHORIZED);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.NotFound should create correct AppError', () => {
      const context = { resourceId: 404 };
      const errorDefault = Errors.NotFound();
      const errorCustomResource = Errors.NotFound('User Profile');
      const errorWithContext = Errors.NotFound(undefined, context);

      expect(errorDefault.message).toBe('Resource not found');
      expect(errorDefault.code).toBe(ErrorCodes.NOT_FOUND);
      expect(errorDefault.statusCode).toBe(HttpStatusCode.NOT_FOUND);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomResource.message).toBe('User Profile not found');
      expect(errorCustomResource.code).toBe(ErrorCodes.NOT_FOUND);

      expect(errorWithContext.message).toBe('Resource not found'); // Uses default resource name
      expect(errorWithContext.code).toBe(ErrorCodes.NOT_FOUND);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.ValidationError should create correct AppError', () => {
      const context = { issues: [{ path: ['email'], message: 'Invalid' }] };
      const error = Errors.ValidationError('Input validation failed', context);

      expect(error.message).toBe('Input validation failed');
      expect(error.code).toBe(ErrorCodes.VALIDATION_ERROR);
      expect(error.statusCode).toBe(HttpStatusCode.UNPROCESSABLE_ENTITY);
      expect(error.context).toEqual(context);
    });

    it('Errors.RateLimitExceeded should create correct AppError', () => {
      const context = { limit: 100, window: '1m' };
      const errorDefault = Errors.RateLimitExceeded();
      const errorWithContext = Errors.RateLimitExceeded(context);

      expect(errorDefault.message).toBe(
        'Rate limit exceeded. Please try again later.'
      );
      expect(errorDefault.code).toBe(ErrorCodes.RATE_LIMIT_EXCEEDED);
      expect(errorDefault.statusCode).toBe(HttpStatusCode.TOO_MANY_REQUESTS);
      expect(errorDefault.context).toBeUndefined();

      expect(errorWithContext.message).toBe(
        'Rate limit exceeded. Please try again later.'
      );
      expect(errorWithContext.code).toBe(ErrorCodes.RATE_LIMIT_EXCEEDED);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.DatabaseError should create correct AppError', () => {
      const context = { query: 'SELECT * FROM users' };
      const errorDefault = Errors.DatabaseError();
      const errorCustomMsg = Errors.DatabaseError('Failed to connect');
      const errorWithContext = Errors.DatabaseError(undefined, context);

      expect(errorDefault.message).toBe('A database error occurred');
      expect(errorDefault.code).toBe(ErrorCodes.DATABASE_ERROR);
      expect(errorDefault.statusCode).toBe(
        HttpStatusCode.INTERNAL_SERVER_ERROR
      );
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Failed to connect');
      expect(errorCustomMsg.code).toBe(ErrorCodes.DATABASE_ERROR);

      expect(errorWithContext.message).toBe('A database error occurred');
      expect(errorWithContext.code).toBe(ErrorCodes.DATABASE_ERROR);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.NetworkError should create correct AppError', () => {
      const context = { targetHost: 'api.example.com' };
      const errorDefault = Errors.NetworkError();
      const errorCustomMsg = Errors.NetworkError('Timeout connecting');
      const errorWithContext = Errors.NetworkError(undefined, context);

      expect(errorDefault.message).toBe('A network error occurred');
      expect(errorDefault.code).toBe(ErrorCodes.NETWORK_ERROR);
      expect(errorDefault.statusCode).toBe(HttpStatusCode.SERVICE_UNAVAILABLE);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Timeout connecting');
      expect(errorCustomMsg.code).toBe(ErrorCodes.NETWORK_ERROR);

      expect(errorWithContext.message).toBe('A network error occurred');
      expect(errorWithContext.code).toBe(ErrorCodes.NETWORK_ERROR);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.Forbidden should create correct AppError', () => {
      const context = { requiredRole: 'admin' };
      const errorDefault = Errors.Forbidden();
      const errorCustomMsg = Errors.Forbidden('Admin role required');
      const errorWithContext = Errors.Forbidden(undefined, context);

      expect(errorDefault.message).toBe('Permission denied');
      expect(errorDefault.code).toBe(ErrorCodes.FORBIDDEN);
      expect(errorDefault.statusCode).toBe(HttpStatusCode.FORBIDDEN);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Admin role required');
      expect(errorCustomMsg.code).toBe(ErrorCodes.FORBIDDEN);

      expect(errorWithContext.message).toBe('Permission denied');
      expect(errorWithContext.code).toBe(ErrorCodes.FORBIDDEN);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.ServerError should create correct AppError', () => {
      const context = { traceId: 'xyz789' };
      const errorDefault = Errors.ServerError();
      const errorCustomMsg = Errors.ServerError('Unexpected service failure');
      const errorWithContext = Errors.ServerError(undefined, context);

      expect(errorDefault.message).toBe('An internal server error occurred');
      expect(errorDefault.code).toBe(ErrorCodes.SERVER_ERROR);
      expect(errorDefault.statusCode).toBe(
        HttpStatusCode.INTERNAL_SERVER_ERROR
      );
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Unexpected service failure');
      expect(errorCustomMsg.code).toBe(ErrorCodes.SERVER_ERROR);

      expect(errorWithContext.message).toBe(
        'An internal server error occurred'
      );
      expect(errorWithContext.code).toBe(ErrorCodes.SERVER_ERROR);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.Conflict should create correct AppError', () => {
      const context = { conflictingField: 'username' };
      const errorDefault = Errors.Conflict();
      const errorCustomMsg = Errors.Conflict('Username already taken');
      const errorWithContext = Errors.Conflict(undefined, context);

      // Note: The code uses ALREADY_EXISTS, ensure mapping is correct
      expect(errorDefault.message).toBe('Resource conflict');
      expect(errorDefault.code).toBe(ErrorCodes.ALREADY_EXISTS); // Check code used in factory
      expect(errorDefault.statusCode).toBe(HttpStatusCode.CONFLICT);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Username already taken');
      expect(errorCustomMsg.code).toBe(ErrorCodes.ALREADY_EXISTS);

      expect(errorWithContext.message).toBe('Resource conflict');
      expect(errorWithContext.code).toBe(ErrorCodes.ALREADY_EXISTS);
      expect(errorWithContext.context).toEqual(context);
    });

    it('Errors.BadRequest should create correct AppError', () => {
      const context = { invalidParam: 'page' };
      const errorDefault = Errors.BadRequest();
      const errorCustomMsg = Errors.BadRequest('Invalid page number');
      const errorWithContext = Errors.BadRequest(undefined, context);

      // Note: The code uses INVALID_INPUT, ensure mapping is correct
      expect(errorDefault.message).toBe('Bad request');
      expect(errorDefault.code).toBe(ErrorCodes.INVALID_INPUT); // Check code used in factory
      expect(errorDefault.statusCode).toBe(HttpStatusCode.BAD_REQUEST);
      expect(errorDefault.context).toBeUndefined();

      expect(errorCustomMsg.message).toBe('Invalid page number');
      expect(errorCustomMsg.code).toBe(ErrorCodes.INVALID_INPUT);

      expect(errorWithContext.message).toBe('Bad request');
      expect(errorWithContext.code).toBe(ErrorCodes.INVALID_INPUT);
      expect(errorWithContext.context).toEqual(context);
    });
  });
});
