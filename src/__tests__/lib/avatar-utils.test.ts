import {
  getDefaultAvatar,
  getDeterministicAvatar,
  type DefaultAvatarType,
} from '@/lib/avatar-utils';

describe('avatar-utils', () => {
  describe('getDefaultAvatar', () => {
    it('should return correct path for default (man) avatar', () => {
      expect(getDefaultAvatar()).toBe('/default-avatars/man.svg');
    });

    it('should return correct paths for all avatar types', () => {
      const types: DefaultAvatarType[] = ['man', 'woman', 'boy', 'girl'];
      types.forEach((type) => {
        expect(getDefaultAvatar(type)).toBe(`/default-avatars/${type}.svg`);
      });
    });
  });

  describe('getDeterministicAvatar', () => {
    it('should return consistent avatar for same seed', () => {
      const seed = 'user123';
      const firstCall = getDeterministicAvatar(seed);
      const secondCall = getDeterministicAvatar(seed);
      expect(firstCall).toBe(secondCall);
    });

    it('should return different avatars for different seeds', () => {
      const seed1 = 'user123';
      const seed2 = 'user456';
      const avatar1 = getDeterministicAvatar(seed1);
      const avatar2 = getDeterministicAvatar(seed2);
      expect(avatar1).not.toBe(avatar2);
    });

    it('should handle empty string seed', () => {
      expect(() => getDeterministicAvatar('')).not.toThrow();
    });

    it('should handle long string seeds', () => {
      const longSeed = 'a'.repeat(1000);
      expect(() => getDeterministicAvatar(longSeed)).not.toThrow();
    });

    it('should only return valid avatar types', () => {
      // Test with 100 different seeds to ensure all returned paths are valid
      const validPaths = [
        '/default-avatars/man.svg',
        '/default-avatars/woman.svg',
        '/default-avatars/boy.svg',
        '/default-avatars/girl.svg',
      ];

      Array.from({ length: 100 }, (_, i) => `seed${i}`).forEach((seed) => {
        const result = getDeterministicAvatar(seed);
        expect(validPaths).toContain(result);
      });
    });

    it('should distribute avatars somewhat evenly', () => {
      // Test with 1000 seeds to check distribution
      const distribution: Record<string, number> = {};

      Array.from({ length: 1000 }, (_, i) => `seed${i}`).forEach((seed) => {
        const avatar = getDeterministicAvatar(seed);
        distribution[avatar] = (distribution[avatar] || 0) + 1;
      });

      // Check that each avatar type is used at least 150 times (rough distribution check)
      Object.values(distribution).forEach((count) => {
        expect(count).toBeGreaterThan(150);
      });
    });

    it('should handle special characters in seed', () => {
      const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      expect(() => getDeterministicAvatar(specialChars)).not.toThrow();
    });

    it('should handle unicode characters in seed', () => {
      const unicodeChars = '你好世界😀🌍';
      expect(() => getDeterministicAvatar(unicodeChars)).not.toThrow();
    });
  });
});
