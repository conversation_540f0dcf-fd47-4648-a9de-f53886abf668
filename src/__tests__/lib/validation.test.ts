import { z } from 'zod';

// Adjust path as necessary

// Import error types for checking thrown errors
import { AppError, ErrorCodes } from '@/lib/error-utils'; // Adjust path

// Import and mock the sanitizeHtml dependency

// Import the utilities and schemas to test
import {
  createSchema,
  validateInput,
  ValidationSchemas,
} from '@/lib/validation-utils';
import { sanitizeHtml } from '@/lib/validation-utils/sanitize'; // Adjust path

jest.mock('@/lib/validation-utils/sanitize', () => ({
  // Mock implementation: returns a predictable string for testing transforms
  sanitizeHtml: jest.fn((input: string) => `sanitized-${input}`),
}));

// Type assertion helper for checking thrown AppErrors
const expectAppError = (
  error: unknown,
  expectedCode: string, // Use ErrorCodes enum value here
  expectedMessage?: string | RegExp
): AppError => {
  expect(error).toBeInstanceOf(AppError);
  const appError = error as AppError;
  expect(appError.code).toBe(expectedCode);
  if (expectedMessage) {
    if (expectedMessage instanceof RegExp) {
      expect(appError.message).toMatch(expectedMessage);
    } else {
      expect(appError.message).toBe(expectedMessage);
    }
  }
  expect(appError.context).toBeDefined();
  expect(appError.context?.issues).toBeInstanceOf(Array);
  expect(appError.context?.issues.length).toBeGreaterThan(0);
  return appError; // Return for further context checks if needed
};

describe('Validation Utilities (validation.ts)', () => {
  // Clear mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // --- ValidationSchemas ---
  describe('ValidationSchemas', () => {
    describe('email', () => {
      const schema = ValidationSchemas.email;

      it('should pass for valid emails', () => {
        expect(schema.safeParse('<EMAIL>').success).toBe(true);
        expect(schema.parse(' <EMAIL> ')).toBe('<EMAIL>'); // Check transforms
      });

      it('should fail for invalid emails', () => {
        const result = schema.safeParse('invalid-email');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            'Please enter a valid email address'
          );
        }
      });

      it('should fail for empty strings', () => {
        // Note: Zod's .email() allows empty string by default unless chained with .min(1) or .nonempty()
        // If empty is invalid, the schema should be updated. Assuming default Zod behavior here.
        // To make it fail on empty, add `.min(1, { message: 'Email cannot be empty' })` to the schema
        expect(schema.safeParse('').success).toBe(false); // Fails because default .email() considers "" invalid format
        if (!schema.safeParse('').success) {
          expect(schema.safeParse('').error?.issues[0].message).toBe(
            'Please enter a valid email address'
          );
        }
      });
    });

    describe('username', () => {
      const schema = ValidationSchemas.username;

      it('should pass for valid usernames', () => {
        expect(schema.safeParse('valid_user-123').success).toBe(true);
        expect(schema.parse('valid_user-123')).toBe('valid_user-123');
      });

      it('should fail for usernames that are too short', () => {
        const result = schema.safeParse('ab');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            'Username must be at least 3 characters'
          );
        }
      });

      it('should fail for usernames that are too long', () => {
        const longUsername = 'a'.repeat(31);
        const result = schema.safeParse(longUsername);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            'Username must be at most 30 characters'
          );
        }
      });

      it('should fail for usernames with invalid characters', () => {
        const result = schema.safeParse('invalid user!');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toMatch(
            /can only contain letters, numbers, underscores, and hyphens/
          );
        }
      });
    });

    describe('url', () => {
      const schema = ValidationSchemas.url;

      it('should pass for valid URLs', () => {
        expect(schema.safeParse('https://example.com').success).toBe(true);
        expect(
          schema.safeParse('http://localhost:3000/path?query=1').success
        ).toBe(true);
        expect(schema.parse(' https://example.com ')).toBe(
          'https://example.com'
        ); // check trim
      });

      it('should fail for invalid URLs', () => {
        const result = schema.safeParse('invalid-url');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            'Please enter a valid URL'
          );
        }
      });
      it('should fail for empty strings', () => {
        // .url() considers "" invalid
        expect(schema.safeParse('').success).toBe(false);
        if (!schema.safeParse('').success) {
          expect(schema.safeParse('').error?.issues[0].message).toBe(
            'Please enter a valid URL'
          );
        }
      });
    });

    describe('safeHtml', () => {
      const schema = ValidationSchemas.safeHtml;

      it('should call sanitizeHtml and return its result', () => {
        const inputHtml = '<p>Test content <script>alert("XSS")</script></p>';
        const expectedOutput = `sanitized-${inputHtml}`; // Based on mock

        const result = schema.parse(inputHtml);

        expect(sanitizeHtml).toHaveBeenCalledTimes(1);
        expect(sanitizeHtml).toHaveBeenCalledWith(inputHtml);
        expect(result).toBe(expectedOutput);
      });

      it('should handle empty string', () => {
        const inputHtml = '';
        const expectedOutput = `sanitized-${inputHtml}`;
        const result = schema.parse(inputHtml);
        expect(sanitizeHtml).toHaveBeenCalledWith(inputHtml);
        expect(result).toBe(expectedOutput);
      });
    });

    describe('password', () => {
      const schema = ValidationSchemas.password;

      it('should pass for valid passwords', () => {
        expect(schema.safeParse('Password123').success).toBe(true);
        expect(schema.safeParse('lettersANDnums8').success).toBe(true);
      });

      it('should fail for passwords that are too short', () => {
        const result = schema.safeParse('Pass1');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            'Password must be at least 8 characters'
          );
        }
      });

      it('should fail for passwords without a letter', () => {
        const result = schema.safeParse('12345678');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toMatch(
            /must contain at least one letter and one number/
          );
        }
      });

      it('should fail for passwords without a number', () => {
        const result = schema.safeParse('PasswordOnly');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toMatch(
            /must contain at least one letter and one number/
          );
        }
      });
    });
  });

  // --- validateInput ---
  describe('validateInput', () => {
    const testSchema = z.object({
      email: ValidationSchemas.email,
      name: z.string().min(1, { message: 'Name required' }),
    });
    const validator = validateInput(testSchema);

    it('should return validated data for valid input', async () => {
      const validInput = { email: ' <EMAIL> ', name: 'John Doe' };
      const expectedOutput = { email: '<EMAIL>', name: 'John Doe' };

      await expect(validator(validInput)).resolves.toEqual(expectedOutput);
    });

    it('should throw AppError with ValidationError code for invalid input', async () => {
      const invalidInput = { email: 'invalid-email', name: 'John Doe' };
      await expect(validator(invalidInput)).rejects.toThrow(AppError);
    });

    it('should throw correct AppError details for the first validation issue', async () => {
      const invalidInput = { email: '<EMAIL>', name: '' }; // Name is invalid
      try {
        await validator(invalidInput);
        fail('Should have thrown an error'); // Ensures the catch block is executed
      } catch (error) {
        const appError = expectAppError(
          error,
          ErrorCodes.VALIDATION_ERROR,
          'Name required'
        );
        expect(appError.context?.field).toBe('name'); // Path of the first issue
        expect(appError.context?.value).toBe('Name required'); // Message of the first issue
        expect(appError.context?.issues).toHaveLength(1); // Only one issue in this case
        expect(appError.context?.issues[0].path).toEqual(['name']);
      }
    });

    it('should include all issues in the error context', async () => {
      const invalidInput = { email: 'invalid', name: '' }; // Both email and name are invalid
      try {
        await validator(invalidInput);
        fail('Should have thrown an error');
      } catch (error) {
        // Error message and field will be based on the *first* issue Zod reports (usually email here)
        const appError = expectAppError(
          error,
          ErrorCodes.VALIDATION_ERROR,
          'Please enter a valid email address'
        );
        expect(appError.context?.field).toBe('email');
        expect(appError.context?.value).toBe(
          'Please enter a valid email address'
        );
        // Check that *all* issues are present in the context
        expect(appError.context?.issues).toHaveLength(2);
        const paths =
          appError.context?.issues.map((issue: any) => issue.path.join('.')) ||
          [];
        expect(paths).toContain('email');
        expect(paths).toContain('name');
      }
    });

    // Example using an async schema (though none defined in the file, conceptually important)
    it('should handle async schemas correctly', async () => {
      const asyncSchema = z.object({
        value: z
          .string()
          .refine(async (val) => val === 'valid', { message: 'Async fail' }),
      });
      const asyncValidator = validateInput(asyncSchema);

      await expect(asyncValidator({ value: 'valid' })).resolves.toEqual({
        value: 'valid',
      });

      try {
        await asyncValidator({ value: 'invalid' });
        fail('Should have thrown');
      } catch (error) {
        const appError = expectAppError(
          error,
          ErrorCodes.VALIDATION_ERROR,
          'Async fail'
        );
        expect(appError.context?.field).toBe('value');
        expect(appError.context?.issues).toHaveLength(1);
      }
    });
  });

  // --- createSchema ---
  describe('createSchema', () => {
    it('should create a Zod object schema from the raw shape', () => {
      const rawShape = {
        id: z.string().uuid(),
        count: z.number().int().positive(),
      };
      const schema = createSchema(rawShape);

      // Check if it's a Zod object
      expect(schema).toBeInstanceOf(z.ZodObject);

      // Test basic parsing
      const validData = {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        count: 5,
      };
      const invalidData = { id: 'invalid-uuid', count: 5 };
      const invalidData2 = {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        count: -1,
      };

      expect(schema.safeParse(validData).success).toBe(true);
      expect(schema.parse(validData)).toEqual(validData); // Check parsed data
      expect(schema.safeParse(invalidData).success).toBe(false);
      expect(schema.safeParse(invalidData2).success).toBe(false);
    });

    it('should work with nested schemas created by createSchema', () => {
      const addressSchema = createSchema({
        street: z.string().min(1),
        city: z.string().min(1),
      });
      const userSchema = createSchema({
        name: z.string().min(1),
        address: addressSchema, // Nesting
      });

      const validUser = {
        name: 'Test',
        address: { street: '123 Main St', city: 'Anytown' },
      };
      const invalidUser = {
        name: 'Test',
        address: { street: '', city: 'Anytown' },
      }; // Invalid street

      expect(userSchema.safeParse(validUser).success).toBe(true);
      expect(userSchema.safeParse(invalidUser).success).toBe(false);
    });
  });
});
