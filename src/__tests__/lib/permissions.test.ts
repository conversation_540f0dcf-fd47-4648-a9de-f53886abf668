import { Role, Tier } from '@prisma/client';

import type { SessionUser } from '@/types/user.types';
import { type Action, type Resource } from '@/config/permissions.config';
import { db } from '@/lib/db';
import {
  PermissionService,
  permissionService,
  type PermissionCheck,
} from '@/lib/permissions';

// Mock Prisma client
jest.mock('@/lib/db', () => ({
  db: {
    plan: {
      findFirst: jest.fn(),
    },
    tenant: {
      count: jest.fn(),
    },
  },
}));

describe('PermissionService', () => {
  // Reset all mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock users for different roles
  const mockUsers: Record<Role, SessionUser> = {
    [Role.OWNER]: {
      id: 'owner-id',
      role: Role.OWNER,
      stripePriceId: 'price_enterprise',
    },
    [Role.ADMIN]: {
      id: 'admin-id',
      role: Role.ADMIN,
      stripePriceId: 'price_pro',
    },
    [Role.COLLABORATOR]: {
      id: 'collab-id',
      role: Role.COLLABORATOR,
      stripePriceId: 'price_starter',
    },
    [Role.USER]: {
      id: 'user-id',
      role: Role.USER,
      stripePriceId: undefined,
    },
  };

  describe('getInstance', () => {
    it('should return the same instance', () => {
      const instance1 = PermissionService.getInstance();
      const instance2 = PermissionService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('can', () => {
    it('should deny access for anonymous users', async () => {
      const check: PermissionCheck = {
        user: null,
        resource: 'post',
        action: 'create',
      };

      const result = await permissionService.can(check);
      expect(result).toBe(false);
    });

    it('should grant full access to OWNER role', async () => {
      const checks: Array<{ resource: Resource; action: Action }> = [
        { resource: 'post', action: 'create' },
        { resource: 'billing', action: 'read' },
        { resource: 'settings', action: 'update' },
      ];

      for (const check of checks) {
        const result = await permissionService.can({
          user: mockUsers.OWNER,
          ...check,
        });
        expect(result).toBe(true);
      }
    });

    it('should respect role-based permissions for non-owner roles', async () => {
      const testCases = [
        {
          role: Role.ADMIN,
          allowed: [
            { resource: 'post' as Resource, action: 'create' as Action },
            { resource: 'user' as Resource, action: 'read' as Action },
            { resource: 'billing' as Resource, action: 'update' as Action },
          ],
          denied: [
            { resource: 'plan' as Resource, action: 'create' as Action },
          ],
        },
        {
          role: Role.COLLABORATOR,
          allowed: [
            { resource: 'post' as Resource, action: 'create' as Action },
            { resource: 'comment' as Resource, action: 'update' as Action },
          ],
          denied: [
            { resource: 'user' as Resource, action: 'delete' as Action },
            { resource: 'billing' as Resource, action: 'read' as Action },
          ],
        },
        {
          role: Role.USER,
          allowed: [
            { resource: 'post' as Resource, action: 'read' as Action },
            { resource: 'comment' as Resource, action: 'create' as Action },
          ],
          denied: [
            { resource: 'user' as Resource, action: 'update' as Action },
            { resource: 'billing' as Resource, action: 'read' as Action },
          ],
        },
      ];

      for (const { role, allowed, denied } of testCases) {
        for (const { resource, action } of allowed) {
          const result = await permissionService.can({
            user: mockUsers[role],
            resource,
            action,
          });
          expect(result).toBe(true);
        }

        for (const { resource, action } of denied) {
          const result = await permissionService.can({
            user: mockUsers[role],
            resource,
            action,
          });
          expect(result).toBe(false);
        }
      }
    });

    describe('subscription tier checks', () => {
      beforeEach(() => {
        // Reset mock implementations
        (db.plan.findFirst as jest.Mock).mockResolvedValue(null);
        (db.tenant.count as jest.Mock).mockResolvedValue(0);
      });

      it('should check subscription tier when requiredPlan is specified', async () => {
        // Mock a PRO plan
        (db.plan.findFirst as jest.Mock).mockResolvedValue({
          tier: Tier.PRO,
        });

        const result = await permissionService.can({
          user: mockUsers.ADMIN,
          resource: 'post',
          action: 'create',
          requiredPlan: Tier.PRO,
        });

        expect(result).toBe(true);
        expect(db.plan.findFirst).toHaveBeenCalled();
      });

      it('should deny access when user tier is lower than required', async () => {
        // Mock a STARTER plan
        (db.plan.findFirst as jest.Mock).mockResolvedValue({
          tier: Tier.STARTER,
        });

        const result = await permissionService.can({
          user: mockUsers.USER,
          resource: 'post',
          action: 'create',
          requiredPlan: Tier.PRO,
        });

        expect(result).toBe(false);
      });

      it('should respect tenant limits based on subscription tier', async () => {
        // Mock PRO plan with 5 tenants limit
        (db.plan.findFirst as jest.Mock).mockResolvedValue({
          tier: Tier.PRO,
        });

        // Test with different tenant counts
        const testCases = [
          { count: 4, expected: true }, // Under limit
          { count: 5, expected: false }, // At limit
          { count: 6, expected: false }, // Over limit
        ];

        for (const { count, expected } of testCases) {
          (db.tenant.count as jest.Mock).mockResolvedValue(count);

          const result = await permissionService.can({
            user: mockUsers.ADMIN,
            resource: 'tenant',
            action: 'create',
            requiredPlan: Tier.PRO,
          });

          expect(result).toBe(expected);
        }
      });

      it('should handle missing subscription data gracefully', async () => {
        (db.plan.findFirst as jest.Mock).mockResolvedValue(null);

        const result = await permissionService.can({
          user: mockUsers.USER,
          resource: 'post',
          action: 'create',
          requiredPlan: Tier.PRO,
        });

        expect(result).toBe(false);
      });
    });
  });

  describe('checkMultiple', () => {
    it('should return true only if all checks pass', async () => {
      const checks: PermissionCheck[] = [
        {
          user: mockUsers.ADMIN,
          resource: 'post',
          action: 'create',
        },
        {
          user: mockUsers.ADMIN,
          resource: 'comment',
          action: 'delete',
        },
      ];

      const result = await permissionService.checkMultiple(checks);
      expect(result).toBe(true);
    });

    it('should return false if any check fails', async () => {
      const checks: PermissionCheck[] = [
        {
          user: mockUsers.USER,
          resource: 'post',
          action: 'create',
        },
        {
          user: mockUsers.USER,
          resource: 'billing',
          action: 'read',
        },
      ];

      const result = await permissionService.checkMultiple(checks);
      expect(result).toBe(false);
    });

    it('should handle empty checks array', async () => {
      const result = await permissionService.checkMultiple([]);
      expect(result).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Setup the mock to reject with a database error
      (db.plan.findFirst as jest.Mock).mockRejectedValueOnce(
        new Error('Database error')
      );

      // The permission check should complete without throwing
      const result = await permissionService.can({
        user: mockUsers.USER,
        resource: 'post' as Resource,
        action: 'create' as Action,
        requiredPlan: Tier.PRO,
      });

      // Verify the permission was denied due to the error
      expect(result).toBe(false);

      // Verify the database was actually called
      expect(db.plan.findFirst).toHaveBeenCalled();
    });

    it('should handle invalid role gracefully', async () => {
      const result = await permissionService.can({
        user: { ...mockUsers.USER, role: 'INVALID_ROLE' as Role },
        resource: 'post' as Resource,
        action: 'create' as Action,
      });

      expect(result).toBe(false);
    });
  });
});
