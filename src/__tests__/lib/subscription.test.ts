import { getPlans } from '@/actions/plans';
import { auth } from '@/auth';
import { FREE_PLAN } from '@/constants';
import { Role } from '@prisma/client';

import { db } from '@/lib/db';
import { stripe } from '@/lib/stripe';
import { getUserSubscriptions } from '@/lib/subscription';

// Mock auth
jest.mock('@/auth', () => ({
  auth: jest.fn().mockResolvedValue({
    user: {
      id: 'user-123',
      email: '<EMAIL>',
      role: 'USER',
      name: 'Test User',
      currentTenantId: 'tenant-123',
    },
  }),
}));

// Mock Stripe
jest.mock('@/lib/stripe', () => ({
  stripe: {
    subscriptions: {
      retrieve: jest.fn().mockResolvedValue({
        id: 'sub_123',
        status: 'active',
        cancel_at_period_end: false, // Set to false by default
        current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
        items: {
          data: [
            {
              price: { id: 'price_monthly_123' },
            },
          ],
        },
      }),
    },
    customers: {
      retrieve: jest.fn().mockResolvedValue({
        id: 'cus_123',
        email: '<EMAIL>',
      }),
    },
  },
}));

// Mock database
jest.mock('@/lib/db', () => ({
  db: {
    user: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
    subscription: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    tenant: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
  },
}));

// Mock plans action
jest.mock('@/actions/plans', () => ({
  getPlans: jest.fn().mockResolvedValue({
    items: [
      {
        id: 'plan-1',
        name: 'Pro Monthly',
        stripePriceIdMonthly: 'price_monthly_123',
        stripePriceIdYearly: 'price_yearly_123',
        features: ['feature1', 'feature2'],
        tier: 'PRO',
      },
      {
        id: 'plan-2',
        name: 'Enterprise',
        stripePriceIdMonthly: 'price_monthly_456',
        stripePriceIdYearly: 'price_yearly_456',
        features: ['feature1', 'feature2', 'feature3'],
        tier: 'ENTERPRISE',
      },
    ],
  }),
}));

// Mock constants
jest.mock('@/constants', () => ({
  FREE_PLAN: {
    id: 'free',
    name: 'Free',
    tier: 'FREE',
    features: ['basic1', 'basic2'],
  },
  REDIRECT_URL: '/dashboard',
}));

describe('getUserSubscriptions', () => {
  // Mock data
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: Role.USER,
    name: 'Test User',
    currentTenantId: 'tenant-123',
  };

  const mockPlans = {
    items: [
      {
        id: 'plan-1',
        name: 'Pro Monthly',
        stripePriceIdMonthly: 'price_monthly_123',
        stripePriceIdYearly: 'price_yearly_123',
        features: ['feature1', 'feature2'],
        tier: 'PRO',
      },
      {
        id: 'plan-2',
        name: 'Enterprise',
        stripePriceIdMonthly: 'price_monthly_456',
        stripePriceIdYearly: 'price_yearly_456',
        features: ['feature1', 'feature2', 'feature3'],
        tier: 'ENTERPRISE',
      },
    ],
  };

  const mockDbUser = {
    id: 'user-123',
    stripeCustomerId: 'cus_123',
    stripeSubscriptionId: 'sub_123',
    stripePriceId: 'price_monthly_123',
    stripeCurrentPeriodEnd: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days in future
    role: Role.USER,
    email: '<EMAIL>',
    name: 'Test User',
    currentTenantId: 'tenant-123',
  };

  const mockTenant = {
    id: 'tenant-123',
    name: 'Test Tenant',
    tier: 'PRO',
    stripeCustomerId: 'cus_123',
    stripeSubscriptionId: 'sub_123',
    stripePriceId: 'price_monthly_123',
    stripeCurrentPeriodEnd: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  };

  // Reset all mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    (auth as jest.Mock).mockResolvedValue({ user: mockUser });
    (getPlans as jest.Mock).mockResolvedValue(mockPlans);
    (db.user.findFirst as jest.Mock).mockResolvedValue(mockDbUser);
    (db.tenant.findFirst as jest.Mock).mockResolvedValue(mockTenant);

    // Reset Stripe subscription mock to default state
    (stripe.subscriptions.retrieve as jest.Mock).mockResolvedValue({
      id: 'sub_123',
      status: 'active',
      cancel_at_period_end: false,
      current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60,
      items: {
        data: [
          {
            price: { id: 'price_monthly_123' },
          },
        ],
      },
    });
  });

  it('should throw error if user is not authenticated', async () => {
    (auth as jest.Mock).mockResolvedValue(null);
    await expect(getUserSubscriptions()).rejects.toThrow('Not authenticated');
  });

  it('should throw error if user has no email', async () => {
    (auth as jest.Mock).mockResolvedValue({
      user: {
        id: 'user-123',
        role: Role.USER,
        currentTenantId: 'tenant-123',
      },
    });
    await expect(getUserSubscriptions()).rejects.toThrow('Not authenticated');
  });

  it('should return FREE_PLAN if no plans are available', async () => {
    (getPlans as jest.Mock).mockResolvedValue({ items: [] });

    const result = await getUserSubscriptions();

    expect(result).toEqual(FREE_PLAN);
  });

  it('should throw error if user is not found in database', async () => {
    (db.user.findFirst as jest.Mock).mockResolvedValue(null);

    await expect(getUserSubscriptions()).rejects.toThrow('User not found');
  });

  it('should return active subscription details for subscribed user', async () => {
    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...mockPlans.items[0], // First plan matches the stripePriceId
      ...mockDbUser,
      isSubscribed: true,
      isCanceled: false,
    });
  });

  it('should identify canceled subscription', async () => {
    (stripe.subscriptions?.retrieve as jest.Mock).mockResolvedValue({
      cancel_at_period_end: true,
    });

    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...mockPlans.items[0],
      ...mockDbUser,
      isSubscribed: true,
      isCanceled: true,
    });
  });

  it('should handle expired subscription', async () => {
    const expiredUser = {
      ...mockDbUser,
      stripeCurrentPeriodEnd: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    };
    (db.user.findFirst as jest.Mock).mockResolvedValue(expiredUser);

    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...mockPlans.items[0],
      ...expiredUser,
      isSubscribed: false,
      isCanceled: false,
    });
  });

  it('should return FREE_PLAN if no matching plan is found', async () => {
    const userWithUnknownPlan = {
      ...mockDbUser,
      stripePriceId: 'unknown_price_id',
    };
    (db.user.findFirst as jest.Mock).mockResolvedValue(userWithUnknownPlan);

    // Ensure subscription is not canceled
    (stripe.subscriptions.retrieve as jest.Mock).mockResolvedValue({
      cancel_at_period_end: false,
    });

    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...FREE_PLAN,
      ...userWithUnknownPlan,
      isSubscribed: true,
      isCanceled: false,
    });
  });

  it('should handle yearly subscription plan', async () => {
    const yearlySubscriber = {
      ...mockDbUser,
      stripePriceId: 'price_yearly_123',
    };
    (db.user.findFirst as jest.Mock).mockResolvedValue(yearlySubscriber);

    // Ensure subscription is not canceled
    (stripe.subscriptions.retrieve as jest.Mock).mockResolvedValue({
      cancel_at_period_end: false,
    });

    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...mockPlans.items[0],
      ...yearlySubscriber,
      isSubscribed: true,
      isCanceled: false,
    });
  });

  it('should handle user without subscription data', async () => {
    const unsubscribedUser = {
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      stripePriceId: null,
      stripeCurrentPeriodEnd: null,
    };
    (db.user.findFirst as jest.Mock).mockResolvedValue(unsubscribedUser);

    const result = await getUserSubscriptions();

    expect(result).toEqual({
      ...FREE_PLAN,
      ...unsubscribedUser,
      isSubscribed: null, // Updated to match the actual implementation
      isCanceled: false,
    });
  });

  it('should not check Stripe subscription if no subscription ID exists', async () => {
    const userWithoutSubId = {
      ...mockDbUser,
      stripeSubscriptionId: null,
    };
    (db.user.findFirst as jest.Mock).mockResolvedValue(userWithoutSubId);

    await getUserSubscriptions();

    expect(stripe.subscriptions?.retrieve).not.toHaveBeenCalled();
  });
});
