// import { clsx } from 'clsx'; // Import directly for mocking verification if needed
// import { twMerge } from 'tailwind-merge'; // Import directly for mocking verification if needed

import { SessionUser, User } from '@/types/user.types'; // Adjust path if needed

// Adjust path as necessary

// Mock the dependency from avatar-utils
import { getDeterministicAvatar } from '@/lib/avatar-utils'; // Adjust path

// Import the functions to test
import {
  absoluteUrl,
  capitalize,
  cn,
  formatCurrency,
  formatDate,
  getInitials,
  renderAvatar,
  renderUserName,
  safeParse,
  safeStringify,
  slugify,
  unslugiify, // Corrected typo from 'unslugiify' if it exists in your code
} from '@/lib/utils';

jest.mock('@/lib/avatar-utils', () => ({
  getDeterministicAvatar: jest.fn((id: string) => `deterministic-avatar-${id}`),
}));

// Mock process.env - important for absoluteUrl
const originalEnv = process.env;

describe('Utility Functions (utils.ts)', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    // Restore original process.env structure before mocking for a specific test
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    // Restore original env after all tests
    process.env = originalEnv;
  });

  // --- cn ---
  describe('cn (Classname utility)', () => {
    it('should merge class names correctly', () => {
      expect(cn('text-red-500', 'bg-blue-200')).toBe(
        'text-red-500 bg-blue-200'
      );
    });

    it('should handle conditional classes', () => {
      expect(
        cn('base', { 'conditional-true': true, 'conditional-false': false })
      ).toBe('base conditional-true');
    });

    it('should override conflicting Tailwind classes', () => {
      // Relies on tailwind-merge's logic
      expect(cn('p-4', 'p-2')).toBe('p-2');
      expect(cn('bg-red-500', 'bg-blue-500')).toBe('bg-blue-500');
    });

    it('should handle arrays and mixed types', () => {
      expect(cn('p-4', ['m-2', 'block'], { 'text-center': true })).toBe(
        'p-4 m-2 block text-center'
      );
    });

    it('should handle null and undefined inputs gracefully', () => {
      expect(cn('p-4', null, 'm-2', undefined, 'block')).toBe('p-4 m-2 block');
    });
  });

  // --- formatDate ---
  describe('formatDate', () => {
    it('should format a Date object correctly', () => {
      const date = new Date(2023, 9, 26); // Month is 0-indexed (9 = October)
      expect(formatDate(date)).toBe('October 26, 2023');
    });

    it('should format a date string correctly', () => {
      const dateString = '2024-01-15T10:30:00.000Z';
      expect(formatDate(dateString)).toBe('January 15, 2024');
    });

    it('should format a timestamp number correctly', () => {
      const timestamp = new Date('2022-12-25').getTime();
      expect(formatDate(timestamp)).toBe('December 25, 2022');
    });

    // Note: Behavior with invalid input relies on `new Date()` behavior
    it('should handle potentially invalid input (relies on Date constructor)', () => {
      expect(formatDate('invalid-date-string')).toBe('Invalid Date'); // Default JS behavior
    });
  });

  // --- absoluteUrl ---
  describe('absoluteUrl', () => {
    const OLD_ENV = process.env;

    beforeEach(() => {
      jest.resetModules(); // Most important - it clears the cache
      process.env = { ...OLD_ENV }; // Make a copy
    });

    afterAll(() => {
      process.env = OLD_ENV; // Restore old environment
    });

    it('should prepend the domain from NEXT_PUBLIC_DOMAIN', () => {
      process.env.NEXT_PUBLIC_DOMAIN = 'https://example.com';
      expect(absoluteUrl('/path/to/resource')).toBe(
        'https://example.com/path/to/resource'
      );
    });

    it('should handle paths without leading slash', () => {
      process.env.NEXT_PUBLIC_DOMAIN = 'https://sub.example.com';
      expect(absoluteUrl('another/path')).toBe(
        'https://sub.example.com/another/path'
      );
    });

    it('should handle empty path', () => {
      process.env.NEXT_PUBLIC_DOMAIN = 'http://localhost:3000';
      expect(absoluteUrl('')).toBe('http://localhost:3000');
    });

    it('should work when NEXT_PUBLIC_DOMAIN is undefined', () => {
      delete process.env.NEXT_PUBLIC_DOMAIN;
      expect(absoluteUrl('/test')).toBe('undefined/test'); // JS default string coercion
    });
  });

  // --- slugify ---
  describe('slugify', () => {
    it('should convert basic strings', () => {
      expect(slugify('Hello World')).toBe('hello-world');
    });

    it('should handle leading/trailing spaces', () => {
      expect(slugify('  Trimmed String  ')).toBe('trimmed-string');
    });

    it('should replace multiple spaces/hyphens with a single hyphen', () => {
      expect(slugify('Multiple   Spaces --- Hyphens')).toBe(
        'multiple-spaces-hyphens'
      );
    });

    it('should remove non-alphanumeric characters (except hyphens)', () => {
      expect(slugify('Special!@#$%^&*()_+ Characters?')).toBe(
        'special_-characters'
      );
    });

    it('should handle Unicode characters by keeping them', () => {
      // Renamed test slightly
      expect(slugify('Crème brûlée')).toBe('creme-brulee');
      expect(slugify('你好 世界')).toBe('你好-世界');
    });

    it('should remove leading/trailing hyphens', () => {
      expect(slugify('--Leading Hyphens--')).toBe('leading-hyphens');
      expect(slugify('Trailing Hyphens--')).toBe('trailing-hyphens');
    });

    it('should return empty string for empty input', () => {
      expect(slugify('')).toBe('');
    });

    it('should throw error for non-string input', () => {
      expect(() => slugify(123 as any)).toThrow('Input must be a string');
      expect(() => slugify(null as any)).toThrow('Input must be a string');
      expect(() => slugify(undefined as any)).toThrow('Input must be a string');
    });
  });

  // --- unslugiify --- (Assuming corrected typo)
  describe('unslugiify', () => {
    it('should replace hyphens with spaces', () => {
      expect(unslugiify('hello-world')).toBe('hello world');
    });

    it('should handle multiple hyphens correctly', () => {
      // Note: The original implementation has redundant replaces, let's test the outcome
      expect(unslugiify('multiple--hyphens---here')).toBe(
        'multiple hyphens here'
      );
    });

    it('should trim whitespace resulting from leading/trailing hyphens', () => {
      expect(unslugiify('-leading-and-trailing-')).toBe('leading and trailing');
    });

    it('should return empty string for empty input', () => {
      expect(unslugiify('')).toBe('');
    });

    it('should handle strings without hyphens', () => {
      expect(unslugiify('nospaces')).toBe('nospaces');
    });
  });

  // --- capitalize ---
  describe('capitalize', () => {
    it('should capitalize the first letter of each word separated by space', () => {
      expect(capitalize('hello world')).toBe('Hello World');
    });

    it('should handle strings with hyphens', () => {
      expect(capitalize('kebab-case-string')).toBe('Kebab Case String');
    });

    it('should handle strings with underscores', () => {
      expect(capitalize('snake_case_string')).toBe('Snake Case String');
    });

    it('should handle mixed separators', () => {
      expect(capitalize('mixed-separators_and spaces')).toBe(
        'Mixed Separators And Spaces'
      );
    });

    it('should handle already capitalized or mixed case strings', () => {
      expect(capitalize('Already Capitalized')).toBe('Already Capitalized');
      expect(capitalize('mIxEd CaSe')).toBe('Mixed Case');
    });

    it('should handle single words', () => {
      expect(capitalize('word')).toBe('Word');
    });

    it('should return empty string for empty input', () => {
      expect(capitalize('')).toBe('');
    });
  });

  // --- safeStringify ---
  describe('safeStringify', () => {
    it('should stringify a simple object', () => {
      const obj = { a: 1, b: 'test' };
      expect(safeStringify(obj)).toBe('{"a":1,"b":"test"}');
    });

    it('should stringify an array', () => {
      const arr = [1, 'two', null];
      expect(safeStringify(arr)).toBe('[1,"two",null]');
    });

    it('should return an empty string for null input', () => {
      expect(safeStringify(null)).toBe('');
    });

    it('should return an empty string for undefined input', () => {
      expect(safeStringify(undefined)).toBe('');
    });

    // Note: JSON.stringify throws on circular references, safeStringify doesn't prevent this
    it('should behave like JSON.stringify for edge cases (e.g., functions, undefined properties)', () => {
      const obj = { a: 1, fn: () => {}, c: undefined };
      expect(safeStringify(obj)).toBe('{"a":1}'); // Functions and undefined props are omitted
    });
  });

  // --- safeParse ---
  describe('safeParse', () => {
    it('should parse a valid JSON string (object)', () => {
      const jsonString = '{"a": 1, "b": "test"}';
      expect(safeParse(jsonString)).toEqual({ a: 1, b: 'test' });
    });

    it('should parse a valid JSON string (array)', () => {
      const jsonString = '[1, "two", null]';
      expect(safeParse(jsonString)).toEqual([1, 'two', null]);
    });

    it('should return null for invalid JSON string', () => {
      const invalidJson = '{a: 1, "b": "test"}'; // Missing quotes around key 'a'
      expect(safeParse(invalidJson)).toBeNull();
    });

    it('should return null for non-JSON string', () => {
      expect(safeParse('just a regular string')).toBeNull();
    });

    it('should return null for empty string', () => {
      // JSON.parse('') throws an error
      expect(safeParse('')).toBeNull();
    });
  });

  // --- formatCurrency ---
  describe('formatCurrency', () => {
    it('should format amount with default USD currency', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('should format amount with specified EUR currency', () => {
      // Note: Output includes non-breaking space for EUR locale formatting
      expect(formatCurrency(987.1, 'EUR')).toMatch(/€\s*987\.10/); // Use regex for space flexibility
    });

    it('should format zero correctly', () => {
      expect(formatCurrency(0)).toBe('$0.00');
    });

    it('should format negative amounts correctly', () => {
      expect(formatCurrency(-50.99)).toBe('-$50.99'); // Check placement of minus sign
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1000000)).toBe('$1,000,000.00');
    });
  });

  // --- getInitials ---
  describe('getInitials', () => {
    it('should return initials for a two-word name', () => {
      expect(getInitials('John Doe')).toBe('JD');
    });

    it('should return initials for a single-word name', () => {
      expect(getInitials('Alice')).toBe('A');
    });

    it('should return the first two initials for multi-word names', () => {
      expect(getInitials('Peter Van Der Sar')).toBe('PV');
    });

    it('should handle extra spaces', () => {
      expect(getInitials('  Jane   Smith  ')).toBe('JS');
    });

    it('should handle lowercase names', () => {
      expect(getInitials('lower case')).toBe('LC');
    });

    it('should return empty string for empty input', () => {
      expect(getInitials('')).toBe('');
    });
  });

  // --- renderAvatar ---
  describe('renderAvatar', () => {
    const mockUserWithImage: Pick<User, 'id' | 'image'> = {
      id: 'user1',
      image: 'http://example.com/user1.jpg',
    };
    const mockUserWithoutImage: Pick<User, 'id' | 'image'> = {
      id: 'user2',
      image: null, // or undefined
    };
    const mockSessionUserWithImage: Pick<SessionUser, 'id' | 'image'> = {
      id: 'session-user-1',
      image: 'http://example.com/session-user-1.png',
    };
    const mockSessionUserWithoutImage: Pick<SessionUser, 'id' | 'image'> = {
      id: 'session-user-2',
      image: undefined,
    };

    it('should return user image if available', () => {
      expect(renderAvatar(mockUserWithImage)).toBe(mockUserWithImage.image);
      expect(renderAvatar(mockSessionUserWithImage)).toBe(
        mockSessionUserWithImage.image
      );
      expect(getDeterministicAvatar).not.toHaveBeenCalled();
    });

    it('should call getDeterministicAvatar if user image is missing', () => {
      const expectedAvatarUser = `deterministic-avatar-${mockUserWithoutImage.id}`;
      const expectedAvatarSession = `deterministic-avatar-${mockSessionUserWithoutImage.id}`;

      expect(renderAvatar(mockUserWithoutImage)).toBe(expectedAvatarUser);
      expect(getDeterministicAvatar).toHaveBeenCalledWith(
        mockUserWithoutImage.id
      );

      jest.clearAllMocks(); // Clear mock calls before next assertion

      expect(renderAvatar(mockSessionUserWithoutImage)).toBe(
        expectedAvatarSession
      );
      expect(getDeterministicAvatar).toHaveBeenCalledWith(
        mockSessionUserWithoutImage.id
      );
    });

    it('should handle potentially null/undefined user input gracefully (calls getDeterministicAvatar with undefined)', () => {
      // If the input `user` itself can be null/undefined
      const expectedAvatarUndefined = 'deterministic-avatar-undefined';
      expect(renderAvatar(null as any)).toBe(expectedAvatarUndefined);
      expect(getDeterministicAvatar).toHaveBeenCalledWith(undefined);

      jest.clearAllMocks();

      expect(renderAvatar(undefined as any)).toBe(expectedAvatarUndefined);
      expect(getDeterministicAvatar).toHaveBeenCalledWith(undefined);
    });
  });

  // --- renderUserName ---
  describe('renderUserName', () => {
    const mockUser1: Pick<User, 'username' | 'name'> = {
      username: 'johnd',
      name: 'John Doe',
    };
    const mockUser2: Pick<User, 'username' | 'name'> = {
      username: null,
      name: 'Jane Smith',
    };
    const mockUser3: Pick<User, 'username' | 'name'> = {
      username: null,
      name: null,
    };
    const mockSessionUser1: Pick<SessionUser, 'username' | 'name'> = {
      username: 'admin_user',
      name: 'Admin',
    };
    const mockSessionUser2: Pick<SessionUser, 'username' | 'name'> = {
      username: undefined,
      name: 'Session Person',
    };
    const mockSessionUser3: Pick<SessionUser, 'username' | 'name'> = {
      username: undefined,
      name: undefined,
    };

    it('should return username if available', () => {
      expect(renderUserName(mockUser1)).toBe('johnd');
      expect(renderUserName(mockSessionUser1)).toBe('admin_user');
    });

    it('should return name if username is missing', () => {
      expect(renderUserName(mockUser2)).toBe('Jane Smith');
      expect(renderUserName(mockSessionUser2)).toBe('Session Person');
    });

    it('should return "Unknown" if both username and name are missing', () => {
      expect(renderUserName(mockUser3)).toBe('Unknown');
      expect(renderUserName(mockSessionUser3)).toBe('Unknown');
    });

    it('should return "Unknown" for null or undefined input', () => {
      expect(renderUserName(null as any)).toBe('Unknown');
      expect(renderUserName(undefined as any)).toBe('Unknown');
    });
  });
});
