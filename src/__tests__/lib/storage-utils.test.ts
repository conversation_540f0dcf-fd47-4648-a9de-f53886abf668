import {
  getFileFromLocalStorage,
  getFromLocalStorage,
  removeFromLocalStorage,
  saveFileToLocalStorage,
  saveToLocalStorage,
} from '@/lib/storage-utils';

describe('storage-utils', () => {
  // Mock localStorage
  const localStorageMock = (() => {
    let store: { [key: string]: string } = {};
    return {
      getItem: jest.fn((key: string) => store[key] || null),
      setItem: jest.fn((key: string, value: string) => {
        store[key] = value;
      }),
      removeItem: jest.fn((key: string) => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        store = {};
      }),
    };
  })();

  // Mock FileReader
  const mockFileReader = {
    readAsDataURL: jest.fn(),
    onloadend: null as null | (() => void),
    onerror: null as null | (() => void),
    error: null as null | Error,
    result: null as null | string,
  };

  // Mock console.error to prevent output during tests
  const consoleErrorSpy = jest
    .spyOn(console, 'error')
    .mockImplementation(() => {});

  beforeEach(() => {
    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    // Setup FileReader mock
    jest
      .spyOn(window, 'FileReader')
      .mockImplementation(() => mockFileReader as any);
    localStorageMock.clear();
    jest.clearAllMocks();
  });

  afterEach(() => {
    consoleErrorSpy.mockClear();
    mockFileReader.error = null;
    mockFileReader.result = null;
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
  });

  describe('saveFileToLocalStorage', () => {
    it('should save file to localStorage with default key', async () => {
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const base64Content = 'data:text/plain;base64,dGVzdA==';

      const savePromise = saveFileToLocalStorage(undefined, file);

      // Simulate FileReader success
      mockFileReader.result = base64Content;
      mockFileReader.onloadend?.();

      await savePromise;

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'cover-image',
        base64Content
      );
    });

    it('should save file to localStorage with custom key', async () => {
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const base64Content = 'data:text/plain;base64,dGVzdA==';
      const customKey = 'custom-key';

      const savePromise = saveFileToLocalStorage(customKey, file);

      // Simulate FileReader completion
      mockFileReader.result = base64Content;
      mockFileReader.onloadend?.();

      await savePromise;

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        customKey,
        base64Content
      );
    });

    it('should handle FileReader errors', async () => {
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const savePromise = saveFileToLocalStorage(undefined, file);

      // Simulate FileReader error
      mockFileReader.error = new Error('Read error');
      mockFileReader.result = null;
      mockFileReader.onloadend?.();

      await expect(savePromise).rejects.toThrow('Failed to read file');
    });

    it('should handle localStorage errors during file save', async () => {
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const base64Content = 'data:text/plain;base64,dGVzdA==';

      // Mock localStorage error
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('Storage full');
      });

      const savePromise = saveFileToLocalStorage(undefined, file);

      // Simulate FileReader success
      mockFileReader.result = base64Content;
      mockFileReader.onloadend?.();

      await expect(savePromise).rejects.toThrow(
        'Failed to save to localStorage'
      );
      expect(consoleErrorSpy).toHaveBeenCalled();
    });
  });

  describe('getFileFromLocalStorage', () => {
    it('should return null if no file exists', () => {
      const result = getFileFromLocalStorage();
      expect(result).toBeNull();
      expect(localStorageMock.getItem).toHaveBeenCalledWith('cover-image');
    });

    it('should retrieve file from localStorage with default key', () => {
      const base64Content =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';
      localStorageMock.getItem.mockReturnValueOnce(base64Content);

      const result = getFileFromLocalStorage();

      expect(result).toBeInstanceOf(File);
      expect(result?.type).toBe('image/png');
      expect(result?.name).toBe('coverImage');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('cover-image');
    });

    it('should retrieve file from localStorage with custom key', () => {
      const base64Content =
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2Q==';
      const customKey = 'custom-key';
      localStorageMock.getItem.mockReturnValueOnce(base64Content);

      const result = getFileFromLocalStorage(customKey);

      expect(result).toBeInstanceOf(File);
      expect(result?.type).toBe('image/jpeg');
      expect(result?.name).toBe('coverImage');
      expect(localStorageMock.getItem).toHaveBeenCalledWith(customKey);
    });

    it('should handle invalid base64 data', () => {
      localStorageMock.getItem.mockReturnValueOnce(
        'data:image/png;base64,invalid-data'
      );

      const result = getFileFromLocalStorage();

      expect(result).toBeNull();
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });

    it('should handle malformed data URL', () => {
      localStorageMock.getItem.mockReturnValueOnce('data:');

      const result = getFileFromLocalStorage();

      expect(result).toBeNull();
      expect(consoleErrorSpy).not.toHaveBeenCalled();
    });
  });

  describe('basic localStorage operations', () => {
    it('should save and retrieve string value', () => {
      const key = 'test-key';
      const value = 'test-value';

      saveToLocalStorage(key, value);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(key, value);

      localStorageMock.getItem.mockReturnValueOnce(value);
      const retrieved = getFromLocalStorage(key);

      expect(retrieved).toBe(value);
      expect(localStorageMock.getItem).toHaveBeenCalledWith(key);
    });

    it('should return null for non-existent key', () => {
      localStorageMock.getItem.mockReturnValueOnce(null);

      const retrieved = getFromLocalStorage('non-existent');

      expect(retrieved).toBeNull();
      expect(localStorageMock.getItem).toHaveBeenCalledWith('non-existent');
    });

    it('should remove value from localStorage', () => {
      const key = 'test-key';

      removeFromLocalStorage(key);

      expect(localStorageMock.removeItem).toHaveBeenCalledWith(key);
    });

    it('should handle localStorage errors gracefully', () => {
      const key = 'test-key';
      const value = 'test-value';

      // Simulate localStorage error
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('Storage full');
      });

      saveToLocalStorage(key, value);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Failed to save to localStorage:',
        expect.any(Error)
      );
    });
  });
});
