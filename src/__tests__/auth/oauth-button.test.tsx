import React from 'react';
import userEvent from '@testing-library/user-event';

import { render, screen } from '@/lib/test-utils';
import OauthButton from '@/components/auth/oauth-button';

// Mock the EnhancedButton component
// jest.mock('@/components/shared/enhanced-button', () => ({
//   EnhancedButton: ({
//     children,
//     onClick,
//     isLoading,
//     'aria-label': ariaLabel,
//     variant,
//   }) => (
//     <button
//       onClick={onClick}
//       disabled={isLoading}
//       aria-label={ariaLabel}
//       data-variant={variant}
//       data-testid="enhanced-button"
//     >
//       {children}
//     </button>
//   ),
// }));

describe('OauthButton Component', () => {
  it('renders GitHub provider button correctly', () => {
    const handleClick = jest.fn();
    render(
      <OauthButton provider="github" onClick={handleClick}>
        Sign in with GitHub
      </OauthButton>
    );

    const button = screen.getByRole('button', { name: /sign in with github/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('aria-label', 'Sign in with github');
    expect(button).toHaveTextContent('Sign in with GitHub');
  });

  it('renders Google provider button correctly', () => {
    const handleClick = jest.fn();
    render(
      <OauthButton provider="google" onClick={handleClick}>
        Sign in with Google
      </OauthButton>
    );

    const button = screen.getByRole('button', { name: /sign in with google/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('aria-label', 'Sign in with google');
    expect(button).toHaveTextContent('Sign in with Google');
  });

  it('calls onClick handler when clicked', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();

    render(
      <OauthButton provider="github" onClick={handleClick}>
        Sign in with GitHub
      </OauthButton>
    );

    const button = screen.getByRole('button', { name: /sign in with github/i });
    await user.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state during transition', async () => {
    // Mock useTransition to simulate loading state
    jest.spyOn(React, 'useTransition').mockImplementation(() => {
      return [true, jest.fn()];
    });

    const handleClick = jest.fn();

    render(
      <OauthButton provider="github" onClick={handleClick}>
        Sign in with GitHub
      </OauthButton>
    );

    const button = screen.getByRole('button', { name: /sign in with github/i });
    expect(button).toBeDisabled();

    // Restore the original implementation
    jest.spyOn(React, 'useTransition').mockRestore();
  });
});
