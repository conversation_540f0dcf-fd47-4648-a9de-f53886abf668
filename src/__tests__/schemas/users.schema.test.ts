import { UpdateProfileSchema } from '@/schemas/users.schema';

describe('Users Schemas', () => {
  describe('UpdateProfileSchema', () => {
    it('should validate valid profile data', () => {
      const validProfile = {
        name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        bio: 'This is my bio',
        image: 'https://example.com/image.jpg',
        urls: [{ value: 'https://example.com' }],
      };
      
      const result = UpdateProfileSchema.safeParse(validProfile);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validProfile);
      }
    });

    it('should validate profile with minimal required fields', () => {
      const minimalProfile = {
        name: '<PERSON>',
        username: 'johndo<PERSON>',
      };
      
      const result = UpdateProfileSchema.safeParse(minimalProfile);
      expect(result.success).toBe(true);
    });

    it('should validate profile with optional fields set to null or undefined', () => {
      const profileWithNulls = {
        name: '<PERSON>',
        username: 'johndo<PERSON>',
        bio: undefined,
        image: null,
        urls: undefined,
      };
      
      const result = UpdateProfileSchema.safeParse(profileWithNulls);
      expect(result.success).toBe(true);
    });

    it('should reject when name is too short', () => {
      const invalidProfile = {
        name: 'J',
        username: 'johndoe',
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Name must be at least 2 characters.');
      }
    });

    it('should reject when name is too long', () => {
      const invalidProfile = {
        name: 'J'.repeat(31),
        username: 'johndoe',
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Name must not be longer than 30 characters.');
      }
    });

    it('should reject when username is too short', () => {
      const invalidProfile = {
        name: 'John Doe',
        username: 'j',
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Username must be at least 2 characters.');
      }
    });

    it('should reject when username is too long', () => {
      const invalidProfile = {
        name: 'John Doe',
        username: 'j'.repeat(31),
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Username must not be longer than 30 characters.');
      }
    });

    it('should reject when bio is too long', () => {
      const invalidProfile = {
        name: 'John Doe',
        username: 'johndoe',
        bio: 'a'.repeat(161),
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
    });

    it('should reject when urls is not an array of objects with value property', () => {
      const invalidProfile = {
        name: 'John Doe',
        username: 'johndoe',
        urls: [{ link: 'https://example.com' }],
      };
      
      const result = UpdateProfileSchema.safeParse(invalidProfile);
      expect(result.success).toBe(false);
    });

    it('should reject when required fields are missing', () => {
      const missingName = {
        username: 'johndoe',
      };
      
      const missingUsername = {
        name: 'John Doe',
      };
      
      const nameResult = UpdateProfileSchema.safeParse(missingName);
      expect(nameResult.success).toBe(false);
      
      const usernameResult = UpdateProfileSchema.safeParse(missingUsername);
      expect(usernameResult.success).toBe(false);
    });
  });
});