import { SigninSchema } from '@/schemas/auth.schemas';

describe('Auth Schemas', () => {
  describe('SigninSchema', () => {
    it('should validate valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      
      validEmails.forEach(email => {
        const result = SigninSchema.safeParse({ email });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.email).toBe(email);
        }
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'not-an-email',
        'missing@domain',
        '@nodomain.com',
        'user@.com',
        'user@domain.',
        'user <EMAIL>',
        '',
        ' ',
      ];
      
      invalidEmails.forEach(email => {
        const result = SigninSchema.safeParse({ email });
        expect(result.success).toBe(false);
      });
    });

    it('should reject when email is missing', () => {
      const result = SigninSchema.safeParse({});
      expect(result.success).toBe(false);
    });

    it('should reject when email is null or undefined', () => {
      const nullResult = SigninSchema.safeParse({ email: null });
      expect(nullResult.success).toBe(false);
      
      const undefinedResult = SigninSchema.safeParse({ email: undefined });
      expect(undefinedResult.success).toBe(false);
    });

    it('should reject when email is not a string', () => {
      const numberResult = SigninSchema.safeParse({ email: 123 });
      expect(numberResult.success).toBe(false);
      
      const objectResult = SigninSchema.safeParse({ email: {} });
      expect(objectResult.success).toBe(false);
      
      const arrayResult = SigninSchema.safeParse({ email: [] });
      expect(arrayResult.success).toBe(false);
    });
  });
});