import { createApi<PERSON><PERSON> } from '@/actions/api-keys';

import { db } from '@/lib/db';
import { Errors } from '@/lib/error-utils';
import { permissionService } from '@/lib/permissions';
import { getCurrentUser } from '@/lib/session';

// Mock dependencies
jest.mock('nanoid', () => ({
  nanoid: () => 'test-nanoid-string',
}));

jest.mock('@/lib/session', () => ({
  getCurrentUser: jest.fn(),
}));

jest.mock('@/lib/db', () => {
  const mockDb = {
    apiKey: {
      create: jest.fn(),
    },
  };
  return { db: mockDb };
});

jest.mock('@/lib/permissions', () => ({
  permissionService: {
    can: jest.fn(),
  },
}));

describe('API Key Actions', () => {
  const mockUser = {
    id: 'user-123',
    currentTenantId: 'tenant-123',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser);
    (permissionService.can as jest.Mock).mockResolvedValue(true);
  });

  describe('createApiKey', () => {
    const mockApiKey = {
      id: 'key-123',
      name: 'Test API Key',
      key: 'sk_test-nanoid-string',
      createdAt: new Date(),
      userId: 'user-123',
      tenantId: 'tenant-123',
    };

    beforeEach(() => {
      (db.apiKey.create as jest.Mock).mockResolvedValue(mockApiKey);
    });

    it('should create an API key successfully', async () => {
      const name = 'Test API Key';
      const result = await createApiKey(name);

      expect(result).toEqual({
        ...mockApiKey,
        maskedKey: `sk_test-...ring`, // Updated to match actual implementation
      });

      expect(db.apiKey.create).toHaveBeenCalledWith({
        data: {
          name,
          key: 'sk_test-nanoid-string',
          user: {
            connect: { id: mockUser.id },
          },
          tenant: {
            connect: { id: mockUser.currentTenantId },
          },
        },
      });
    });

    it('should throw Unauthorized error if user is not authenticated', async () => {
      (getCurrentUser as jest.Mock).mockResolvedValue(null);

      await expect(createApiKey('Test Key')).rejects.toThrow(
        Errors.Unauthorized()
      );
    });

    it('should throw Unauthorized error if tenant is not set', async () => {
      (getCurrentUser as jest.Mock).mockResolvedValue({
        id: 'user-123',
        currentTenantId: null,
      });

      await expect(createApiKey('Test Key')).rejects.toThrow(
        Errors.Unauthorized()
      );
    });

    it('should throw Forbidden error if user lacks permission', async () => {
      (permissionService.can as jest.Mock).mockResolvedValue(false);

      await expect(createApiKey('Test Key')).rejects.toThrow(
        Errors.Forbidden('You do not have permission to create API keys')
      );
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database error');
      (db.apiKey.create as jest.Mock).mockRejectedValue(dbError);

      await expect(createApiKey('Test Key')).rejects.toThrow();
    });

    it('should check permissions with correct parameters', async () => {
      const name = 'Test Key';
      await createApiKey(name);

      expect(permissionService.can).toHaveBeenCalledWith({
        user: mockUser,
        resource: 'apiKey',
        action: 'create',
      });
    });
  });
});
