import { login } from '@/actions/auth/login.action';
import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';

// Mock dependencies
jest.mock('@/auth', () => ({
  signIn: jest.fn(),
}));

jest.mock('@/lib/error-utils', () => ({
  handleServerError: jest.fn((error) => {
    throw error; // Just rethrow for testing
  }),
}));

describe('login Action', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call signIn with email provider and correct parameters', async () => {
    const mockValues = { email: '<EMAIL>' };
    const mockRedirectUrl = '/dashboard';

    (signIn as jest.Mock).mockResolvedValueOnce({ success: true });

    await login(mockValues, mockRedirectUrl);

    expect(signIn).toHaveBeenCalledWith('email', {
      email: mockValues.email,
      redirectTo: mockRedirectUrl,
    });
  });

  it('should use default redirect URL when not provided', async () => {
    const mockValues = { email: '<EMAIL>' };

    (signIn as jest.Mock).mockResolvedValueOnce({ success: true });

    await login(mockValues);

    expect(signIn).toHaveBeenCalledWith('email', {
      email: mockValues.email,
      redirectTo: REDIRECT_URL,
    });
  });

  it('should throw validation error for invalid email', async () => {
    const mockValues = { email: 'invalid-email' };

    await expect(login(mockValues)).rejects.toThrow();
    expect(signIn).not.toHaveBeenCalled();
  });

  it('should handle signIn errors', async () => {
    const mockValues = { email: '<EMAIL>' };
    const mockError = new Error('Sign in failed');

    (signIn as jest.Mock).mockRejectedValueOnce(mockError);

    await expect(login(mockValues)).rejects.toThrow();
  });

  it('should return signIn result on success', async () => {
    const mockValues = { email: '<EMAIL>' };
    const mockResult = { success: true };

    (signIn as jest.Mock).mockResolvedValueOnce(mockResult);

    const result = await login(mockValues);

    expect(result).toEqual(mockResult);
  });
});
