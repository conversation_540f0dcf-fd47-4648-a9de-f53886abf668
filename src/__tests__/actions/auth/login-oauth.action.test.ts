import { loginOAuth } from '@/actions/auth/login-oauth.action';
import { signIn } from '@/auth';
import { REDIRECT_URL } from '@/constants';

import { handleServerError } from '@/lib/error-utils';

// Mock dependencies
jest.mock('@/auth', () => ({
  signIn: jest.fn(),
}));

jest.mock('@/lib/error-utils', () => ({
  handleServerError: jest.fn((error) => {
    throw error; // Just rethrow for testing
  }),
}));

describe('loginOAuth Action', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call signIn with Google provider and correct parameters', async () => {
    const mockRedirectUrl = '/dashboard';

    (signIn as jest.Mock).mockResolvedValueOnce({ success: true });

    await loginOAuth('google', mockRedirectUrl);

    expect(signIn).toHaveBeenCalledWith('google', {
      redirectTo: mockRedirectUrl,
    });
  });

  it('should call signIn with GitHub provider and correct parameters', async () => {
    const mockRedirectUrl = '/dashboard';

    (signIn as jest.Mock).mockResolvedValueOnce({ success: true });

    await loginOAuth('github', mockRedirectUrl);

    expect(signIn).toHaveBeenCalledWith('github', {
      redirectTo: mockRedirectUrl,
    });
  });

  it('should use default redirect URL when not provided', async () => {
    (signIn as jest.Mock).mockResolvedValueOnce({ success: true });

    await loginOAuth('google');

    expect(signIn).toHaveBeenCalledWith('google', {
      redirectTo: REDIRECT_URL,
    });
  });

  it('should handle signIn errors', async () => {
    const mockError = new Error('Sign in failed');

    (signIn as jest.Mock).mockRejectedValueOnce(mockError);

    await expect(loginOAuth('google')).rejects.toThrow();
    expect(handleServerError).toHaveBeenCalledWith(mockError, {
      provider: 'google',
    });
  });

  it('should return signIn result on success', async () => {
    const mockResult = { success: true };

    (signIn as jest.Mock).mockResolvedValueOnce(mockResult);

    const result = await loginOAuth('github');

    expect(result).toEqual(mockResult);
  });
});
