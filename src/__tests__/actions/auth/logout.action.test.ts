import { signOut } from '@/auth';
import { logOut } from '@/actions/auth/logout.action';

// Mock dependencies
jest.mock('@/auth', () => ({
  signOut: jest.fn(),
}));

describe('logOut Action', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call signOut with correct redirect URL', async () => {
    await logOut();
    
    expect(signOut).toHaveBeenCalledWith({ redirectTo: '/signin' });
  });

  it('should handle signOut errors', async () => {
    const mockError = new Error('Sign out failed');
    
    (signOut as jest.Mock).mockRejectedValueOnce(mockError);
    
    await expect(logOut()).rejects.toThrow('Sign out failed');
  });

  it('should complete successfully when signOut succeeds', async () => {
    (signOut as jest.Mock).mockResolvedValueOnce(undefined);
    
    await expect(logOut()).resolves.not.toThrow();
  });
});