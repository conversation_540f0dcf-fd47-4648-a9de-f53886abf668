import { renderHook } from '@testing-library/react';

import { useScrollLock } from '@/hooks/use-scroll-lock';

describe('useScrollLock Hook', () => {
  // Store original document.body.style.overflow
  const originalOverflow = document.body.style.overflow;

  // Mock getComputedStyle
  const originalGetComputedStyle = window.getComputedStyle;

  beforeEach(() => {
    // Reset document.body.style.overflow before each test
    document.body.style.overflow = originalOverflow;

    // Mock getComputedStyle to return a consistent value
    window.getComputedStyle = jest.fn().mockImplementation(() => ({
      overflow: 'visible',
    }));
  });

  afterEach(() => {
    // Restore original getComputedStyle
    window.getComputedStyle = originalGetComputedStyle;
  });

  it('should lock scroll when lock is true', () => {
    renderHook(() => useScrollLock(true));

    expect(document.body.style?.overflow).toBe('hidden');
  });

  it('should not lock scroll when lock is false', () => {
    renderHook(() => useScrollLock(false));

    expect(document.body.style?.overflow).toBe('visible');
  });

  it('should restore original overflow style on unmount when locked', () => {
    const { unmount } = renderHook(() => useScrollLock(true));

    // Verify scroll is locked
    expect(document.body.style?.overflow).toBe('hidden');

    // Unmount the hook
    unmount();

    // Verify original style is restored
    expect(document.body.style?.overflow).toBe('visible');
  });

  it('should update overflow style when lock prop changes', () => {
    const { rerender } = renderHook(({ lock }) => useScrollLock(lock), {
      initialProps: { lock: false },
    });

    // Initially not locked
    expect(document.body.style?.overflow).toBe('visible');

    // Update to locked
    rerender({ lock: true });
    expect(document.body.style?.overflow).toBe('hidden');

    // Update back to unlocked
    rerender({ lock: false });
    expect(document.body.style?.overflow).toBe('visible');
  });
});
