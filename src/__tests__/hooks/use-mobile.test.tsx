import { renderHook } from '@testing-library/react';

import { useIsMobile } from '@/hooks/use-mobile';

describe('useIsMobile Hook', () => {
  // Store original window properties
  const originalInnerWidth = window.innerWidth;
  const originalMatchMedia = window.matchMedia;

  // Mock for matchMedia
  const mockMatchMedia = (matches: boolean) => {
    window.matchMedia = jest.fn().mockImplementation((query) => {
      return {
        matches,
        media: query,
        onchange: null,
        addListener: jest.fn(), // Deprecated
        removeListener: jest.fn(), // Deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    });
  };

  // Reset window properties after each test
  afterEach(() => {
    window.innerWidth = originalInnerWidth;
    window.matchMedia = originalMatchMedia;
  });

  it('should return true when screen width is less than mobile breakpoint', () => {
    // Set up window properties for mobile view
    window.innerWidth = 767; // Mobile breakpoint is 768
    mockMatchMedia(true);

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBe(true);
  });

  it('should return false when screen width is greater than or equal to mobile breakpoint', () => {
    // Set up window properties for desktop view
    window.innerWidth = 1024;
    mockMatchMedia(false);

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBe(false);
  });

  // it('should update when window size changes', () => {
  // // Start with desktop view
  // window.innerWidth = 1024;
  // mockMatchMedia(false);
  // const { result } = renderHook(() => useIsMobile());
  // expect(result.current).toBe(false);
  // // Simulate resize to mobile view
  // act(() => {
  //   window.innerWidth = 767;
  //   // Trigger the media query listener
  //   const mediaQueryListEvent = new Event('change');
  //   window.dispatchEvent(mediaQueryListEvent);
  // });
  // // The hook should now return true for mobile
  // expect(result.current).toBe(true);
  // });

  it('should clean up event listeners on unmount', () => {
    // Set up window properties
    window.innerWidth = 1024;
    const removeEventListenerMock = jest.fn();
    window.matchMedia = jest.fn().mockImplementation((query) => {
      return {
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: removeEventListenerMock,
        dispatchEvent: jest.fn(),
      };
    });

    const { unmount } = renderHook(() => useIsMobile());

    // Unmount the hook
    unmount();

    // Check if removeEventListener was called
    expect(removeEventListenerMock).toHaveBeenCalled();
  });
});
