import React from 'react';
import { render, screen } from '@testing-library/react';

import Page from '@/components/layout/page';

describe('Page Component', () => {
  it('renders children correctly', () => {
    render(
      <Page>
        <div data-testid="test-child">Test Content</div>
      </Page>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent('Test Content');
  });

  it('applies default classes', () => {
    render(<Page>Test Content</Page>);

    const pageElement = screen.getByTestId('page');
    expect(pageElement).toHaveClass('flex');
    expect(pageElement).toHaveClass('min-h-screen');
    expect(pageElement).toHaveClass('flex-col');
    expect(pageElement).toHaveClass('space-y-6');
    expect(pageElement).toHaveClass('flex-1');
    expect(pageElement).toHaveClass('max-w-full');
  });

  it('applies additional classes from className prop', () => {
    render(<Page className="test-class">Test Content</Page>);

    const pageElement = screen.getByTestId('page');
    expect(pageElement).toHaveClass('test-class');
    expect(pageElement).toHaveClass('flex'); // Still has default classes
    expect(pageElement).toHaveClass('min-h-screen');
  });

  it('renders multiple children correctly', () => {
    render(
      <Page>
        <div data-testid="child-1">First Child</div>
        <div data-testid="child-2">Second Child</div>
        <div data-testid="child-3">Third Child</div>
      </Page>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();

    const pageElement = screen.getByTestId('page');
    expect(pageElement).toContainElement(screen.getByTestId('child-1'));
    expect(pageElement).toContainElement(screen.getByTestId('child-2'));
    expect(pageElement).toContainElement(screen.getByTestId('child-3'));
  });
});
