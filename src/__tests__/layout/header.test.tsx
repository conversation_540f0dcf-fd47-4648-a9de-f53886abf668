import React from 'react';

import { render, screen } from '@/lib/test-utils';
import Header from '@/components/layout/header';

describe('Header Component', () => {
  it('renders children correctly', () => {
    render(
      <Header>
        <div data-testid="test-child">Test Content</div>
      </Header>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent('Test Content');
  });

  it('applies default classes', () => {
    render(<Header>Test Content</Header>);

    const headerElement = screen.getByTestId('header');
    // Check for essential classes only
    expect(headerElement).toHaveClass('sticky');
    expect(headerElement).toHaveClass('w-full');
    // Check that the className contains the expected string
    // expect(headerElement?.className).toContain('backdrop-blur');
  });

  it('applies additional classes from className prop', () => {
    render(<Header className="test-class">Test Content</Header>);

    const headerElement = screen.getByTestId('header');
    expect(headerElement).toHaveClass('test-class');
    expect(headerElement).toHaveClass('sticky'); // Still has default classes
  });
});
