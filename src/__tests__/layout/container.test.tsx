import React from 'react';

import { render, screen } from '@/lib/test-utils';
import Container from '@/components/layout/container';

describe('Container Component', () => {
  it('renders children correctly', () => {
    render(
      <Container>
        <div data-testid="test-child">Test Content</div>
      </Container>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent('Test Content');
  });
});
