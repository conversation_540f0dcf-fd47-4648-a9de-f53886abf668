import React from 'react';
import { render, screen } from '@testing-library/react';

import Prose from '@/components/layout/prose';

describe('Prose Component', () => {
  it('renders children correctly', () => {
    render(
      <Prose>
        <div data-testid="test-child">Test Content</div>
      </Prose>
    );

    const childElement = screen.getByTestId('test-child');
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent('Test Content');
  });

  it('applies default classes', () => {
    render(<Prose>Test Content</Prose>);

    const proseElement = screen.getByTestId('prose');
    expect(proseElement).toHaveClass('prose');
    expect(proseElement).toHaveClass('prose-md');
    expect(proseElement).toHaveClass('dark:prose-invert');
    expect(proseElement).toHaveClass('prose-headings:font-title');
    expect(proseElement).toHaveClass('font-default');
    expect(proseElement).toHaveClass('focus:outline-none');
  });

  it('applies additional classes from className prop', () => {
    render(<Prose className="test-class">Test Content</Prose>);

    const proseElement = screen.getByTestId('prose');
    expect(proseElement).toHaveClass('test-class');
    expect(proseElement).toHaveClass('prose'); // Still has default classes
  });

  it('renders markdown content correctly', () => {
    render(
      <Prose>
        <h1 data-testid="heading">Heading</h1>
        <p data-testid="paragraph">Paragraph text</p>
        <ul>
          <li data-testid="list-item-1">Item 1</li>
          <li data-testid="list-item-2">Item 2</li>
        </ul>
      </Prose>
    );

    expect(screen.getByTestId('heading')).toBeInTheDocument();
    expect(screen.getByTestId('paragraph')).toBeInTheDocument();
    expect(screen.getByTestId('list-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('list-item-2')).toBeInTheDocument();

    const proseElement = screen.getByTestId('prose');
    expect(proseElement).toContainElement(screen.getByTestId('heading'));
    expect(proseElement).toContainElement(screen.getByTestId('paragraph'));
    expect(proseElement).toContainElement(screen.getByTestId('list-item-1'));
    expect(proseElement).toContainElement(screen.getByTestId('list-item-2'));
  });
});
