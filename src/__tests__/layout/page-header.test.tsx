import React from 'react';
import { render, screen } from '@testing-library/react';
import { PageHeader } from '@/components/layout/page-header';

describe('PageHeader Component', () => {
  it('renders title and subtitle correctly', () => {
    render(
      <PageHeader 
        title="Test Title" 
        subtitle="Test Subtitle" 
      />
    );
    
    // Check if title is rendered correctly
    const titleElement = screen.getByRole('heading', { name: /test title/i });
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveClass('text-3xl');
    expect(titleElement).toHaveClass('font-bold');
    expect(titleElement).toHaveClass('mb-2');
    
    // Check if subtitle is rendered correctly
    const subtitleElement = screen.getByText(/test subtitle/i);
    expect(subtitleElement).toBeInTheDocument();
    expect(subtitleElement).toHaveClass('text-gray-500');
    expect(subtitleElement).toHaveClass('text-lg');
    expect(subtitleElement).toHaveClass('mb-12');
  });

  it('renders with long text correctly', () => {
    const longTitle = 'This is a very long title that might wrap to multiple lines in the UI';
    const longSubtitle = 'This is a very long subtitle that contains a lot of text and might wrap to multiple lines in the user interface';
    
    render(
      <PageHeader 
        title={longTitle} 
        subtitle={longSubtitle} 
      />
    );
    
    // Check if long title is rendered correctly
    const titleElement = screen.getByRole('heading', { name: new RegExp(longTitle, 'i') });
    expect(titleElement).toBeInTheDocument();
    
    // Check if long subtitle is rendered correctly
    const subtitleElement = screen.getByText(new RegExp(longSubtitle, 'i'));
    expect(subtitleElement).toBeInTheDocument();
  });

  it('renders within a container correctly', () => {
    render(
      <div data-testid="container">
        <PageHeader 
          title="Test Title" 
          subtitle="Test Subtitle" 
        />
      </div>
    );
    
    const container = screen.getByTestId('container');
    const pageHeaderDiv = container.firstChild;
    
    expect(pageHeaderDiv).toHaveClass('pt-4');
    expect(container).toContainElement(screen.getByRole('heading', { name: /test title/i }));
    expect(container).toContainElement(screen.getByText(/test subtitle/i));
  });
});