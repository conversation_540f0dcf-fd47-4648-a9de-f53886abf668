import React from 'react';

import { useAuthPrompt } from '@/hooks/use-auth-prompt';
import { Textarea } from '@/components/ui/textarea';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type PostCommentTextAreaProps = {
  value: string;
  onSubmit: (e: React.FormEvent) => void;
  onChange: (value: string) => void;
};

export default function PostCommentTextArea({
  value,
  onSubmit,
  onChange,
}: PostCommentTextAreaProps) {
  const { checkAuth, AuthPrompt } = useAuthPrompt();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    checkAuth(() => {
      onSubmit(e);
    });
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="mb-8 space-y-4">
        <Textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Write a comment..."
          rows={3}
        />

        <EnhancedButton type="submit" className="mt-2">
          Post Comment
        </EnhancedButton>
      </form>
      <AuthPrompt />
    </>
    // maybe use React Hook Form
  );
}
