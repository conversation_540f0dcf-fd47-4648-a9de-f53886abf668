'use client';

import Link from 'next/link';
import { BookDashed, Edit, MoreHorizontal, Trash } from 'lucide-react';

import { Post } from '@/types/post.types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface PostActionsProps {
  post: Post;
  onUnpublish: (post: Post) => void;
  onDelete: (post: Post) => void;
}

export function PostActions({ post, onUnpublish, onDelete }: PostActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </EnhancedButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/posts/${post.slug}/edit`}>
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Link>
        </DropdownMenuItem>
        {post.published && (
          <DropdownMenuItem onClick={() => onUnpublish(post)}>
            <BookDashed className="mr-2 h-4 w-4" /> Unpublish
          </DropdownMenuItem>
        )}
        <DropdownMenuItem onClick={() => onDelete(post)}>
          <Trash className="mr-2 h-4 w-4" /> Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
