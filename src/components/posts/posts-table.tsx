'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { deletePost, getPostsByAuthor, updatePost } from '@/actions/posts';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';

import { Post } from '@/types/post.types';
import { GetPaginatedResult } from '@/types/shared.types';
import { calculateNewPage } from '@/lib/pagination';
import { toastPromise } from '@/lib/toast-promise';
import { formatDate } from '@/lib/utils';
import { useTable } from '@/hooks/use-table';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';

import { ConfirmDialog } from '../shared/confirm-dialog';
import { PostActions } from './post-actions';

type PostsTableProps = {
  initialData: GetPaginatedResult<Post>;
};

export function PostsTable({ initialData }: PostsTableProps) {
  const isInitialMount = useRef(true);
  const queryClient = useQueryClient();
  const {
    data: posts,
    setData: setPosts,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sortBy,
    sorting,
    onSortChange,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<Post>({ initialData });
  const [isPending, startTransition] = useTransition();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState<Post | null>(null);
  const router = useRouter();
  const coverImage = '/blog-default-image.svg';

  const columns = useMemo<ColumnDef<Post>[]>(
    () => [
      {
        accessorKey: 'cover',
        header: 'Cover',
        enableSorting: false,
        cell: ({ row }) => (
          <div>
            <div className="relative w-12 h-12">
              <Image
                src={row.original.cover || coverImage}
                alt={`Cover for ${row.original.title}`}
                fill
                sizes="50px"
                className="rounded-sm object-cover"
              />
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'title',
        header: 'Title',
        cell: ({ row }) => (
          <Link href={`/blog/${row.original.slug}`}>{row.original.title}</Link>
        ),
      },
      {
        accessorKey: 'published',
        header: 'Status',
        cell: ({ row }) => (
          <Badge variant={row.original.published ? 'default' : 'secondary'}>
            {row.original.published ? 'Published' : 'Draft'}
          </Badge>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: 'Date',
        cell: ({ row }) => formatDate(row.original.createdAt),
      },
      {
        accessorKey: '_count.likes',
        header: 'Likes',
        cell: ({ row }) => row.original._count?.likes || 0,
      },
      {
        accessorKey: '_count.bookmarks',
        header: 'Bookmarks',
        cell: ({ row }) => row.original._count?.bookmarks || 0,
      },
      {
        accessorKey: '_count.comments',
        header: 'Comments',
        cell: ({ row }) => row.original._count?.comments || 0,
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <PostActions
            post={row.original}
            onUnpublish={handleUnpublish}
            onDelete={(post) => {
              setPostToDelete(post);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: posts,
    columns,
    state: {
      sorting,
    },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['posts-by-author', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getPostsByAuthor(defaultParams);
    },
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  const handleDelete = async () => {
    if (!postToDelete) return;

    startTransition(async () => {
      try {
        await deletePost(postToDelete.id);
        setSearchTerm('');
        queryClient.invalidateQueries({ queryKey: ['posts-by-author'] });
        const newTotalItems = totalItems - 1;
        const newPage = calculateNewPage(page, newTotalItems, itemsPerPage);
        setTotalItems(newTotalItems);
        setPage(newPage);
        await refetch();
        toast.success('Post deleted successfully');
      } catch (error) {
        console.error('Error deleting post:', error);
        toast.error('Failed to delete post');
      } finally {
        setIsDeleteConfirmOpen(false);
        setPostToDelete(null);
      }
    });
  };

  const handleUnpublish = async (post: Post) => {
    return toastPromise(
      updatePost(post.slug, { published: false }).then(() => {
        queryClient.invalidateQueries({ queryKey: ['posts-by-author'] });
        refetch();
      }),
      {
        loading: 'Unpublishing post...',
        success: 'Post unpublished successfully',
        error: 'Failed to unpublish post',
      }
    );
  };

  useEffect(() => {
    if (result) {
      setPosts(result.items);
      setTotalItems(result.totalCount);

      // Only update page on initial mount if it differs
      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);

  return (
    <>
      {isEmpty && table.getRowModel().rows?.length === 0 ? (
        <EmptyState
          title="No posts yet"
          description="You have not created any posts yet. Click the button above to add your first post."
          actionLabel="Create post"
          onAction={() => router.push('/posts/new')}
        />
      ) : (
        <>
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter posts..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <EnhancedButton asChild>
              <Link href="/posts/new">
                <Plus className="w-4 h-4 mr-2" />
                New Post
              </Link>
            </EnhancedButton>
          </div>

          <div className="relative">
            {isLoading && <BluredBgSpinner />}
            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() &&
                              header.column.getIsSorted() && (
                                <span className="ml-2">
                                  {{
                                    asc: '↑',
                                    desc: '↓',
                                  }[header.column.getIsSorted() as string] ??
                                    ''}
                                </span>
                              )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No posts found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      )}

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDelete}
        title="Confirm Delete Post"
        isLoading={isPending}
        loadingText="Deleting..."
        description="Are you sure you want to delete this post? This action cannot be undone."
      />
    </>
  );
}
