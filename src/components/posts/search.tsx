import {
  KeyboardEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { getPosts } from '@/actions/posts';
import { useQuery } from '@tanstack/react-query';
import { FileText, Loader2, Search } from 'lucide-react';
import { useDebounce } from 'use-debounce';

import { Post } from '@/types/post.types';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

export default function PostSearch() {
  const [search, setSearch] = useState('');
  const [debouncedSearch] = useDebounce(search, 300);
  const [isFocused, setIsFocused] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const resultRefs = useRef<(HTMLDivElement | null)[]>([]);
  const pathname = usePathname();

  const {
    data: postsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['posts', 'search', debouncedSearch],
    queryFn: async () => {
      if (!isFocused) return { items: [] };
      return getPosts({
        take: 50,
        filter: debouncedSearch ? { searchQuery: debouncedSearch } : undefined,
      });
    },
    enabled: isFocused,
    staleTime: 1000 * 60 * 5, // Cache results for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    retry: 1, // Only retry failed requests once
  });

  const posts = postsData?.items ?? [];

  const filteredPosts = useMemo(() => {
    if (!debouncedSearch) return posts;
    return posts.filter((post) => {
      const searchLower = debouncedSearch.toLowerCase();
      const titleMatch = post.title.toLowerCase().includes(searchLower);
      const excerptMatch = post.excerpt?.toLowerCase().includes(searchLower);
      const tagsMatch = post.tags?.some((tag) =>
        tag.name.toLowerCase().includes(searchLower)
      );
      return titleMatch || excerptMatch || tagsMatch;
    });
  }, [debouncedSearch, posts]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
      setActiveIndex(-1); // Reset active index when search changes
    },
    []
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Escape') {
        setSearch('');
        setActiveIndex(-1);
        inputRef.current?.blur();
      } else if (
        e.key === 'Enter' &&
        filteredPosts.length > 0 &&
        activeIndex >= 0
      ) {
        router.push(`${pathname}/${filteredPosts[activeIndex].slug}`);
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setActiveIndex((prevIndex) =>
          prevIndex < filteredPosts.length - 1 ? prevIndex + 1 : prevIndex
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setActiveIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0));
      }
    },
    [filteredPosts, router, activeIndex, pathname]
  );

  useEffect(() => {
    if (resultRefs.current[activeIndex]) {
      resultRefs.current[activeIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }, [activeIndex]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setTimeout(() => {
      if (!document.activeElement?.closest('.dropdown-container')) {
        setIsFocused(false);
        setActiveIndex(-1);
      }
    }, 100);
  }, []);

  const highlightMatch = useCallback((text: string, query: string) => {
    if (!query) return text;
    const regex = new RegExp(`(${query})`, 'gi');
    return text.split(regex).map((part, index) =>
      regex.test(part) ? (
        <strong key={index} className="font-bold">
          {part}
        </strong>
      ) : (
        part
      )
    );
  }, []);

  const ResultItem = ({ post, index }: { post: Post; index: number }) => {
    const highlightedTitle = useMemo(
      () => highlightMatch(post.title, debouncedSearch),
      [post.title]
    );
    const highlightedExcerpt = useMemo(
      () => highlightMatch(post.excerpt || '', debouncedSearch),
      [post.excerpt]
    );
    const highlightedTags = useMemo(
      () => post.tags.map((tag) => highlightMatch(tag.name, debouncedSearch)),
      [post.tags]
    );

    return (
      <div
        ref={(el) => {
          resultRefs.current[index] = el;
        }}
        className={`flex items-start px-4 py-3 hover:bg-accent cursor-pointer border-b border-input last:border-b-0 ${
          activeIndex === index ? 'bg-accent' : ''
        }`}
        onMouseDown={() => {
          router.push(`${pathname}/${post.slug}`);
        }}
      >
        <div className="flex-shrink-0 mr-4">
          <Image
            src={post.cover || '/default-image.png'}
            alt={post.title || 'Blog post cover'}
            width={60}
            height={60}
            className="rounded-md object-cover"
          />
        </div>
        <div className="flex-grow min-w-0">
          <h3 className="text-sm font-medium text-foreground line-clamp-1">
            {highlightedTitle}
          </h3>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {highlightedExcerpt}
          </p>
          <div className="mt-1 flex flex-wrap gap-2">
            {highlightedTags.map((tag, index) => (
              <span key={index} className="text-xs text-muted-foreground">
                #{tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full max-w-3xl mx-auto">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search blog posts..."
          value={search}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          ref={inputRef}
          className="pl-10 pr-4 py-2 w-full focus-visible:ring-0 focus-visible:ring-accent rounded-md"
          aria-label="Search blog posts"
        />
        <Search
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          size={20}
        />
      </div>
      {isFocused && (
        <div className="absolute z-10 w-full bg-background border border-t-0 border-input rounded-b-md shadow-md dropdown-container">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2
                className="animate-spin text-muted-foreground"
                size={20}
              />
            </div>
          ) : filteredPosts.length > 0 ? (
            filteredPosts.length <= 4 ? (
              filteredPosts.map((post, index) => (
                <ResultItem key={post.id} post={post} index={index} />
              ))
            ) : (
              <ScrollArea className="h-[400px]">
                {filteredPosts.map((post, index) => (
                  <ResultItem key={post.id} post={post} index={index} />
                ))}
              </ScrollArea>
            )
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4">
              <FileText className="h-8 w-8 text-red-500 mb-2" />
              <p className="text-sm text-red-500">
                Failed to load search results
              </p>
            </div>
          ) : (
            search && (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <FileText className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">No posts found</p>
              </div>
            )
          )}
        </div>
      )}
    </div>
  );
}
