import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Bookmark, Heart, MessageCircle } from 'lucide-react';

import { Post } from '@/types/post.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { getInitials } from '@/lib/utils';

import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Card, CardContent, CardFooter } from '../ui/card';

const coverImage = '/blog-default-image.svg';

type RelatedPostCardProps = {
  post: Post;
};

export default function RelatedPostCard({ post }: RelatedPostCardProps) {
  return (
    <Link href={`/blog/${post.slug}`}>
      <Card className="hover:shadow-md transition-all duration-300 hover:-translate-y-0.5">
        <CardContent className="p-4 pb-0">
          <div className="flex flex-col md:flex-row md:gap-6">
            <div className="flex-shrink-0 mb-4 md:mb-0">
              <div className="relative w-full md:w-[160px] h-[200px] md:h-[100px]">
                <Image
                  src={post.cover || coverImage}
                  alt={post.title}
                  fill
                  className="rounded-md object-cover"
                />
              </div>
            </div>
            <div className="flex-grow min-w-0">
              <h3 className="font-semibold text-base line-clamp-1 mb-2">
                {post.title}
              </h3>
              <p className="text-muted-foreground text-sm line-clamp-2">
                {post.excerpt || 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="p-4 pt-3 mt-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage
                src={
                  post.author.image || getDeterministicAvatar(post.author.id)
                }
              />
              <AvatarFallback>
                {getInitials(post.author.name || 'U')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">
              {post.author.name}
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-muted-foreground flex items-center">
              <Heart className="w-4 h-4 mr-1" />
              {post._count.likes}
            </span>
            <span className="text-sm text-muted-foreground flex items-center">
              <MessageCircle className="w-4 h-4 mr-1" />
              {post._count.comments}
            </span>
            <span className="text-sm text-muted-foreground flex items-center">
              <Bookmark className="w-4 h-4 mr-1" />
              {post._count.bookmarks}
            </span>
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}
