'use client';

import React, { forwardRef } from 'react';
import Image, { ImageProps } from 'next/image';

export const CoverImage = forwardRef(
  ({ src, alt }: ImageProps, ref: React.Ref<HTMLImageElement>) => {
    return (
      <Image
        ref={ref}
        src={src}
        alt={alt}
        width={1200}
        height={630}
        priority
        className="w-full aspect-video object-cover rounded-lg shadow-lg mb-4 border-2 border-transparent"
      />
    );
  }
);

CoverImage.displayName = 'CoverImage';
