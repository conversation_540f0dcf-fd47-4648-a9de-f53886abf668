'use client';

import React from 'react';
import { useSession } from 'next-auth/react';

import { Comment } from '@/types/post.types';

import CommentItem from './comment-item';

type PostCommentsProps = {
  comments: Comment[];
  onCommentLikeToggle: (commentId: string) => void;
  onReply: (commentId: string, content: string) => void;
  onCommentEdit: (commentId: string, newContent: string) => void;
  onDelete: (commentId: string) => void;
};

export default function PostComments({
  comments,
  onCommentLikeToggle,
  onReply,
  onCommentEdit,
  onDelete,
}: PostCommentsProps) {
  const session = useSession();

  const renderComment = (comment: Comment, depth = 0) => (
    <CommentItem
      key={comment.id}
      comment={comment}
      depth={depth}
      currentUser={session.data?.user}
      onLike={onCommentLikeToggle}
      onReply={onReply}
      onEdit={onCommentEdit}
      onDelete={onDelete}
    >
      {comment.replies && comment.replies.length > 0 && (
        <div className="space-y-4 border-l-2 border-gray-200 pl-4">
          {comment.replies.map((reply) => renderComment(reply, depth + 1))}
        </div>
      )}
    </CommentItem>
  );

  return (
    <section id="comments-section">
      {comments.map((comment) => renderComment(comment))}
    </section>
  );
}
