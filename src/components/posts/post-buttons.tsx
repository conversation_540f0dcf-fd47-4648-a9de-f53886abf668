'use client';

import React from 'react';
import { Bookmark, Heart, MessageCircle, Share2 } from 'lucide-react';
import { toast } from 'sonner';

import { Post } from '@/types/post.types';
import { useAuthPrompt } from '@/hooks/use-auth-prompt';
import { Separator } from '@/components/ui/separator';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type PostButtonsProps = {
  post: Post;
  onLikeClick: (postId: string) => void;
  onBookmarkClick: (postId: string) => void;
  likesCount: number;
  bookmarksCount: number;
  commentsCount: number;
  isLiked: boolean;
  isBookmarked: boolean;
};

export default function PostButtons({
  post,
  onLikeClick,
  onBookmarkClick,
  likesCount,
  bookmarksCount,
  commentsCount,
  isLiked,
  isBookmarked,
}: PostButtonsProps) {
  const { checkAuth, AuthPrompt } = useAuthPrompt();

  const handleLikeClick = () => {
    checkAuth(() => onLikeClick(post.id));
  };

  const handleBookmarkClick = () => {
    checkAuth(() => onBookmarkClick(post.id));
  };

  const handleCommentClick = () => {
    checkAuth(() => {
      document
        .querySelector('#comments-section')
        ?.scrollIntoView({ behavior: 'smooth' });
    });
  };

  const handleShare = async () => {
    const shareData = {
      title: post.title,
      text: post.excerpt || `Check out this post: ${post.title}`,
      url: window.location.href,
    };

    if (navigator.share && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData);
        toast.success('Shared successfully', {
          description: 'The post has been shared.',
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      try {
        await navigator.clipboard.writeText(window.location.href);
        toast.success('Link copied', {
          description: 'The post link has been copied to your clipboard.',
        });
      } catch (error) {
        console.error('Error copying to clipboard:', error);
        toast.error('Sharing failed', {
          description: 'Unable to share or copy the link.',
        });
      }
    }
  };

  return (
    <>
      <div className="mt-8 grid grid-cols-2 gap-2 sm:flex sm:flex-row sm:flex-wrap sm:items-center relative">
        <EnhancedButton
          variant="ghost"
          size="sm"
          className="flex items-center justify-center sm:flex-1"
          onClick={handleLikeClick}
        >
          <Heart
            className={`h-5 w-5 mr-1.5 ${isLiked ? 'fill-current text-primary' : ''}`}
          />
          {likesCount}
        </EnhancedButton>

        <EnhancedButton
          variant="ghost"
          size="sm"
          className="flex items-center justify-center sm:flex-1"
          onClick={handleCommentClick}
        >
          <MessageCircle className="h-5 w-5 mr-1.5" />
          {commentsCount}
        </EnhancedButton>

        <EnhancedButton
          variant="ghost"
          size="sm"
          className="flex items-center justify-center sm:flex-1"
          onClick={handleBookmarkClick}
        >
          <Bookmark
            className={`h-5 w-5 mr-1.5 ${isBookmarked ? 'fill-current text-primary' : ''}`}
          />
          {bookmarksCount}
        </EnhancedButton>

        <EnhancedButton
          variant="ghost"
          size="sm"
          className="flex items-center justify-center sm:flex-1"
          onClick={handleShare}
        >
          <Share2 className="h-5 w-5 mr-1.5" />
          Share
        </EnhancedButton>

        {/* Vertical divider */}
        <Separator
          orientation="vertical"
          className="absolute left-1/2 h-full -translate-x-1/2 sm:hidden"
        />

        {/* Horizontal divider */}
        <Separator
          orientation="horizontal"
          className="absolute top-1/2 w-full -translate-y-1/2 sm:hidden"
        />
      </div>

      <AuthPrompt />
    </>
  );
}
