'use client';

import React from 'react';

import { Post } from '@/types/post.types';

import RelatedPostCard from './related-post-card';

type RelatedPostsProps = {
  relatedPosts: Post[];
  loadingRelatedPosts: boolean;
};

export default function RelatedPosts({
  relatedPosts,
  loadingRelatedPosts,
}: RelatedPostsProps) {
  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold mb-4">Related Posts</h2>
      {loadingRelatedPosts ? (
        <p>Loading related posts...</p>
      ) : relatedPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {relatedPosts?.map((relatedPost) => (
            <RelatedPostCard key={relatedPost.id} post={relatedPost} />
          ))}
        </div>
      ) : (
        <p>No related posts found.</p>
      )}
    </div>
  );
}
