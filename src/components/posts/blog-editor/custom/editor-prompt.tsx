import { useState } from 'react';
import { DialogTitle } from '@radix-ui/react-dialog';
import { Editor } from '@tiptap/react';

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const EditorPrompt = ({
  editor,
  onClose,
  fileType,
}: {
  editor: Editor;
  onClose: () => void;
  fileType: 'image' | 'video' | 'youtube' | 'twitter';
}) => {
  const [url, setUrl] = useState('');

  const handleSubmit = () => {
    if (url) {
      editor.chain().focus().setImage({ src: url }).run();
      onClose();
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Embed {fileType}</DialogTitle>
        </DialogHeader>
        <Input
          placeholder={`Enter ${fileType} URL`}
          value={url}
          onChange={(e) => setUrl(e.target.value)}
        />
        <DialogFooter>
          <EnhancedButton onClick={handleSubmit}>Embed</EnhancedButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditorPrompt;
