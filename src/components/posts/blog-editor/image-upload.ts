import { uploadFile } from '@/actions/upload';
import { createImageUpload } from 'novel/plugins';
import { toast } from 'sonner';

// This is using server action
const onUpload = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const promise = uploadFile(formData);

  return new Promise((resolve) => {
    toast.promise(
      promise.then(async ({ url }) => {
        // Successfully uploaded image
        if (url) {
          // preload the image
          const image = new Image();
          image.src = url;
          image.onload = () => {
            resolve(url);
          };
        } else {
          throw new Error(`Error uploading image. Please try again.`);
        }
      }),
      {
        loading: 'Uploading image...',
        success: 'Image uploaded successfully.',
        error: (e) => e.message,
      }
    );
  });
};

export const uploadFn = createImageUpload({
  onUpload,
  validateFn: (file) => {
    if (!file.type.includes('image/')) {
      toast.error('File type not supported.');
      return false;
    } else if (file.size / 1024 / 1024 > 20) {
      toast.error('File size too big (max 20MB).');
      return false;
    }
    return true;
  },
});
