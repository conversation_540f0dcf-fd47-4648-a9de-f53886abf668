import React, { useEffect, useState } from 'react';
import { EditorContent, useEditor } from '@tiptap/react';
import { JSONContent } from 'novel';

import { defaultExtensions } from './extensions';

interface PostRendererProps {
  content: JSONContent | null | undefined;
  contentLoaded: boolean;
}

const PostRenderer: React.FC<PostRendererProps> = ({
  content,
  contentLoaded,
}) => {
  const [key, setKey] = useState('loading');

  useEffect(() => {
    if (contentLoaded) {
      setKey('loaded');
    }
  }, [contentLoaded]);

  const editor = useEditor(
    {
      extensions: defaultExtensions,
      immediatelyRender: false,
      content: contentLoaded && content ? content : undefined,
      editable: false,
      editorProps: {
        attributes: {
          class:
            'prose prose-sm sm:prose-base lg:prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full',
        },
      },
    },
    [key]
  );

  if (!editor) {
    return null;
  }

  return (
    <div className="relative w-full">
      <EditorContent
        key={key}
        editor={editor}
        className="relative h-full w-full border-muted bg-background"
      />
    </div>
  );
};

export default PostRenderer;
