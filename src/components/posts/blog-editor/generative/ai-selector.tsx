'use client';

import { useCallback, useState } from 'react';
import { useCompletion } from 'ai/react';
import { ArrowU<PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { useEditor } from 'novel';
import { addAIHighlight } from 'novel/extensions';
import Markdown from 'react-markdown';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Command, CommandInput } from '@/components/ui/command';
import { ScrollArea } from '@/components/ui/scroll-area';
import CrazySpinner from '@/components/shared/crazy-spinner';

import AICompletionCommands from './ai-completion-command';
import AISelectorCommands from './ai-selector-commands';

interface AISelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AISelector({ onOpenChange }: AISelectorProps) {
  const { editor } = useEditor();
  const [inputValue, setInputValue] = useState('');

  const { completion, complete, isLoading } = useCompletion({
    api: '/api/generate',
    onResponse: (response) => {
      if (response.status === 429) {
        toast.error('You have reached your request limit for the day.');
        return;
      }
    },
    onError: (e) => {
      console.error('Completion error:', e);
      toast.error(e.message);
    },
  });

  const hasCompletion = completion && completion.length > 0;

  const handleComplete = useCallback(
    async (text: string, option: string, command?: string) => {
      try {
        console.log('Sending request:', { text, option, command });
        const result = await complete(text, {
          body: { prompt: text, option, command },
        });
        console.log('Completion result:', result);
        setInputValue('');
      } catch (error) {
        console.error('Error in handleComplete:', error);
        toast.error('An error occurred while processing your request.');
      }
    },
    [complete]
  );

  const handleSubmit = useCallback(() => {
    if (hasCompletion) {
      handleComplete(completion, 'zap', inputValue);
    } else {
      const slice = editor?.state.selection.content();
      const text = editor?.storage.markdown.serializer.serialize(
        slice?.content
      );
      if (text) {
        handleComplete(text, 'zap', inputValue);
      } else {
        toast.error('No text selected or available for processing.');
      }
    }
  }, [hasCompletion, completion, inputValue, editor, handleComplete]);

  const handleSelect = useCallback(
    (value: string, option: string) => {
      handleComplete(value, option);
    },
    [handleComplete]
  );

  return (
    <Command className="w-[350px]">
      {hasCompletion && (
        <div className="flex max-h-[400px]">
          <ScrollArea>
            <div className="prose p-2 px-4 prose-sm">
              <Markdown>{completion}</Markdown>
            </div>
          </ScrollArea>
        </div>
      )}

      {isLoading && (
        <div className="flex h-12 w-full items-center px-4 text-sm font-medium text-muted-foreground text-purple-500">
          <Sparkles className="mr-2 h-4 w-4 shrink-0  " />
          AI is thinking
          <div className="ml-2 mt-1">
            <CrazySpinner />
          </div>
        </div>
      )}
      {!isLoading && (
        <>
          <div className="relative">
            <CommandInput
              value={inputValue}
              onValueChange={setInputValue}
              autoFocus
              placeholder={
                hasCompletion
                  ? 'Tell AI what to do next'
                  : 'Ask AI to edit or generate...'
              }
              onFocus={() => editor && addAIHighlight(editor)}
            />
            <Button
              size="icon"
              className="absolute right-2 top-1/2 h-6 w-6 -translate-y-1/2 rounded-full bg-purple-500 hover:bg-purple-900"
              onClick={handleSubmit}
              type="button"
            >
              <ArrowUp className="h-4 w-4" />
            </Button>
          </div>
          {hasCompletion ? (
            <AICompletionCommands
              onDiscard={() => {
                editor?.chain?.().unsetHighlight?.().focus?.().run?.();
                onOpenChange(false);
              }}
              completion={completion || ''}
            />
          ) : (
            <AISelectorCommands onSelect={handleSelect} />
          )}
        </>
      )}
    </Command>
  );
}
