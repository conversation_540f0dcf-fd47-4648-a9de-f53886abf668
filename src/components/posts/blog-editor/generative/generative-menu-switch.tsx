'use client';

import { Fragment, useEffect, type ReactNode } from 'react';
import { Spark<PERSON> } from 'lucide-react';
import { EditorBubble, useEditor } from 'novel';
import { removeAIHighlight } from 'novel/extensions';

import { EnhancedButton } from '@/components/shared/enhanced-button';

import { AISelector } from './ai-selector';

interface GenerativeMenuSwitchProps {
  children: ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
const GenerativeMenuSwitch = ({
  children,
  open,
  onOpenChange,
}: GenerativeMenuSwitchProps) => {
  const { editor } = useEditor();

  useEffect(() => {
    if (!open && editor) removeAIHighlight(editor);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);
  return (
    <EditorBubble
      tippyOptions={{
        placement: open ? 'bottom-start' : 'top',
        onHidden: () => {
          if (!editor) return;
          onOpenChange(false);
          editor.chain().unsetHighlight().run();
        },
      }}
      className="flex w-fit max-w-[90vw] overflow-hidden rounded-md border border-muted bg-background shadow-xl"
    >
      {open && <AISelector open={open} onOpenChange={onOpenChange} />}

      {!open && (
        <Fragment>
          <EnhancedButton
            className="gap-1 rounded-none text-purple-500"
            variant="ghost"
            type="button"
            onClick={() => onOpenChange(true)}
            size="sm"
          >
            <Sparkles className="h-5 w-5" />
            Ask AI
          </EnhancedButton>
          {children}
        </Fragment>
      )}
    </EditorBubble>
  );
};

export default GenerativeMenuSwitch;
