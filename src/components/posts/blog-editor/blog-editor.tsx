'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Editor<PERSON><PERSON>mandEmpty,
  Editor<PERSON><PERSON>mand<PERSON><PERSON>,
  Editor<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Editor<PERSON>ontent,
  EditorRoot,
  type JSONContent,
} from 'novel';
import { handleCommandNavigation, ImageResizer } from 'novel/extensions';
import { handleImageDrop, handleImagePaste } from 'novel/plugins';

import { Separator } from '@/components/ui/separator';

import { defaultExtensions } from './extensions';
import GenerativeMenuSwitch from './generative/generative-menu-switch';
import { uploadFn } from './image-upload';
import { ColorSelector } from './selectors/color-selector';
import { LinkSelector } from './selectors/link-selector';
import { MathSelector } from './selectors/math-selector';
import { NodeSelector } from './selectors/node-selector';
import { TextButtons } from './selectors/text-buttons';
import { slashCommand, suggestionItems } from './slash-command';

const extensions = [...defaultExtensions, slashCommand];

type Props = {
  value: JSONContent | undefined;
  onChange: (value: JSONContent) => void;
  contentLoaded: boolean;
};

const BlogEditor = ({ value, onChange, contentLoaded }: Props) => {
  const [openNode, setOpenNode] = useState(false);
  const [openColor, setOpenColor] = useState(false);
  const [openLink, setOpenLink] = useState(false);
  const [openAI, setOpenAI] = useState(false);

  return (
    <div className="relative w-full">
      <EditorRoot>
        <EditorContent
          // content is not updating so we force a re-render
          key={contentLoaded ? 'loaded' : 'loading'}
          immediatelyRender={false}
          initialContent={contentLoaded ? value : undefined}
          extensions={extensions}
          className="relative h-full w-full   sm:mb-[calc(20vh)]"
          editorProps={{
            handleDOMEvents: {
              keydown: (_view, event) => handleCommandNavigation(event),
            },
            handlePaste: (view, event) =>
              handleImagePaste(view, event, uploadFn),
            handleDrop: (view, event, _slice, moved) =>
              handleImageDrop(view, event, moved, uploadFn),
            attributes: {
              class:
                'prose prose-sm sm:prose-base lg:prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full ',
            },
          }}
          onUpdate={({ editor }) => {
            onChange({ json: editor.getJSON(), text: editor.getText() });
          }}
          slotAfter={<ImageResizer />}
        >
          <EditorCommand className="z-50 h-auto max-h-[330px] w-full sm:w-auto overflow-y-auto rounded-md border border-muted bg-background px-1 py-2 shadow-md transition-all">
            <EditorCommandEmpty className="px-2 text-muted-foreground">
              No results
            </EditorCommandEmpty>
            <EditorCommandList>
              {suggestionItems.map((item) => (
                <EditorCommandItem
                  value={item.title}
                  onCommand={(val) => item?.command?.(val)}
                  className="flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-accent aria-selected:bg-accent"
                  key={item.title}
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-md border border-muted bg-background">
                    {item.icon}
                  </div>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </EditorCommandItem>
              ))}
            </EditorCommandList>
          </EditorCommand>

          <GenerativeMenuSwitch open={openAI} onOpenChange={setOpenAI}>
            <Separator orientation="vertical" />
            <NodeSelector open={openNode} onOpenChange={setOpenNode} />
            <Separator orientation="vertical" />
            <LinkSelector open={openLink} onOpenChange={setOpenLink} />
            <Separator orientation="vertical" />
            <MathSelector />
            <Separator orientation="vertical" />
            <TextButtons />
            <Separator orientation="vertical" />
            <ColorSelector open={openColor} onOpenChange={setOpenColor} />
          </GenerativeMenuSwitch>
        </EditorContent>
      </EditorRoot>
    </div>
  );
};

export default BlogEditor;
