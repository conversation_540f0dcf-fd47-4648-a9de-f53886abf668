'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toggleBookmark } from '@/actions/bookmarks';
import { Bookmark, Heart, MessageCircle } from 'lucide-react';
import { toast } from 'sonner';

import { Post } from '@/types/post.types';
import { User } from '@/types/user.types';
import { useAuthPrompt } from '@/hooks/use-auth-prompt';
import { Badge } from '@/components/ui/badge';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { HoverCardAvatar } from '@/components/shared/hover-card-avatar';

const coverImage = '/blog-default-image.svg';

interface PostItemProps {
  post: Post;
  isBookmarked?: boolean;
}

export default function PostItem({
  post,
  isBookmarked = false,
}: PostItemProps) {
  const router = useRouter();
  const [localBookmarkStatus, setLocalBookmarkStatus] = useState(isBookmarked);
  const { checkAuth, AuthPrompt } = useAuthPrompt();

  const handleBookmarkClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    checkAuth(async () => {
      setLocalBookmarkStatus(!localBookmarkStatus);
      try {
        await toggleBookmark(post.id);
      } catch (error) {
        console.error('Error bookmarking:', error);
        setLocalBookmarkStatus(localBookmarkStatus);
        toast.error('Error', {
          description: 'Failed to bookmark. Please try again.',
        });
      }
    });
  };

  return (
    <>
      <div className="group overflow-hidden bg-background rounded-lg">
        {/* Make this div clickable instead of wrapping everything in Link */}
        <div
          onClick={() => router.push(`blog/${post.slug}`)}
          className="cursor-pointer"
        >
          <div className=" space-y-4">
            {/* Author info at the top */}
            <div className="flex items-center gap-3">
              <HoverCardAvatar user={post.author as User} />
              <div>
                <p className="text-sm font-medium">{post.author.name}</p>
                <p className="text-xs text-muted-foreground">
                  {new Date(post.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            {/* Cover image */}
            <div className="relative h-48 overflow-hidden rounded-lg">
              <Image
                src={post.cover || coverImage}
                alt={post.title}
                priority
                width={600}
                height={400}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
              <EnhancedButton
                variant="ghost"
                size="sm"
                onClick={handleBookmarkClick}
                className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm hover:bg-background/90"
              >
                <Bookmark
                  className={`w-5 h-5 ${
                    localBookmarkStatus ? 'fill-current text-primary' : ''
                  }`}
                />
              </EnhancedButton>
            </div>

            {/* Title and content */}
            <div className="space-y-2">
              <h2 className="text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors">
                {post.title}
              </h2>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <Badge key={tag.id} variant="secondary">
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </div>

            <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>

            {/* Stats at the bottom */}
            <div className="flex justify-end gap-3 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Heart className="w-3.5 h-3.5" />
                {post._count.likes}
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="w-3.5 h-3.5" />
                {post._count.comments}
              </div>
              <div className="flex items-center gap-1">
                <Bookmark className="w-3.5 h-3.5" />
                {post._count.bookmarks}
              </div>
            </div>
          </div>
        </div>
      </div>
      <AuthPrompt />
    </>
  );
}
