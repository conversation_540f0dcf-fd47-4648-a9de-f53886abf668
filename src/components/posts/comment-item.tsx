'use client';

import React, { useState } from 'react';
import { Edit, MoreVertical, ThumbsUp, Trash } from 'lucide-react';
import { User } from 'next-auth';

import { Author, Comment } from '@/types/post.types';
import { formatDate } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';
import { EnhancedButton } from '@/components/shared/enhanced-button';

import { HoverCardAvatar } from '../shared/hover-card-avatar';

type CommentItemProps = {
  comment: Comment;
  depth: number;
  currentUser: User | null | undefined;
  onLike: (commentId: string) => void;
  onReply: (commentId: string, content: string) => void;
  onEdit: (commentId: string, newContent: string) => void;
  onDelete: (commentId: string) => void;
  children?: React.ReactNode;
};

export default function CommentItem({
  comment,
  depth,
  currentUser,
  onLike,
  onReply,
  onEdit,
  onDelete,
  children,
}: CommentItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(comment.content);
  const [isReplying, setIsReplying] = useState(false);
  const [replyContent, setReplyContent] = useState('');

  const handleEdit = () => {
    setIsEditing(true);
    setEditedContent(comment.content);
  };

  const handleSaveEdit = () => {
    onEdit(comment.id, editedContent);
    setIsEditing(false);
  };

  const handleReply = () => {
    onReply(comment.id, replyContent);
    setIsReplying(false);
    setReplyContent('');
  };

  return (
    <div className={`space-y-4 ${depth > 0 ? 'ml-6' : ''}`}>
      <div className="flex items-start space-x-4">
        <HoverCardAvatar user={comment.author as Author} />

        <div className="flex-1 space-y-2">
          <div className="flex justify-between items-center">
            <div>
              <span className="font-semibold">{comment.author.name}</span>
              <span className="ml-2 text-sm text-gray-500">
                {formatDate(comment.createdAt)}
              </span>
            </div>

            {currentUser?.id === comment.authorId && (
              <CommentActions
                onEdit={handleEdit}
                onDelete={() => onDelete(comment.id)}
              />
            )}
          </div>

          <CommentContent
            comment={comment}
            isEditing={isEditing}
            editedContent={editedContent}
            onEditChange={setEditedContent}
            onSave={handleSaveEdit}
            onCancel={() => setIsEditing(false)}
          />

          <div className="flex space-x-4">
            <CommentLikeButton
              comment={comment}
              currentUser={currentUser}
              onLike={() => onLike(comment.id)}
            />
            <CommentReplyButton
              currentUser={currentUser}
              authorId={comment.authorId}
              onClick={() => setIsReplying(!isReplying)}
            />
          </div>
        </div>
      </div>

      {isReplying && (
        <ReplyForm
          value={replyContent}
          onChange={setReplyContent}
          onSubmit={handleReply}
          onCancel={() => setIsReplying(false)}
        />
      )}

      {children}
    </div>
  );
}

// Subcomponents for better organization
function CommentActions({
  onEdit,
  onDelete,
}: {
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <EnhancedButton variant="ghost" size="sm">
          <MoreVertical className="h-4 w-4" />
        </EnhancedButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={onEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onDelete}>
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function CommentContent({
  comment,
  isEditing,
  editedContent,
  onEditChange,
  onSave,
  onCancel,
}: {
  comment: Comment;
  isEditing: boolean;
  editedContent: string;
  onEditChange: (value: string) => void;
  onSave: () => void;
  onCancel: () => void;
}) {
  if (isEditing) {
    return (
      <div>
        <Textarea
          value={editedContent}
          onChange={(e) => onEditChange(e.target.value)}
          rows={3}
        />
        <div className="mt-2 space-x-2">
          <EnhancedButton onClick={onSave} size="sm">
            Save
          </EnhancedButton>
          <EnhancedButton onClick={onCancel} variant="outline" size="sm">
            Cancel
          </EnhancedButton>
        </div>
      </div>
    );
  }

  return (
    <p>
      {comment.parentId && (
        <span className="font-semibold">@{comment.author.name} </span>
      )}
      {comment.content}
      {new Date(comment.createdAt).getTime() !==
        new Date(comment.updatedAt).getTime() && (
        <span className="text-xs text-gray-500 ml-2">(Edited)</span>
      )}
    </p>
  );
}

function CommentLikeButton({
  comment,
  currentUser,
  onLike,
}: {
  comment: Comment;
  currentUser: User | null | undefined;
  onLike: () => void;
}) {
  return (
    <EnhancedButton
      variant="ghost"
      size="sm"
      disabled={!currentUser || currentUser.id === comment.authorId}
      onClick={onLike}
    >
      <ThumbsUp
        className={`w-4 h-4 mr-2 ${
          comment.likes?.some((like) => like.authorId === currentUser?.id)
            ? 'fill-primary'
            : ''
        }`}
      />
      {comment._count?.likes}
    </EnhancedButton>
  );
}

function CommentReplyButton({
  currentUser,
  authorId,
  onClick,
}: {
  currentUser: User | null | undefined;
  authorId: string;
  onClick: () => void;
}) {
  return (
    <EnhancedButton
      variant="ghost"
      disabled={!currentUser || currentUser.id === authorId}
      size="sm"
      onClick={onClick}
    >
      Reply
    </EnhancedButton>
  );
}

function ReplyForm({
  value,
  onChange,
  onSubmit,
  onCancel,
}: {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onCancel: () => void;
}) {
  return (
    <div className="ml-12 mt-2 space-y-4">
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Write a reply..."
        rows={2}
      />
      <div className="mt-2 space-x-2">
        <EnhancedButton onClick={onSubmit} className="mt-2" size="sm">
          Post Reply
        </EnhancedButton>
        <EnhancedButton
          onClick={onCancel}
          variant="outline"
          className="mt-2"
          size="sm"
        >
          Cancel
        </EnhancedButton>
      </div>
    </div>
  );
}
