import Image from 'next/image';
import Link from 'next/link';
import { Bell, MoreHorizontal } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function PostHeader() {
  return (
    <header className="py-2">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link className="mr-6 flex items-center space-x-2" href="/">
            <Image
              src="/saastarter-logo.svg"
              alt="Logo"
              width={150}
              height={40}
            />
          </Link>
          <div className="flex items-center space-x-4">
            <EnhancedButton variant="outline" size="sm">
              Save draft
            </EnhancedButton>
            <EnhancedButton variant="default" size="sm">
              Publish
            </EnhancedButton>
            <EnhancedButton
              variant="ghost"
              size="icon"
              className="text-gray-500"
            >
              <Bell className="h-5 w-5" />
            </EnhancedButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <EnhancedButton
                  variant="ghost"
                  size="icon"
                  className="text-gray-500"
                >
                  <MoreHorizontal className="h-5 w-5" />
                </EnhancedButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Add to publication</DropdownMenuItem>
                <DropdownMenuItem>Share draft link</DropdownMenuItem>
                <DropdownMenuItem>Manage versions</DropdownMenuItem>
                <DropdownMenuItem>Change featured image</DropdownMenuItem>
                <DropdownMenuItem>More settings</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
