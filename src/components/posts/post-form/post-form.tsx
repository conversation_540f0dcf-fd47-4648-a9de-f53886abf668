'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { createPost, updatePost } from '@/actions/posts';
import { uploadFile } from '@/actions/upload';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { Post, Tag } from '@/types/post.types';
import { safeStringify } from '@/lib/utils';
import { usePost } from '@/hooks/use-post';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import Container from '@/components/layout/container';
import BackButton from '@/components/shared/back-button';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import ParagraphSkeleton from '@/components/skeletons/paragraph.skeleton';

import { AutocompleteTagInput, PostCover, PostTitle } from './';

const Editor = dynamic(() => import('@/components/posts/blog-editor'), {
  ssr: false,
  loading: () => <ParagraphSkeleton />,
});

export default function PostForm({
  initialValues,
  tags = [],
}: {
  initialValues: Post | null;
  tags: Tag[];
}) {
  const { form, post } = usePost();

  const pathname = usePathname();
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isDraftSaving, setIsDraftSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  const slug = params.slug as string;
  const isUpdatePostRoute = pathname.startsWith(`/posts/${slug}/edit`);

  const {
    coverImageUrl,
    setCoverImageUrl,
    cover: postCover,
    setCover: setPostCover,
    contentLoaded,
    setContentLoaded,
    isPublished,
    setIsPublished,
  } = post;

  const hasCover = Boolean(coverImageUrl);

  // Add this effect to properly sync initial values
  useEffect(() => {
    if (initialValues) {
      setCoverImageUrl(initialValues.cover);
      setContentLoaded(true);
      setIsPublished(initialValues.published);

      // Batch set values to avoid race conditions
      form.reset({
        title: initialValues.title,
        content: initialValues.content,
        tags: initialValues.tags,
        cover: initialValues.cover,
      });
    }
  }, [initialValues, form]);

  const handleCoverImageSelect = ({
    image,
    url,
  }: {
    image: FormData;
    url: string;
  }) => {
    setCoverImageUrl(url);
    setPostCover(image);
  };

  const handleSaveDraft = async () => {
    if (isDraftSaving) return;

    setIsDraftSaving(true);
    try {
      const values = form.getValues();
      const { content, ...rest } = values;

      let cover;
      if (postCover) {
        const { url } = await uploadFile(postCover);
        cover = url;
      }

      const postData = {
        ...rest,
        content: safeStringify(content),
        published: false,
        cover,
      };

      if (isUpdatePostRoute) {
        await updatePost(slug, postData);
      } else {
        await createPost(postData);
      }

      queryClient.removeQueries({ queryKey: ['posts-by-author'] });
      await queryClient.invalidateQueries({ queryKey: ['posts-by-author'] });

      router.push('/posts');

      toast.success(
        `Your draft has been ${isUpdatePostRoute ? 'updated' : 'saved'} successfully.`
      );
    } catch (error) {
      toast.error('Error saving draft', {
        description: `Failed to save draft. Please try again. ${
          (error as Error).message
        }`,
      });
    } finally {
      setIsDraftSaving(false);
    }
  };

  const handlePublish = async () => {
    if (isPublishing) return;

    setIsPublishing(true);
    try {
      const values = form.getValues();
      const { content, ...rest } = values;

      if (!post.coverImageUrl || !rest.title || !rest.tags || !content) {
        toast.error('Missing required fields', {
          description:
            'Please fill in all required fields before publishing your post.',
        });
        setIsPublishing(false);
        return;
      }

      let cover;
      if (post.cover) {
        const { url } = await uploadFile(post.cover);
        cover = url;
      }

      const postData = {
        ...rest,
        content: safeStringify(content),
        published: true,
        cover,
      };

      if (isUpdatePostRoute) {
        await updatePost(slug, postData);
      } else {
        await createPost(postData);
      }
      queryClient.removeQueries({ queryKey: ['posts-by-author'] });
      queryClient.removeQueries({ queryKey: ['posts'] });

      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['posts-by-author'] }),
        queryClient.invalidateQueries({ queryKey: ['posts'] }),
      ]);

      router.push('/posts');
      toast.success('Your post has been published successfully.');
    } catch (error) {
      toast.error('Error publishing post', {
        description: `Failed to publish post. Please try again. ${
          (error as Error).message
        }`,
      });
    } finally {
      setIsPublishing(false);
    }
  };

  return (
    <>
      <Container>
        <header className="flex h-16 shrink-0 items-center gap-2 justify-between  relative flex-1 ">
          <BackButton />
          <div className={'md:items-center md:space-x-4'}>
            <div className="flex items-center space-x-4">
              {!isPublished && (
                <EnhancedButton
                  variant="outline"
                  size="sm"
                  onClick={handleSaveDraft}
                  isLoading={isDraftSaving}
                  loadingText="Saving..."
                  disabled={
                    (!form?.formState?.isDirty && isUpdatePostRoute) ||
                    isPublishing
                  }
                >
                  {form?.formState?.isDirty || !isUpdatePostRoute
                    ? 'Save draft'
                    : 'Saved'}
                </EnhancedButton>
              )}
              <EnhancedButton
                variant="default"
                size="sm"
                onClick={handlePublish}
                isLoading={isPublishing}
                loadingText="Publishing..."
                disabled={
                  (!form?.formState?.isDirty && isUpdatePostRoute) ||
                  isDraftSaving
                }
              >
                Publish
              </EnhancedButton>
            </div>
          </div>
        </header>

        <Container narrow className="px-0 py-0">
          <Form {...form}>
            <form className="space-y-8" id="post-form">
              <FormField
                control={form.control}
                name="cover"
                render={() => (
                  <FormItem>
                    <FormControl>
                      <PostCover
                        coverImageUrl={coverImageUrl}
                        hasCover={hasCover}
                        onCoverImageSelect={handleCoverImageSelect}
                        onCoverImageRemove={() => {
                          setCoverImageUrl(null);
                          setPostCover(null);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <PostTitle
                        onChange={field.onChange}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <AutocompleteTagInput
                        value={field.value}
                        onChange={field.onChange}
                        existingTags={tags}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Editor
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value.json);
                          form.setValue(
                            'excerpt',
                            value?.text?.substring?.(0, 150)
                          );
                        }}
                        contentLoaded={contentLoaded}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </Container>
      </Container>
    </>
  );
}
