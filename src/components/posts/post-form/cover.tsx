'use client';

import { useRef, useState } from 'react';
import { ImagePlus, Trash, UploadCloud } from 'lucide-react';

import { fileToFormData } from '@/lib/image-utils';
import { CoverImage } from '@/components/posts/cover-image';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface PostCoverProps {
  coverImageUrl: string | null;
  hasCover: boolean;
  onCoverImageSelect: ({
    image,
    url,
  }: {
    image: FormData;
    url: string;
  }) => void;
  onCoverImageRemove: () => void;
}

export default function PostCover({
  coverImageUrl,
  hasCover,
  onCoverImageSelect,
  onCoverImageRemove,
}: PostCoverProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const coverRef = useRef<HTMLImageElement>(null);
  const dragCounter = useRef(0);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      const formData = fileToFormData(file);
      onCoverImageSelect({ image: formData, url: imageUrl });
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current += 1;
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current -= 1;
    if (dragCounter.current === 0) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    dragCounter.current = 0;

    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      const imageUrl = URL.createObjectURL(file);
      const formData = fileToFormData(file);
      onCoverImageSelect({ image: formData, url: imageUrl });
    }
  };

  const renderButtons = () => {
    if (hasCover) {
      return (
        <>
          <div className="hidden md:flex space-x-2 absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out">
            <EnhancedButton
              type="button"
              onClick={handleButtonClick}
              variant="outline"
              size="sm"
              className="flex items-center"
              data-testid="desktop-replace-cover-button"
            >
              <ImagePlus className="mr-2 h-4 w-4" />
              Replace cover
            </EnhancedButton>

            <EnhancedButton
              type="button"
              onClick={onCoverImageRemove}
              variant="outline"
              size="sm"
              className="flex items-center"
              data-testid="desktop-remove-cover-button"
            >
              <Trash className="mr-2 h-4 w-4" />
              Remove cover
            </EnhancedButton>
          </div>

          <div className="flex md:hidden space-x-2 absolute top-4 right-4">
            <EnhancedButton
              type="button"
              onClick={handleButtonClick}
              variant="outline"
              size="sm"
              className="flex items-center justify-center"
              data-testid="mobile-replace-cover-button"
            >
              <ImagePlus className=" h-4 w-4" />
            </EnhancedButton>

            <EnhancedButton
              type="button"
              onClick={onCoverImageRemove}
              variant="outline"
              size="sm"
              className="flex items-center justify-center"
              data-testid="mobile-remove-cover-button"
            >
              <Trash className=" h-4 w-4" />
            </EnhancedButton>
          </div>
        </>
      );
    } else {
      return (
        <>
          <div className="hidden absolute top-4 right-4 md:flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out">
            <EnhancedButton
              type="button"
              onClick={handleButtonClick}
              variant="outline"
              size="sm"
              className="flex items-center"
              data-testid="desktop-add-cover-button"
            >
              <ImagePlus className="mr-2 h-4 w-4" />
              Add cover
            </EnhancedButton>
          </div>
          <div className="md:hidden absolute top-4 right-4 z-10">
            <EnhancedButton
              type="button"
              onClick={handleButtonClick}
              variant="outline"
              size="sm"
              className="flex items-center"
              data-testid="mobile-add-cover-button"
            >
              <ImagePlus className="mr-2 h-4 w-4" />
              Add cover
            </EnhancedButton>
          </div>
        </>
      );
    }
  };

  return (
    <div
      className={`relative group space-y-2 ${
        !coverImageUrl && !hasCover
          ? 'w-full aspect-video rounded-lg bg-muted'
          : ''
      }`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {renderButtons()}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept="image/*"
        data-testid="cover-image-input"
      />
      {coverImageUrl && hasCover ? (
        <CoverImage ref={coverRef} src={coverImageUrl} alt="Blog cover image" />
      ) : (
        <div className="absolute inset-0 flex items-center justify-center md:pointer-events-none">
          <div className="text-center hidden md:block">
            <div className="flex justify-center items-center">
              <UploadCloud className="h-10 w-10 text-muted-foreground text-center" />
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              Drag and drop to upload
            </p>
          </div>
        </div>
      )}
      {isDragging && (
        <div className="absolute inset-0 bg-primary bg-opacity-50 flex items-center justify-center rounded-lg">
          <p className="text-white text-sm ">Drop image here</p>
        </div>
      )}
    </div>
  );
}
