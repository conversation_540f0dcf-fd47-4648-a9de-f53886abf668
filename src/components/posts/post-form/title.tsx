'use client';

import { useEffect, useRef } from 'react';

interface PostTitleProps {
  onChange: (value: string) => void;
  value: string | undefined;
}

function PostTitle({ onChange, value }: PostTitleProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height =
        textareaRef.current.scrollHeight + 'px';
    }
  }, [value]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="relative group space-y-2">
      <textarea
        autoFocus
        ref={textareaRef}
        value={value}
        onChange={handleTitleChange}
        placeholder="Untitled"
        className="font-heading w-full resize-none overflow-hidden bg-transparent text-3xl md:text-4xl lg:text-5xl font-bold focus:outline-none"
        rows={1}
      />
    </div>
  );
}

export default PostTitle;
