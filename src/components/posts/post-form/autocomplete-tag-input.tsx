'use client';

import { KeyboardEvent, useRef, useState } from 'react';
import { Tag as TagIcon, X } from 'lucide-react';

import { Tag, TagInput } from '@/types/post.types';
import { cn, slugify } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface AutocompleteTagInputProps {
  existingTags: Tag[];
  value: TagInput[] | undefined;
  onChange: (tags: TagInput[]) => void;
}

export default function AutocompleteTagInput({
  existingTags,
  value = [], // Provide default value
  onChange,
}: AutocompleteTagInputProps) {
  const tags = value || [];
  const [tagInput, setTagInput] = useState('');
  const [showTagInput, setShowTagInput] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const tagInputRef = useRef<HTMLInputElement>(null);

  const handleAddTag = (newTag: TagInput) => {
    if (
      newTag.name.trim() !== '' &&
      !tags.some((tag) => tag.slug === newTag.slug)
    ) {
      const updatedTags = [...tags, newTag];
      onChange(updatedTags); // Ensure we're passing the complete updated array
      setTagInput('');
      setSelectedIndex(-1);
    }
  };

  const removeTag = (tagToRemove: TagInput) => {
    const updatedTags = tags.filter((tag) => tag.slug !== tagToRemove.slug);
    onChange(updatedTags); // Ensure we're passing the complete updated array
    if (updatedTags.length === 0) {
      setShowTagInput(false);
    }
  };

  const filteredTags = existingTags?.filter(
    (tag) =>
      tag.name.toLowerCase().includes(tagInput.toLowerCase()) &&
      !tags.some((t) => t.slug === tag.slug)
  );

  const handleTagKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex((prev) =>
        prev < filteredTags.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex((prev) => (prev > 0 ? prev - 1 : prev));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (filteredTags.length > 0) {
        const tagToAdd =
          selectedIndex >= 0 ? filteredTags[selectedIndex] : filteredTags[0];
        handleAddTag({ name: tagToAdd.name, slug: tagToAdd.slug });
      } else if (tagInput.trim() !== '') {
        const newTag = {
          name: tagInput.trim(),
          slug: slugify(tagInput.trim()),
        };
        handleAddTag(newTag);
      }
    } else if (
      (e.key === 'Backspace' || e.key === 'Delete') &&
      !tagInput &&
      tags.length > 0
    ) {
      e.preventDefault();
      onChange(tags.slice(0, -1));
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-2">
      {tags.map((tag) => (
        <span
          data-tag
          key={tag.slug}
          className="bg-secondary text-secondary-foreground px-2 py-1 rounded-full text-sm flex items-center"
        >
          {tag.name}
          <button
            onClick={() => removeTag(tag)}
            aria-label={`Remove ${tag.name} tag`}
            className="ml-1 text-muted-foreground hover:text-foreground"
          >
            <X className="w-3 h-3" />
          </button>
        </span>
      ))}
      {showTagInput ? (
        <div className="relative inline-block z-10">
          <Input
            ref={tagInputRef}
            type="text"
            placeholder={
              tags.length > 0 ? 'Add another tag...' : 'Add a tag...'
            }
            value={tagInput}
            onChange={(e) => {
              setTagInput(e.target.value);
              setSelectedIndex(-1);
            }}
            onKeyDown={handleTagKeyDown}
            className="border-none shadow-none focus-visible:ring-transparent px-0 w-32  text-md"
          />
          {tagInput && (
            <div className="absolute left-0 right-0 mt-1">
              <ScrollArea
                className={cn(
                  'w-64 rounded-md border bg-background',
                  // If there are more than 4 items, set fixed height
                  filteredTags.length > 4 ? 'h-48' : 'max-h-48'
                )}
              >
                <div className="p-2">
                  {filteredTags.length > 0 ? (
                    filteredTags.map((tag, index) => (
                      <button
                        aria-label={`Add ${tag.name} tag`}
                        key={tag.slug}
                        className={`block w-full text-left text-sm px-2 py-1 rounded ${
                          index === selectedIndex
                            ? 'bg-accent'
                            : 'hover:bg-accent'
                        }`}
                        onClick={() =>
                          handleAddTag({ name: tag.name, slug: tag.slug })
                        }
                        onMouseEnter={() => setSelectedIndex(index)}
                      >
                        <div className="flex items-center gap-2">
                          <TagIcon className="h-3 w-3 text-muted-foreground" />
                          {tag.name}
                        </div>
                      </button>
                    ))
                  ) : (
                    <>
                      <p className="text-sm text-gray-500 px-2 py-1 text-center">
                        No results found
                      </p>
                      <p className="text-xs text-gray-500 px-2 py-1 text-center">
                        Press (Enter) to add a new tag
                      </p>
                    </>
                  )}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      ) : (
        <EnhancedButton
          size="sm"
          variant="outline"
          onClick={() => {
            setShowTagInput(true);
            setTimeout(() => tagInputRef.current?.focus(), 0);
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <TagIcon className="w-4 h-4 mr-2" />
          Add tags
        </EnhancedButton>
      )}
    </div>
  );
}
