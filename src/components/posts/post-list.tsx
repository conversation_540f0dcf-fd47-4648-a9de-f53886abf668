'use client';

import { useCallback, useMemo, useState } from 'react';
import { getPosts } from '@/actions/posts';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';

import { GetPostsParams, Post } from '@/types/post.types';
import { GetCursorPaginationResult } from '@/types/shared.types';
import Container from '@/components/layout/container';
import FilterSort from '@/components/posts/filter-sort';
import PostItem from '@/components/posts/post-item';
import PostSearch from '@/components/posts/search';
import EmptyState from '@/components/shared/empty-state';
import PostsListSkeleton from '@/components/skeletons/posts-list.skeleton';

const initialParams: GetPostsParams = {
  take: 10,
  orderBy: { createdAt: 'desc' },
  filter: {
    authors: [],
    tags: [],
    searchQuery: '',
  },
};

type SortOptions =
  | 'Most Recent'
  | 'Most Liked'
  | 'Most Commented'
  | 'Most Bookmarked';
const getSortOrderBy = (sortOption: SortOptions) => {
  const sortMapping = {
    'Most Recent': { createdAt: 'desc' },
    'Most Liked': { '_count.likes': 'desc' },
    'Most Bookmarked': { '_count.bookmarks': 'desc' },
    'Most Commented': { '_count.comments': 'desc' },
  } as const;

  return sortMapping[sortOption];
};

export default function PostsList({ className }: { className?: string }) {
  const session = useSession();

  const [selectedAuthors, setSelectedAuthors] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortOption, setSortOption] = useState<SortOptions>('Most Recent');

  const queryParams: GetPostsParams = {
    ...initialParams,
    orderBy: getSortOrderBy(sortOption),
    filter: {
      ...initialParams.filter,
      authors: selectedAuthors,
      tags: selectedCategories,
    },
  };

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useInfiniteQuery<GetCursorPaginationResult<Post>>({
    queryKey: ['posts', queryParams],
    queryFn: async ({ pageParam }) => {
      const response = await getPosts({
        ...queryParams,
        cursor: pageParam as string | undefined,
      });
      return response;
    },
    initialPageParam: null as string | null,
    getNextPageParam: (lastPage) =>
      lastPage.hasMore ? lastPage.nextCursor : undefined,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60, // 1 minute
  });

  // Let's debug the data structure
  const posts = useMemo(() => {
    const allPosts = data?.pages.flatMap((page) => page.items) ?? [];

    // Remove duplicates based on post.id
    const uniquePosts = Array.from(
      new Map(allPosts.map((post) => [post.id, post])).values()
    );

    return uniquePosts;
  }, [data]);

  const hasMore = Boolean(hasNextPage);

  const loadMorePosts = useCallback(async () => {
    if (hasMore && !isFetchingNextPage && posts.length > 0) {
      await fetchNextPage();
    }
  }, [hasMore, isFetchingNextPage, posts.length, fetchNextPage]);

  const resetAndFetchPosts = async () => {
    await refetch();
  };

  const lastPostElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (!node || !hasMore || isFetchingNextPage) return;

      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore && !isFetchingNextPage) {
            loadMorePosts();
          }
        },
        { threshold: 0.5 }
      );

      observer.observe(node);
      return () => observer.disconnect();
    },
    [hasMore, isFetchingNextPage, loadMorePosts]
  );

  const handleFilterChange = useCallback(
    (filter: string) => {
      const newAuthors = authors.includes(filter)
        ? selectedAuthors.includes(filter)
          ? selectedAuthors.filter((a) => a !== filter)
          : [...selectedAuthors, filter]
        : selectedAuthors;
      const newCategories = categories.includes(filter)
        ? selectedCategories.includes(filter)
          ? selectedCategories.filter((c) => c !== filter)
          : [...selectedCategories, filter]
        : selectedCategories;
      setSelectedAuthors(newAuthors);
      setSelectedCategories(newCategories);
    },
    [selectedAuthors, selectedCategories]
  );

  const handleSortChange = useCallback((newSortOption: string) => {
    if (
      newSortOption === 'Most Recent' ||
      newSortOption === 'Most Liked' ||
      newSortOption === 'Most Commented' ||
      newSortOption === 'Most Bookmarked' // Added this condition
    ) {
      setSortOption(newSortOption as SortOptions);
    }
  }, []);

  const handleReset = useCallback(() => {
    setSelectedAuthors([]);
    setSelectedCategories([]);
    setSortOption('Most Recent');
    resetAndFetchPosts();
  }, [resetAndFetchPosts]);

  if (isError) {
    return (
      <div className="text-center p-8">
        <p>Error: {error.message}</p>
        <button
          onClick={() => resetAndFetchPosts()}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  const authors = ['Ricardo Enciso'];
  const categories = ['javascript', 'next-js'];

  const showEmptyState =
    !isLoading &&
    posts.length === 0 &&
    selectedAuthors.length === 0 &&
    selectedCategories.length === 0;

  return (
    <Container narrow className={className}>
      <div className="px-4 py-8">
        {showEmptyState && (
          <EmptyState
            title="No posts found"
            description="Check back later for new posts or add a post."
            actionLabel="Add a post"
            onAction={() => {}}
          />
        )}
        {!showEmptyState && (
          <div className="mb-8 flex items-center gap-4">
            <PostSearch />
            <FilterSort
              filterOptions={
                [
                  // { label: 'Authors', values: authors },
                  // { label: 'Categories', values: categories },
                ]
              }
              sortOptions={[
                'Most Recent',
                'Most Liked',
                'Most Commented',
                'Most Bookmarked',
              ]}
              selectedFilters={[...selectedAuthors, ...selectedCategories]}
              selectedSort={sortOption}
              onFilterChange={handleFilterChange}
              onSortChange={handleSortChange}
              onReset={handleReset}
              aria-label="Filter and sort posts"
            />
          </div>
        )}

        {/* Show skeleton while loading initial data */}
        {isLoading && <PostsListSkeleton />}

        {/* Show skeleton at the bottom while fetching next page */}
        {isFetchingNextPage && (
          <div className="mt-10">
            <PostsListSkeleton />
          </div>
        )}

        {posts.length > 0 && (
          <>
            <div className="grid gap-10 sm:grid-cols-2">
              {posts.map((post, index) => (
                <div
                  key={post.id}
                  ref={index === posts.length - 1 ? lastPostElementRef : null}
                >
                  <PostItem
                    post={post}
                    isBookmarked={post.bookmarks?.some(
                      (b) => b.userId === session.data?.user?.id
                    )}
                  />
                </div>
              ))}
            </div>

            {isFetchingNextPage && (
              <div className="mt-8">
                <PostsListSkeleton />
              </div>
            )}

            {!isLoading && !hasMore && (
              <p className="text-center p-8">You&apos;ve reached the end.</p>
            )}
          </>
        )}

        {!isLoading &&
          !hasMore &&
          posts.length === 0 &&
          (selectedAuthors.length > 0 || selectedCategories.length > 0) && (
            <p className="text-center p-8">No items found</p>
          )}
      </div>
    </Container>
  );
}
