'use client';

import { useEffect, useOptimistic, useState, useTransition } from 'react';
import Link from 'next/link';
import { toggleBookmark } from '@/actions/bookmarks';
import {
  createComment,
  deleteComment,
  toggleCommentLike,
  updateComment,
} from '@/actions/comments';
import { toggleLike } from '@/actions/likes';
import { getRelatedPosts } from '@/actions/posts/get-related-posts.action';
import { useNotification } from '@/contexts/notification.context';
import { Edit } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { JSONContent } from 'novel';
import { toast } from 'sonner';

import { Comment, Post } from '@/types/post.types';
import { User } from '@/types/user.types';
import { formatDate } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import Container from '@/components/layout/container';
import PostRenderer from '@/components/posts/blog-editor/post-renderer';
import { CoverImage } from '@/components/posts/cover-image';
import PostButtons from '@/components/posts/post-buttons';
import PostCommentTextArea from '@/components/posts/post-comment-text-area';
import PostComments from '@/components/posts/post-comments';
import PostTags from '@/components/posts/post-tags';
import RelatedPosts from '@/components/posts/related-posts';
import { Confirm } from '@/components/shared/confirm';
import { EnhancedButton } from '@/components/shared/enhanced-button';

import { HoverCardAvatar } from '../shared/hover-card-avatar';

const coverImage = '/blog-default-image.svg';

interface PostDetailProps {
  post: Post;
  isLiked: boolean;
  isBookmarked: boolean;
  comments: Comment[];
}

export type ReplyInputsType = { [key: string]: string };

export function PostDetail({
  post,
  isLiked,
  isBookmarked,
  comments,
}: PostDetailProps) {
  const session = useSession();

  const { refetch } = useNotification();

  const [newComment, setNewComment] = useState('');
  const [localLikeStatus, setLocalLikeStatus] = useState(isLiked);
  const [localBookmarkStatus, setLocalBookmarkStatus] = useState(isBookmarked);

  // Post Buttons Optimistic State
  const [optimisticBookmarkCount, setOptimisticBookmarkCount] = useOptimistic(
    post._count.bookmarks
  );
  const [optimisticLikesCount, setOptimisticLikesCount] = useOptimistic(
    post._count.likes
  );

  const [relatedPosts, setRelatedPosts] = useState<Post[]>([]);
  const [loadingRelatedPosts, setLoadingRelatedPosts] = useState(true);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);

  const isAuthor = session.data?.user?.id === post.author.id;

  const [, startTransition] = useTransition();

  // Start Post Buttons actions
  const handleLikeToggle = () => {
    if (!session.data?.user) {
      toast.error('Error', {
        description: 'You must be logged in to like a post.',
      });
      return;
    }

    startTransition(async () => {
      try {
        await toggleLike(post.id);
        refetch();
      } catch (error) {
        console.error('Error liking:', error);
        toast.error('Error', {
          description: 'Failed to like. Please try again.',
        });
      }
    });
    setOptimisticLikesCount((state) =>
      localLikeStatus ? state - 1 : state + 1
    );
    setLocalLikeStatus(!localLikeStatus);
  };

  const handleBookmarkToggle = async () => {
    if (!session.data?.user) {
      toast.error('Error', {
        description: 'You must be logged in to bookmark a post.',
      });
      return;
    }

    startTransition(async () => {
      try {
        await toggleBookmark(post.id);
        refetch();
      } catch (error) {
        console.error('Error bookmarking:', error);
        toast.error('Error', {
          description: 'Failed to bookmark. Please try again.',
        });
      }
    });
    setOptimisticBookmarkCount((state) =>
      localBookmarkStatus ? state - 1 : state + 1
    );
    setLocalBookmarkStatus(!localBookmarkStatus);
  };

  // End Post Buttons actions

  useEffect(() => {
    const fetchRelatedPosts = async () => {
      try {
        const tagIds = post?.tags?.map((tag) => tag.id);
        const related = await getRelatedPosts(post.id, tagIds);
        setRelatedPosts(related as Post[]);
      } catch (error) {
        console.error('Error fetching related posts:', error);
      } finally {
        setLoadingRelatedPosts(false);
      }
    };

    fetchRelatedPosts();
  }, [post.id, post.tags]);

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    const optimisticComment: Comment = {
      id: `temp-${Date.now()}`,
      content: newComment,
      createdAt: new Date(),
      updatedAt: new Date(),
      authorId: session?.data?.user?.id || '',
      parentId: null,
      postId: post.id,
      author: {
        id: session?.data?.user?.id || '',
        username: session.data?.user?.username || '',
        name: session?.data?.user?.name || '',
        image: session?.data?.user?.image || '',
        email: session?.data?.user?.email || '',
        bio: session?.data?.user?.bio || '',
        urls: session.data?.user?.urls || [],
      },
      _count: { likes: 0 },
      likes: [], // Add this to match the structure
      replies: [], // Add this to match the structure
    };

    startTransition(async () => {
      setOptimisticComments((comments) => [optimisticComment, ...comments]);
      setNewComment('');

      try {
        await createComment(post.id, newComment);
        refetch();
      } catch (error) {
        console.error('Error creating comment:', error);
        toast.error('Error', {
          description: 'Failed to create comment. Please try again.',
        });
      }
    });
  };

  const [optimisticComments, setOptimisticComments] =
    useOptimistic<Comment[]>(comments);

  const handleCommentDelete = async () => {
    if (!commentToDelete) return;

    startTransition(async () => {
      // Find the comment to determine if it's a reply
      const commentToDeleteData = optimisticComments.find(
        (c) => c.id === commentToDelete
      );
      const isReply = commentToDeleteData?.parentId !== null;

      setOptimisticComments((currentComments) => {
        if (isReply) {
          // If it's a reply, update the parent comment's replies array
          return currentComments.map((comment) => ({
            ...comment,
            replies: comment.replies?.filter(
              (reply) => reply.id !== commentToDelete
            ),
          }));
        } else {
          // If it's a top-level comment, remove it and all its replies
          return currentComments.filter(
            (comment) =>
              comment.id !== commentToDelete &&
              comment.parentId !== commentToDelete
          );
        }
      });

      try {
        await deleteComment(commentToDelete);
        toast.success('Comment deleted', {
          description: 'The comment has been successfully deleted.',
        });
      } catch (error) {
        console.error('Error deleting comment:', error);
        toast.error('Error', {
          description: 'Failed to delete the comment. Please try again.',
        });
      } finally {
        setIsDeleteConfirmOpen(false);
        setCommentToDelete(null);
      }
    });
  };

  const handleReply = async (parentCommentId: string, content: string) => {
    if (!content || !content.trim()) return;

    const optimisticReply: Comment = {
      id: `temp-${Date.now()}`,
      content,
      createdAt: new Date(),
      updatedAt: new Date(),
      authorId: session.data?.user?.id || '',
      parentId: parentCommentId,
      postId: post.id,
      author: {
        id: session.data?.user?.id || '',
        username: session.data?.user?.username || '',
        name: session.data?.user?.name || '',
        image: session.data?.user?.image || '',
        email: session.data?.user?.email || '',
        bio: session.data?.user?.bio || '',
        urls: session.data?.user?.urls || [],
      },
      _count: { likes: 0 },
      likes: [], // Add this to match the structure
      replies: [], // Add this to match the structure
    };

    startTransition(async () => {
      // Update the optimistic state to properly nest the reply
      setOptimisticComments((currentComments) => {
        return currentComments.map((comment) => {
          if (comment.id === parentCommentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), optimisticReply],
            };
          }
          return comment;
        });
      });

      try {
        await createComment(post.id, content, parentCommentId);
      } catch (error) {
        console.error('Error creating reply:', error);
        toast.error('Error', {
          description: 'Failed to create reply. Please try again.',
        });
      }
    });
  };

  const handleCommentLikeToggle = async (commentId: string) => {
    if (!session.data?.user) return;

    const userId = session.data.user.id;
    const updateCommentLikes = (comment: Comment) => {
      const isLiked = comment.likes?.some((like) => like.authorId === userId);
      const newLikes = isLiked
        ? comment.likes?.filter((like) => like.authorId !== userId)
        : [
            ...(comment.likes || []),
            {
              id: `temp-${Date.now()}`,
              authorId: userId,
              commentId: commentId,
              postId: null,
              createdAt: new Date(),
              updatedAt: new Date(),
              author: {
                id: userId,
                name: session.data.user?.name || '',
                email: session.data.user?.email || '',
                image: session.data.user?.image || '',
                username: session.data.user?.username || '',
                bio: session.data.user?.bio || '',
                urls: session.data.user?.urls || [],
              },
            }, // Cast the new like object to Like type
          ];

      return {
        ...comment,
        likes: newLikes,
        _count: {
          ...comment._count,
          likes: newLikes?.length || 0,
        },
      } as Comment; // Cast the entire result to Comment type
    };

    startTransition(async () => {
      setOptimisticComments((currentComments: Comment[]) =>
        currentComments.map((comment) => {
          if (comment.id === commentId) {
            return updateCommentLikes(comment);
          }
          // Check replies if present
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map((reply) =>
                reply.id === commentId ? updateCommentLikes(reply) : reply
              ),
            };
          }
          return comment;
        })
      );

      try {
        await toggleCommentLike(commentId);
      } catch (error) {
        console.error('Error toggling comment like:', error);
        toast.error('Error', {
          description:
            'Failed to update comment like status. Please try again.',
        });
      }
    });
  };

  const handleCommentEdit = async (commentId: string, newContent: string) => {
    startTransition(async () => {
      setOptimisticComments((comments) =>
        comments.map((comment) =>
          comment.id === commentId
            ? { ...comment, content: newContent, updatedAt: new Date() }
            : comment
        )
      );

      try {
        await updateComment(commentId, newContent);
        toast.success('Comment updated', {
          description: 'The comment has been successfully updated.',
        });
      } catch (error) {
        console.error('Error updating comment:', error);
        toast.error('Error', {
          description: 'Failed to update the comment. Please try again.',
        });
      }
    });
  };

  // Helper function to trigger the delete confirmation
  const handleDelete = (commentId: string) => {
    setCommentToDelete(commentId);
    setIsDeleteConfirmOpen(true);
  };

  return (
    <Container narrow className="pt-0 pb-6">
      <article>
        <header className="mb-8 space-y-4">
          <h1 className="text-4xl font-bold">{post.title}</h1>
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <HoverCardAvatar user={post.author as User} />
              <div>
                <p className="font-semibold">{post.author.name}</p>
                <p className="text-sm text-gray-500">
                  {formatDate(post.createdAt)}
                </p>
              </div>
            </div>

            {isAuthor && (
              <Link href={`/posts/${post.slug}/edit`} passHref>
                <EnhancedButton size="sm">
                  <Edit className="w-5 h-5 mr-2" />
                  Edit
                </EnhancedButton>
              </Link>
            )}
          </div>
        </header>

        <CoverImage
          src={post.cover || coverImage}
          alt="Blog cover image"
          width={1200}
          height={630}
          priority
          className="w-full aspect-video object-cover rounded-lg shadow-lg mb-4 border-2 border-transparent"
        />

        <PostTags post={post} />

        <PostRenderer content={post.content as JSONContent} contentLoaded />

        <PostButtons
          post={post}
          likesCount={optimisticLikesCount}
          bookmarksCount={optimisticBookmarkCount}
          commentsCount={post._count.comments}
          onLikeClick={handleLikeToggle}
          onBookmarkClick={handleBookmarkToggle}
          isLiked={localLikeStatus}
          isBookmarked={localBookmarkStatus}
        />

        {/* hide on mobile  */}
        <div className="hidden sm:block mt-4">
          <Separator />
        </div>

        <div className="mt-12 mb-12">
          <h2 className="text-2xl font-bold mb-4">
            Comments ({post._count.comments || 0})
          </h2>

          <PostCommentTextArea
            onChange={setNewComment}
            value={newComment}
            onSubmit={handleCommentSubmit}
          />

          <PostComments
            comments={optimisticComments} // Changed from comments to optimisticComments
            onCommentLikeToggle={handleCommentLikeToggle}
            onDelete={handleDelete}
            onCommentEdit={handleCommentEdit}
            onReply={handleReply}
          />
        </div>

        <Separator />

        <RelatedPosts
          relatedPosts={relatedPosts}
          loadingRelatedPosts={loadingRelatedPosts}
        />
      </article>

      <Confirm
        isOpen={isDeleteConfirmOpen}
        setIsOpen={setIsDeleteConfirmOpen}
        onCancel={() => {
          setIsDeleteConfirmOpen(false);
          setCommentToDelete(null);
        }}
        onConfirm={handleCommentDelete}
        title="Confirm Delete Comment"
        description="Are you sure you want to delete this comment? This action cannot be undone."
      />
    </Container>
  );
}
