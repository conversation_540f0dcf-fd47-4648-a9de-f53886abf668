import { ChevronDown, Filter } from 'lucide-react';

import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface FilterSortProps {
  filterOptions: { label: string; values: string[] }[];
  sortOptions: string[];
  selectedFilters: string[];
  selectedSort: string;
  onFilterChange: (filter: string) => void;
  onSortChange: (sort: string) => void;
  onReset: () => void;
}

export default function FilterSort({
  filterOptions,
  sortOptions,
  selectedFilters,
  selectedSort,
  onFilterChange,
  onSortChange,
  onReset,
}: FilterSortProps) {
  return (
    <div className="flex-1 flex items-center gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <EnhancedButton variant="outline" className=" justify-between">
            <Filter className="h-4 w-4 mr-2 text-gray-500" />
            {selectedFilters.length > 0
              ? `${selectedFilters.length} selected`
              : selectedSort || 'Filter & Sort'}
            <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
          </EnhancedButton>
        </PopoverTrigger>
        <PopoverContent className="min-w-46 max-w-[286px] p-0" align="start">
          <div className="p-4 space-y-4">
            <div className="space-y-2">
              {filterOptions.map((filterOption, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-medium">{filterOption.label}</h4>
                  {filterOption.values.map((value) => (
                    <div key={value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`${filterOption.label}-${value}`}
                        checked={selectedFilters.includes(value)}
                        onCheckedChange={() => onFilterChange(value)}
                      />
                      <Label htmlFor={`${filterOption.label}-${value}`}>
                        {value}
                      </Label>
                    </div>
                  ))}
                </div>
              ))}
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Sort by</h4>
              {sortOptions.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <Checkbox
                    id={option}
                    checked={selectedSort === option}
                    onCheckedChange={() => onSortChange(option)}
                  />
                  <Label htmlFor={option}>{option}</Label>
                </div>
              ))}
            </div>
            <EnhancedButton className="w-full" onClick={onReset}>
              Reset
            </EnhancedButton>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
