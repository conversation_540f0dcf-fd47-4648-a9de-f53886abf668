import React from 'react';

import { Post } from '@/types/post.types';

type PostTagsProps = {
  post: Post;
};

export default function PostTags({ post }: PostTagsProps) {
  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {post?.tags?.map((tag) => (
        <span
          key={tag.id}
          className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm"
        >
          {tag.name}
        </span>
      ))}
    </div>
  );
}
