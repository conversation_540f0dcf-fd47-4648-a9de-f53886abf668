'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';

import { Item } from '@/lib/docs-utils/table-of-contents-generator';
import { cn } from '@/lib/utils';

type TocItem = Item;

type TocProps = {
  items?: TocItem[];
};

const TocItem = ({
  item,
  activeId,
}: {
  item: TocItem;
  activeId: string | null;
}) => {
  const isActive = activeId === item.url.slice(1); // Remove '#' from the start

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetId = item.url.slice(1); // Remove '#' from the start
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
      window.history.pushState(null, '', item.url);
    }
  };

  return (
    <li className="mt-2 pt-2">
      <Link
        className={cn(
          'inline-block no-underline transition-colors hover:text-foreground',
          isActive ? 'text-foreground font-medium' : 'text-muted-foreground'
        )}
        href={item.url}
        onClick={handleClick}
      >
        {item.title}
      </Link>
      {item.items && item.items.length > 0 && (
        <ul className="m-0 list-none pl-5">
          {item?.items.map((subItem: any) => (
            <TocItem key={subItem.url} item={subItem} activeId={activeId} />
          ))}
        </ul>
      )}
    </li>
  );
};

export default function DocsTableOfContents({ items }: TocProps) {
  const [activeId, setActiveId] = useState<string | null>(null);

  useEffect(() => {
    const observers: IntersectionObserver[] = [];

    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setActiveId(entry.target.id);
        }
      });
    };

    headings.forEach((heading) => {
      const observer = new IntersectionObserver(observerCallback, {
        rootMargin: '0px 0px -80% 0px',
      });
      observer.observe(heading);
      observers.push(observer);
    });

    return () => {
      observers.forEach((observer) => observer.disconnect());
    };
  }, []);

  return (
    <div className="hidden text-sm xl:block">
      <div className="fixed top-26 -mt-10 max-h-[calc(var(--vh)-4rem)] overflow-y-auto pt-10">
        <div className="space-y-2">
          <p className="font-medium">On This Page</p>
          <ul className="m-0 list-none">
            {items?.map((item: TocItem) => (
              <TocItem key={item.url} item={item} activeId={activeId} />
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
