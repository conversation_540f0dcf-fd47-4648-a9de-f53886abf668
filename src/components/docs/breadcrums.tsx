'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight } from 'lucide-react';

import { capitalize } from '@/lib/utils';

export default function DocsBreadcrumbs() {
  const pathname = usePathname();
  const segments = pathname.split('/').filter(Boolean);

  // Remove 'docs' from the beginning if it exists
  if (segments[0] === 'docs') {
    segments.shift();
  }

  const breadcrumbs = segments.map((segment, index) => {
    const href = `/docs/${segments.slice(0, index + 1).join('/')}`;
    const label =
      segment === 'index'
        ? capitalize(segments[index - 1] || 'Docs')
        : capitalize(segment);
    return { href, label };
  });

  // Handle the root docs page
  if (
    breadcrumbs.length === 0 ||
    (breadcrumbs.length === 1 && breadcrumbs[0].label.toLowerCase() === 'index')
  ) {
    breadcrumbs[0] = { href: '/docs', label: 'Documentation' };
  }

  return (
    <nav
      aria-label="Breadcrumb"
      className="mb-4 flex items-center space-x-1 text-sm text-muted-foreground"
    >
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.href}>
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          {index === breadcrumbs.length - 1 ? (
            <span className="font-medium text-foreground">{crumb.label}</span>
          ) : (
            <Link href={crumb.href} className="hover:text-foreground">
              {crumb.label}
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
