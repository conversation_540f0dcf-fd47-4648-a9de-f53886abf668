import Image from 'next/image';
import Link from 'next/link';
import { docsConfig } from '@/config';
import { Menu } from 'lucide-react';
import { FaGithub } from 'react-icons/fa';

import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import DocsSearch from '@/components/docs/search';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function DocsTopBar() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link className="mr-6 flex items-center space-x-2" href="/">
            <span className="hidden font-bold sm:inline-block">
              <Image
                src="/saastarter-logo.svg"
                alt="Logo"
                width={200}
                height={40}
              />
            </span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            {docsConfig.mainNav.map((link) => (
              <Link
                key={link.title}
                href={link.href}
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                {link.title}
              </Link>
            ))}
          </nav>
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <EnhancedButton
              className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
              variant="ghost"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </EnhancedButton>
          </SheetTrigger>
          <SheetContent side="left">
            <nav className="flex flex-col space-y-3">
              {docsConfig.mainNav.map((link) => (
                <Link
                  key={link.title}
                  href={link.href}
                  className="transition-colors hover:text-foreground/80 text-foreground/60"
                >
                  {link.title}
                </Link>
              ))}
            </nav>
          </SheetContent>
        </Sheet>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <DocsSearch />
          </div>
          <nav className="flex items-center">
            <EnhancedButton variant="ghost" size="icon" asChild>
              <Link
                href={`https://github.com/Ricka7x/next-saas-boilerplate`}
                target="_blank"
              >
                <span className="sr-only">GitHub</span>

                <FaGithub className="h-5 w-5" />
              </Link>
            </EnhancedButton>
          </nav>
        </div>
      </div>
    </header>
  );
}
