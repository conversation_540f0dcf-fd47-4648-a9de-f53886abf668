export default function DocsFooter() {
  return (
    <footer className="border-t py-6 md:py-0">
      <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
        <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
          Built by{' '}
          <a
            href="https://github.com/Ricka7x"
            className="font-medium underline underline-offset-4"
          >
            Ricka7x
          </a>
          . The source code is available on{' '}
          <a
            href="https://github.com/Ricka7x/next-saas-boilerplate"
            className="font-medium underline underline-offset-4"
          >
            GitHub
          </a>
          .
        </p>
        <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
          © 2024 saastarter Inc. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
