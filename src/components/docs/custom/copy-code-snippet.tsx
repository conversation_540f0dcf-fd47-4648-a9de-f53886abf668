'use client';

import { useRef, useState } from 'react';
import { Check, Copy } from 'lucide-react';

type Props = {
  children: React.ReactNode;
};
export default function CopyCodeSnippet({ children }: Props) {
  const [copied, setCopied] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const codeRef = useRef<HTMLDivElement>(null);
  const copyToClipboard = () => {
    if (codeRef.current) {
      const code = codeRef.current.textContent || '';
      navigator.clipboard.writeText(code).then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      });
    }
  };

  return (
    <div
      className="relative bg-gray-800  rounded-sm"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div ref={codeRef}>{children}</div>
      {isHovering && (
        <button
          onClick={copyToClipboard}
          className="absolute right-2 top-2 rounded-md bg-gray-700 p-2 text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
          aria-label={copied ? 'Copied' : 'Copy code to clipboard'}
        >
          {copied ? (
            <Check className="h-3 w-3" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </button>
      )}
    </div>
  );
}
