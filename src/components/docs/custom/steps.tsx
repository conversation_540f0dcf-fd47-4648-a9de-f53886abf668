import React from 'react';

interface StepProps {
  children: React.ReactNode;
}

export function Step({ children }: StepProps) {
  return <>{children}</>;
}

interface StepsProps {
  children: React.ReactNode;
}

export function Steps({ children }: StepsProps) {
  const steps = React.Children.toArray(children).filter(
    (child) => React.isValidElement(child) && child.type === Step
  );

  return (
    <div className="not-prose space-y-4 relative mt-12">
      <div className="absolute left-4 top-8 bottom-8 w-[1px] bg-gray-200 dark:bg-gray-700" />
      {steps.map((step, index) => {
        if (!React.isValidElement(step)) return null;

        const title = step.props.children.find(
          (child: React.ReactNode) =>
            React.isValidElement(child) && child.type === 'h3'
        );
        const content = step.props.children.filter(
          (child: React.ReactNode) =>
            !React.isValidElement(child) || child.type !== 'h3'
        );

        return (
          <div key={index} className="relative pl-10 pb-8">
            <div className="absolute left-0 top-1 flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-gray-50 text-sm font-medium text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
              {index + 1}
            </div>
            <div className="pt-1">
              <div className=" mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                {title}
              </div>
              <div className="text-gray-600 dark:text-gray-400">{content}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default Steps;
