import { AlertTriangle, Info, ShieldAlert } from 'lucide-react';

import { cn } from '@/lib/utils';

type Props = {
  children?: React.ReactNode;
  type?: 'default' | 'warning' | 'danger';
};

export function Callout({ children, type = 'default', ...props }: Props) {
  const icon = {
    default: <Info />,
    warning: <AlertTriangle />,
    danger: <ShieldAlert />,
  }[type];

  return (
    <div
      className={cn(
        'my-6 flex items-start rounded-md border border-l-4 p-4',
        'transition-colors duration-200',
        {
          'border-blue-500 bg-blue-50 dark:border-blue-400 dark:bg-blue-950/50 text-blue-800 dark:text-blue-200':
            type === 'default',
          'border-yellow-500 bg-yellow-50 dark:border-yellow-400 dark:bg-yellow-950/50 text-yellow-800 dark:text-yellow-200':
            type === 'warning',
          'border-red-500 bg-red-50 dark:border-red-400 dark:bg-red-950/50 text-red-800 dark:text-red-200':
            type === 'danger',
        }
      )}
      {...props}
    >
      {icon && (
        <span
          className={cn('mr-4 text-2xl', {
            'text-blue-500 dark:text-blue-400': type === 'default',
            'text-yellow-500 dark:text-yellow-400': type === 'warning',
            'text-red-500 dark:text-red-400': type === 'danger',
          })}
        >
          {icon}
        </span>
      )}
      <div className="not-prose">{children}</div>
    </div>
  );
}
