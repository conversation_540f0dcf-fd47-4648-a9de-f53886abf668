'use client';

import React, { useState } from 'react';

type Props = {
  items: string[];
  children: React.ReactNode;
  defaultIndex?: number;
};

export default function Tabs({ items, children, defaultIndex = 0 }: Props) {
  const [activeTab, setActiveTab] = useState(defaultIndex);

  // Ensure children is always an array
  const childrenArray = React.Children.toArray(children);

  return (
    <div className="not-prose mb-4">
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        {items.map((item, index) => (
          <button
            key={item}
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === index
                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
            onClick={() => setActiveTab(index)}
          >
            {item}
          </button>
        ))}
      </div>
      <div className="mt-4 bg-white dark:bg-gray-800 rounded-b-lg p-4">
        {childrenArray[activeTab]}
      </div>
    </div>
  );
}

export function Tab({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
