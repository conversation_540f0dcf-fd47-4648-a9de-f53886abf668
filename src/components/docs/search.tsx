'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { FileIcon } from 'lucide-react';
import lunr from 'lunr';
import { useDebounce } from 'use-debounce';

import {
  findRelevantExcerpt,
  SearchResultType,
} from '@/lib/docs-utils/search-processor';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function DocsSearch() {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [open, setOpen] = useState(false);
  const [results, setResults] = useState<SearchResultType[]>([]);
  const [debouncedSearch] = useDebounce(query, 300);
  const [searchIndex, setSearchIndex] = useState<lunr.Index | null>(null);
  const [documents, setDocuments] = useState<Record<string, SearchResultType>>(
    {}
  );

  const fetchResults = useCallback(() => {
    if (debouncedSearch && searchIndex) {
      try {
        const searchResults = searchIndex.search(debouncedSearch);
        const formattedResults = searchResults.map((result) => {
          const doc = documents[result.ref];
          return {
            id: result.ref,
            title: doc.title,
            url: doc.url,
            content: doc.content,
            excerpt: findRelevantExcerpt(doc.content, query),
          };
        });
        setResults(formattedResults);
      } catch (error) {
        console.error('Error fetching search results:', error);
        setResults([]);
      }
    } else {
      setResults([]);
    }
  }, [debouncedSearch, searchIndex, documents, query]);

  useEffect(() => {
    async function loadSearchIndex() {
      try {
        const response = await fetch('/search-index.json');
        const { index, docs } = await response.json();
        setSearchIndex(lunr.Index.load(index));
        setDocuments(docs);
      } catch (error) {
        console.error('Error loading search index:', error);
      }
    }
    loadSearchIndex();
  }, []);

  useEffect(() => {
    fetchResults();
  }, [debouncedSearch, fetchResults]);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };
    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setQuery('');
      setResults([]);
    }
  };

  const highlightTerms = (text: string, term: string) => {
    if (!term) return text;
    const regex = new RegExp(`(${term})`, 'gi');
    return text?.replace(regex, '<mark>$1</mark>');
  };

  const handleResultClick = (url: string) => {
    setOpen(false);
    router.push(url);
  };

  return (
    <>
      <EnhancedButton
        onClick={() => setOpen(true)}
        variant="outline"
        className="relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
      >
        <span className="hidden lg:inline-flex">Search documentation...</span>
        <span className="inline-flex lg:hidden">Search...</span>
        <kbd className="pointer-events-none absolute right-1.5 top-2.3 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </EnhancedButton>
      <CommandDialog open={open} onOpenChange={handleOpenChange}>
        <Command className="rounded-lg border shadow-md">
          <CommandInput
            placeholder="Type a command or search..."
            value={query}
            onValueChange={setQuery}
          />
          <CommandList>
            {results.length === 0 && query !== '' ? (
              <CommandEmpty>No results found.</CommandEmpty>
            ) : (
              <CommandGroup heading="Search Results">
                {results.map((result) => (
                  <CommandItem
                    key={result.id}
                    value={result.title}
                    onSelect={() => handleResultClick(result.url)}
                  >
                    <div className="flex flex-col w-full">
                      <div className="flex items-center">
                        <FileIcon className="mr-2 h-4 w-4" />
                        <span className="font-medium">{result.title}</span>
                      </div>
                      {result.excerpt && (
                        <p
                          className="text-xs text-muted-foreground mt-3"
                          dangerouslySetInnerHTML={{
                            __html: highlightTerms(result.excerpt, query),
                          }}
                        />
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </CommandDialog>
    </>
  );
}
