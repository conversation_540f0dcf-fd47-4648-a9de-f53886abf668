import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface NavButtonProps {
  href: string;
  direction: 'left' | 'right';
  label: string;
}

function NavButton({ href, direction, label }: NavButtonProps) {
  const Icon = direction === 'left' ? ChevronLeft : ChevronRight;

  return (
    <Link
      href={href}
      className={`flex items-center gap-2 text-lg font-medium text-gray-600 hover:text-foreground transition-colors ${
        direction === 'right' ? 'flex-row-reverse' : ''
      }`}
    >
      <Icon className="w-5 h-5" />
      <span>{label}</span>
    </Link>
  );
}

interface DocsNavigationProps {
  prevPage?: {
    href: string;
    label: string;
  };
  nextPage?: {
    href: string;
    label: string;
  };
}

export default function DocsNavigationButtons({
  prevPage,
  nextPage,
}: DocsNavigationProps) {
  return (
    <nav className="flex justify-between items-center py-8 px-4 border-t border-gray-200">
      {prevPage ? (
        <NavButton
          href={prevPage.href}
          direction="left"
          label={prevPage.label}
        />
      ) : (
        <div />
      )}
      {nextPage ? (
        <NavButton
          href={nextPage.href}
          direction="right"
          label={nextPage.label}
        />
      ) : (
        <div />
      )}
    </nav>
  );
}
