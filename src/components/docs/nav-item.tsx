'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Collapsible } from '@radix-ui/react-collapsible';
import { ChevronRight } from 'lucide-react';

import { SidebarItem } from '@/lib/docs-utils';
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';

interface NavItemProps {
  item: SidebarItem;
  depth?: number;
}

export function NavItem({ item }: NavItemProps) {
  const pathname = usePathname();
  const [isActive, setIsActive] = React.useState(false);

  React.useEffect(() => {
    setIsActive(pathname === getHref(item));
  }, [pathname, item]);

  const getHref = (item: SidebarItem) => {
    const slugPath = item.slug.join('/');
    if (slugPath === 'index') {
      return '/docs';
    }
    // Remove 'index' from the end of the slug if present
    const cleanedSlug =
      item.slug[item.slug.length - 1] === 'index'
        ? item.slug.slice(0, -1)
        : item.slug;
    return `/docs/${cleanedSlug.join('/')}`;
  };

  if (item.type === 'file') {
    return (
      <div className="px-2">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive}>
              <Link href={getHref(item)}>{item.displayName}</Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </div>
    );
  }

  return (
    <Collapsible
      key={item.displayName}
      title={item.displayName}
      defaultOpen
      className="group/collapsible"
    >
      <SidebarGroup>
        <SidebarGroupLabel
          asChild
          className="group/label text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
        >
          <CollapsibleTrigger>
            {item.displayName}{' '}
            <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
          </CollapsibleTrigger>
        </SidebarGroupLabel>
        <CollapsibleContent>
          <SidebarGroupContent>
            <SidebarMenu>
              {item?.children?.map((item) => {
                const isActive = getHref(item) === pathname;
                return (
                  <SidebarMenuItem key={item.displayName}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <Link href={getHref(item)}>{item.displayName}</Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </CollapsibleContent>
      </SidebarGroup>
    </Collapsible>
  );
}
