import { FaDocker, FaReact, FaShieldAlt, FaStripeS } from 'react-icons/fa';
import { SiMdx, SiPrisma, SiTailwindcss } from 'react-icons/si';
import { TbBrandNextjs, TbLetterCaseToggle } from 'react-icons/tb';

import { Card, CardContent, CardHeader } from '../ui/card';

const featuresData = [
  {
    title: 'Next.js 14 Foundation',
    description:
      'Built on the latest Next.js for optimal performance, SEO, and developer experience.',
    icon: TbBrandNextjs,
  },
  {
    title: 'React Done Right',
    description:
      'Modern React patterns and hooks with TypeScript for type-safe, maintainable code.',
    icon: FaReact,
  },
  {
    title: 'Containerized Setup',
    description:
      'Production-ready Docker configuration for consistent deployment anywhere.',
    icon: FaDocker,
  },
  {
    title: 'Database & ORM',
    description:
      'Type-safe database operations with Prisma, migrations, and seeding included.',
    icon: SiPrisma,
  },
  {
    title: 'Authentication Ready',
    description:
      'Complete auth system with social logins, magic links, and role management.',
    icon: FaShieldAlt,
  },
  {
    title: 'Payments & Billing',
    description:
      'Stripe integration with subscription management and usage-based billing.',
    icon: FaStripeS,
  },
  {
    title: 'Beautiful UI',
    description:
      'Polished components built with Tailwind CSS and Radix UI primitives.',
    icon: SiTailwindcss,
  },
  {
    title: 'Content Management',
    description:
      'Built-in MDX support for documentation, blog posts, and marketing pages.',
    icon: SiMdx,
  },
  {
    title: 'Blog Engine',
    description:
      'Full-featured blog with editor, comments, and content management.',
    icon: TbLetterCaseToggle,
  },
];

export default function FeaturesSection() {
  return (
    <section
      id="features"
      className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-900"
    >
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">
          Everything You Need to Build a SaaS
        </h2>
        <div className="grid gap-10 sm:grid-cols-2 md:grid-cols-3 max-w-6xl mx-auto">
          {featuresData.map((feature, index) => (
            <Card key={index} className="flex flex-col relative">
              <CardHeader>
                <CardContent className="flex flex-col">
                  <feature.icon className="h-10 w-10 text-gray-500 dark:text-gray-400" />
                  <h4 className="font-semibold mt-2">{feature.title}</h4>
                  <p className="text-gray-500 dark:text-gray-400 mt-2">
                    {feature.description}
                  </p>
                </CardContent>
              </CardHeader>
            </Card>
          ))}

          {/* <Card>
            <CardHeader>
              <CardTitle>Seamless Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Easily integrate with your existing tools and workflows.</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Real-time Collaboration</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Work together with your team in real-time, from anywhere.</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Advanced Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Gain insights with powerful analytics and reporting tools.</p>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </section>
  );
}

// <div className="bg-white p-10">
//   <div className="max-w-6xl mx-auto">
//     <h2 className="text-6xl font-bold tracking-tighter sm:text-7xl text-center">
//       Features
//     </h2>
//     <p className="text-center mb-12">
//       This project is an experiment to see how a modern app, with features
//       like auth, subscriptions, API routes, and static pages would work in
//       Next.js 13 app dir.
//     </p>
//     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <TbBrandNextjs className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Next.js 14</h3>
//         <p className="text-center">
//           App dir, Routing, Layouts, Loading UI and API routes.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <FaReact className="mb-4 w-12 h-12" />

//         <h3 className="font-semibold mb-2">React</h3>
//         <p className="text-center">
//           Server and Client Components, Hooks, Context API, and more.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <FaDocker className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Docker</h3>
//         <p className="text-center">
//           Containerization of services for easy deployment and scaling.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <SiPrisma className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Prisma</h3>
//         <p className="text-center">
//           ORM using Prisma and running on a local database through Docker.
//         </p>
//       </div>

//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <FaShieldAlt className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Authentication (Auth.js)</h3>
//         <p className="text-center">
//           Authentication using NextAuth.js through Oauth providers and magic
//           links.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <FaStripeS className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Subscriptions (Stripe)</h3>
//         <p className="text-center">
//           Free and paid subscriptions using Stripe.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <SiTailwindcss className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">Styling</h3>
//         <p className="text-center">
//           Tailwind CSS, CSS variables and dark mode.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         <SiTypescript className="mb-4 w-12 h-12" />
//         <h3 className="font-semibold mb-2">TypeScript</h3>
//         <p className="text-center">
//           Typesafe and easy to use with React and Next.js.
//         </p>
//       </div>
//       <div className="flex flex-col items-center p-6 border rounded-lg">
//         {/* <SiTypescript className="mb-4 w-12 h-12" /> */}
//         <p className="text-2xl h-12 font-bold">Shadcn/ui</p>
//         <h3 className="font-semibold mb-2">Component UI</h3>
//         <p className="text-center">We use Shadcn/ui for our components.</p>
//       </div>
//     </div>
//   </div>
// </div>
