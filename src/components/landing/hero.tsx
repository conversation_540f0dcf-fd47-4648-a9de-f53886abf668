import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function HeroSection() {
  const t = useTranslations('Landing');
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48">
      <div className="container px-4 md:px-6 ">
        <div className="flex flex-col items-center space-y-4 text-center max-w-4xl mx-auto">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-7xl">
              Launch Your SaaS Faster Than Ever
            </h1>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
              Skip the boilerplate. Focus on what makes your SaaS unique.
              Everything else is already built for you.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <EnhancedButton asChild size="lg" variant="default">
              <Link href="/signin">{t('hero.button')}</Link>
            </EnhancedButton>
          </div>
        </div>
      </div>
    </section>
  );
}
