'use client';

import { useState } from 'react';
import { CheckCircle } from 'lucide-react';

import { Badge } from '../ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Switch } from '../ui/switch';

interface PricingSectionProps {
  plans: {
    id: string;
    name: string;
    description: string;
    features: string[];
    monthlyPrice: number;
    yearlyPrice: number;
    isPopular: boolean;
  }[];
}

export function PricingSection({ plans }: PricingSectionProps) {
  const [isYearly, setIsYearly] = useState(false);

  const sortedPlans = [...plans].sort((a, b) => {
    const priceA = isYearly ? a.yearlyPrice : a.monthlyPrice;
    const priceB = isYearly ? b.yearlyPrice : b.monthlyPrice;
    return priceA - priceB;
  });

  return (
    <section id="pricing" className="w-full py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">
          Choose Your Perfect Plan
        </h2>

        <div className="flex items-center justify-center my-8">
          <span className={`mr-2 ${!isYearly ? 'font-bold' : ''}`}>
            Monthly
          </span>
          <Switch checked={isYearly} onCheckedChange={setIsYearly} />
          <span className={`ml-2 ${isYearly ? 'font-bold' : ''}`}>Yearly</span>
          <span className="ml-2 text-sm bg-muted px-2 py-1 rounded">
            Get 2 free months if paid yearly
          </span>
        </div>

        <div
          className={`grid gap-6 sm:grid-cols-2 lg:grid-cols-${sortedPlans.length} max-w-6xl mx-auto`}
        >
          {sortedPlans.map((plan) => (
            <Card key={plan.id} className={`flex flex-col relative `}>
              <CardHeader>
                <CardTitle>{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                {plan.isPopular && (
                  <Badge className="absolute top-4 right-4">Popular</Badge>
                )}
              </CardHeader>
              <CardContent className="flex-1">
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold">
                    ${isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                  </span>
                  <span className="ml-1 text-sm text-muted-foreground">
                    /{isYearly ? ' year' : ' month'}
                  </span>
                </div>

                <ul className="mt-6 space-y-3">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm">
                      <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
