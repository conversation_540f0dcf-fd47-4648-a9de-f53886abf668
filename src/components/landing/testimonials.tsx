import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { <PERSON>, CardContent, CardHeader } from '../ui/card';

const testimonialsData = [
  {
    name: '<PERSON>',
    title: 'Founder, TechFlow',
    testimonial:
      'This starter saved us months of development time. We launched our MVP in weeks instead of months.',
    avatar: '/placeholder.svg?height=40&width=40',
  },
  {
    name: '<PERSON>',
    title: 'CTO, DataPulse',
    testimonial:
      'The authentication and payment systems worked flawlessly out of the box. No more wrestling with Stripe integration.',
    avatar: '/placeholder.svg?height=40&width=40',
  },
  {
    name: '<PERSON>',
    title: 'Lead Developer, CloudStack',
    testimonial:
      'The code quality and architecture are top-notch. It was easy to customize and extend for our specific needs.',
    avatar: '/placeholder.svg?height=40&width=40',
  },
];

export default function TestimonialSection() {
  return (
    <>
      <section
        id="testimonials"
        className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-900"
      >
        <div className="container px-4 md:px-6">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">
            Trusted by SaaS Founders
          </h2>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
            {testimonialsData.map((testimonial, index) => (
              <Card key={index} className="flex flex-col relative">
                <CardHeader>
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10">
                      <AvatarImage
                        src="/placeholder.svg?height=40&width=40"
                        alt="Avatar"
                      />
                      <AvatarFallback>RJ</AvatarFallback>
                    </Avatar>
                    <div className="ml-4">
                      <p className="text-md font-semibold">
                        {testimonial.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {testimonial.title}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 dark:text-gray-400">
                    {testimonial.testimonial}
                  </p>
                </CardContent>
              </Card>
            ))}

            {/* <Card>
              <CardHeader>
                <CardTitle>Amazing Product!</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 dark:text-gray-400">
                  This SaaS has completely transformed our workflow. Highly
                  recommended!
                </p>
                <div className="mt-4 flex items-center">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src="/placeholder.svg?height=40&width=40"
                      alt="Avatar"
                    />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div className="ml-4">
                    <p className="text-sm font-medium">John Doe</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      CEO, TechCorp
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Boosted Our Productivity</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 dark:text-gray-400">
                  Weve seen a 50% increase in team productivity since adopting
                  this solution.
                </p>
                <div className="mt-4 flex items-center">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src="/placeholder.svg?height=40&width=40"
                      alt="Avatar"
                    />
                    <AvatarFallback>JS</AvatarFallback>
                  </Avatar>
                  <div className="ml-4">
                    <p className="text-sm font-medium">Jane Smith</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      CTO, InnovateCo
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Excellent Support</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 dark:text-gray-400">
                  The customer support team is always there when we need them. 5
                  stars!
                </p>
                <div className="mt-4 flex items-center">
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src="/placeholder.svg?height=40&width=40"
                      alt="Avatar"
                    />
                    <AvatarFallback>RJ</AvatarFallback>
                  </Avatar>
                  <div className="ml-4">
                    <p className="text-sm font-medium">Robert Johnson</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Founder, StartupX
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card> */}
          </div>
        </div>
      </section>
    </>
  );
}
