'use client';

import { clearAllNotifications } from '@/actions/notifications/clear-all-notifications.action';
import { deleteNotification } from '@/actions/notifications/delete-notification.action';
import { toast } from 'sonner';

import { Notification } from '@/types/notification.types';
import { ScrollArea } from '@/components/ui/scroll-area';

import NotificationSidebar from './sidebar';

type NotificationSidebarWrapperProps = {
  notifications: Notification[];
  currentNotificationId?: string;
  updateNotifications: (updatedNotifications: Notification[]) => void;
};

export default function NotificationSidebarWrapper({
  notifications,
  updateNotifications,
}: NotificationSidebarWrapperProps) {
  const handleDeleteNotification = async (id: string) => {
    try {
      await deleteNotification(id);

      const updatedNotifications = notifications.filter(
        (notification) => notification.id !== id
      );
      updateNotifications(updatedNotifications);
      toast.success('Notification deleted successfully');
    } catch (error) {
      toast.error((error as Error).message);
    }
  };

  const handleClearAllNotifications = async () => {
    try {
      await clearAllNotifications();

      updateNotifications([]);

      toast.success('All notifications cleared successfully');
    } catch (error) {
      toast.error((error as Error).message);
    }
  };

  return (
    <ScrollArea className="h-[calc(100vh-4rem)]">
      <NotificationSidebar
        notifications={notifications}
        onDeleteNotification={handleDeleteNotification}
        onClearAllNotifications={handleClearAllNotifications}
      />
    </ScrollArea>
  );
}
