import Link from 'next/link';
import { markAsReadNotification } from '@/actions/notifications/mark-as-read-notification.action';

import { Notification } from '@/types/notification.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { NOTIFICATION_TYPES } from '@/lib/get-notification-content';
import { formatDate, getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default async function NotificationDetail({
  notification,
}: {
  notification: Notification;
}) {
  await markAsReadNotification(notification.id);
  const fromUser =
    notification.from.username ||
    notification.from.name ||
    notification.from.email ||
    '';

  return (
    <div className="space-y-6">
      {notification && (
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage
                src={
                  notification.from.image ||
                  getDeterministicAvatar(notification.from.id)
                }
                alt={fromUser}
              />
              <AvatarFallback>{getInitials(fromUser)}</AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-xl font-semibold">{fromUser}</h3>
              <p className="text-sm text-muted-foreground">
                {formatDate(notification.createdAt)}
              </p>
            </div>
          </div>

          <div>
            <p className="text-muted-foreground">
              <span className="font-bold">@{fromUser}</span>{' '}
              {NOTIFICATION_TYPES[notification.type]}{' '}
              {notification.type !== 'SUBSCRIPTION_PURCHASE' && (
                <Link
                  href={`/posts/${notification.post?.slug}`}
                  className="underline"
                >
                  {notification.post?.title?.toLowerCase()}
                </Link>
              )}
            </p>
            {notification.type !== 'LIKE_COMMENT' &&
              notification.type !== 'LIKE_POST' && (
                <div className="mt-4">
                  <p className="text-sm">{notification.content}</p>
                </div>
              )}
          </div>
        </div>
      )}
    </div>
  );
}
