'use client';

import { useState } from 'react';
import { deleteNotification } from '@/actions/notifications/delete-notification.action';
import { markAsReadNotification } from '@/actions/notifications/mark-as-read-notification.action';
import { NotificationType } from '@prisma/client';
import { Bell, Check, MoreVertical, Trash } from 'lucide-react';
import { toast } from 'sonner';

import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { formatDate, getInitials } from '@/lib/utils';
import { useSSE } from '@/hooks/use-sse';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function NotificationButton() {
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    unreadCount,
    markNotificationsAsRead,
    removeNotification,
  } = useSSE();

  const handleNotificationClick = async (notificationId: string) => {
    try {
      await markAsReadNotification(notificationId);
      markNotificationsAsRead([notificationId]);
      setIsOpen(false);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await deleteNotification(notificationId);
      removeNotification(notificationId);
      toast.success('Notification deleted');
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Failed to delete notification');
    }
  };

  const handleClearNotifications = () => {
    markNotificationsAsRead(
      notifications.filter((n) => !n.isRead).map((n) => n.id)
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <EnhancedButton
          size="icon"
          variant="outline"
          className="relative"
          aria-label="Notifications"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 min-w-5 rounded-full bg-red-500 text-xs text-white flex items-center justify-center p-1">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </EnhancedButton>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="grid gap-4">
          <div className="flex justify-between items-center">
            <h3 className="font-medium leading-none">Notifications</h3>
            <EnhancedButton
              variant="ghost"
              size="sm"
              onClick={handleClearNotifications}
              disabled={unreadCount === 0}
            >
              Clear All
            </EnhancedButton>
          </div>
          <ScrollArea className="h-[300px] overflow-y-auto">
            <div className="grid gap-2">
              {unreadCount === 0 && (
                <div className="flex flex-col items-center justify-center h-full text-center p-4">
                  <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No notifications yet
                  </p>
                </div>
              )}
              {notifications
                .filter((n) => !n.isRead)
                .map((notification) => {
                  const fromUser =
                    notification.from.username ||
                    notification.from.name ||
                    notification.from.email ||
                    '';

                  return (
                    <div
                      key={notification.id}
                      className="flex items-start gap-4 hover:bg-muted p-2 rounded group"
                    >
                      {notification.type === NotificationType.ANNOUNCEMENT ? (
                        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <Bell className="h-5 w-5 text-primary" />
                        </div>
                      ) : (
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={
                              notification.from.image ||
                              getDeterministicAvatar(notification.from.id)
                            }
                            alt={fromUser}
                          />
                          <AvatarFallback>
                            {getInitials(fromUser)}
                          </AvatarFallback>
                        </Avatar>
                      )}
                      <div
                        className="grid gap-1 flex-1 cursor-pointer"
                        onClick={() => handleNotificationClick(notification.id)}
                      >
                        <p className="text-sm font-medium leading-none">
                          {notification.type === NotificationType.ANNOUNCEMENT
                            ? 'System'
                            : fromUser}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(notification?.createdAt)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {NotificationType[notification.type]}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <EnhancedButton
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100"
                          >
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">More</span>
                          </EnhancedButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleNotificationClick(notification.id);
                            }}
                          >
                            <Check className="mr-2 h-4 w-4" /> Mark as read
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteNotification(notification.id);
                            }}
                          >
                            <Trash className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  );
                })}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
}
