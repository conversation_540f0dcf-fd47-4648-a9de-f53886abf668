'use client';

import {
  UpdateNotificationSettingsSchema,
  UpdateNotificationSettingsValues,
} from '@/schemas/settings.schemas';
import { useSettingsStore } from '@/stores/settings-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export function NotificationsForm() {
  const { settings, updateSettings } = useSettingsStore();

  const form = useForm<UpdateNotificationSettingsValues>({
    resolver: zodResolver(UpdateNotificationSettingsSchema),
    defaultValues: settings || {
      blogNotifications: true,
      subscriptionNotifications: false,
      marketingNotifications: false,
      marketingEmails: true,
      securityEmails: true,
    },
  });

  async function onSubmit(data: UpdateNotificationSettingsValues) {
    try {
      await updateSettings(data);
      toast.success('Settings updated successfully');
    } catch (error) {
      toast.error('Error', {
        description: `There was an error updating your settings. ${(error as Error).message}`,
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div>
          <h3 className="mb-4 text-lg font-medium">Notifications</h3>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="blogNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Blog notifications
                    </FormLabel>
                    <FormDescription>
                      Receive notifications when someone comments, likes or
                      replies to your posts
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="subscriptionNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Subscription notifications
                    </FormLabel>
                    <FormDescription>
                      Receive notifications when someone subscribes to one of
                      your plans or products
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="marketingNotifications"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Marketing notifications
                    </FormLabel>
                    <FormDescription>
                      Receive notifications when someone subscribes to your
                      marketing emails, newsletters or promos
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div>
          <h3 className="mb-4 text-lg font-medium">Emails</h3>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="marketingEmails"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Marketing emails
                    </FormLabel>
                    <FormDescription>
                      Receive emails about new products, features, and more.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="securityEmails"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Security emails</FormLabel>
                    <FormDescription>
                      Receive emails about your account activity and security.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <EnhancedButton type="submit">Update notifications</EnhancedButton>
      </form>
    </Form>
  );
}
