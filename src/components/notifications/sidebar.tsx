'use client';

import Link from 'next/link';
import { MoreVertical, Trash } from 'lucide-react';

import { Notification } from '@/types/notification.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { NOTIFICATION_TYPES } from '@/lib/get-notification-content';
import { formatDate, getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type NotificationSidebarProps = {
  notifications: Notification[];
  onDeleteNotification: (id: string) => void;
  onClearAllNotifications: () => void;
};

export default function NotificationSidebar({
  notifications,
  onDeleteNotification,
  onClearAllNotifications,
}: NotificationSidebarProps) {
  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <p className="text-sm text-muted-foreground">
          Items will appear here once available
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center p-4">
        <h2 className="text-lg font-semibold">Notifications</h2>
        <EnhancedButton
          onClick={onClearAllNotifications}
          variant="ghost"
          size="sm"
        >
          Clear All
        </EnhancedButton>
      </div>
      {notifications.map((notification) => {
        const fromUser =
          notification.from.username ||
          notification.from.name ||
          notification.from.email ||
          '';
        return (
          <div
            key={notification.id}
            className="flex items-start space-x-4 p-4 hover:bg-accent"
          >
            <Link
              href={`/notifications/${notification.id}`}
              className="flex items-start space-x-4 flex-1"
            >
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={
                    notification.from.image ||
                    getDeterministicAvatar(notification.from.id)
                  }
                  alt={fromUser}
                />
                <AvatarFallback>{getInitials(fromUser)}</AvatarFallback>
              </Avatar>
              <div className="grid gap-1 flex-1">
                <p className="text-sm font-medium leading-none">{fromUser}</p>
                <p className="text-xs text-muted-foreground">
                  {formatDate(notification.createdAt)}
                </p>
                <p className="text-sm text-muted-foreground">
                  {NOTIFICATION_TYPES[notification.type]}
                </p>
              </div>
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </EnhancedButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDeleteNotification(notification.id)}
                >
                  <Trash className="mr-2 h-4 w-4" /> Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      })}
    </>
  );
}
