'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, Trash2 } from 'lucide-react';

import { ApiK<PERSON> } from '@/types/api-keys.types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface ApiKeyActionsProps {
  apiKey: Partial<ApiKey>;
  onRegenerateClick: (apiKey: Partial<ApiKey>) => Promise<void>;
  onDeleteClick: (apiKey: Partial<ApiKey>) => void;
}

export function ApiKeyActions({
  apiKey,
  onRegenerateClick,
  onDeleteClick,
}: ApiKeyActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </EnhancedButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onRegenerateClick(apiKey)}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Regenerate
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onDeleteClick(apiKey)}>
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
