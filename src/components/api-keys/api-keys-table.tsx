'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import {
  createApi<PERSON><PERSON>,
  deleteApi<PERSON><PERSON>,
  getApi<PERSON><PERSON><PERSON>,
  regenerateApi<PERSON><PERSON>,
} from '@/actions/api-keys';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';

import { ApiKey } from '@/types/api-keys.types';
import { GetPaginatedResult } from '@/types/shared.types';
import { calculateNewPage } from '@/lib/pagination';
import { toastPromise } from '@/lib/toast-promise';
import { useTable } from '@/hooks/use-table';
import { Modal } from '@/components/ui/modal';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ApiKeyActions } from '@/components/api-keys/api-key-actions';
import { CreateApiKeyForm } from '@/components/api-keys/create-api-key-form';
import { NewApiKeyModal } from '@/components/api-keys/new-api-key-modal';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';

type ApiKeysTableProps = {
  initialData: GetPaginatedResult<ApiKey>;
};

export default function ApiKeysTable({ initialData }: ApiKeysTableProps) {
  const isInitialMount = useRef(true);
  const queryClient = useQueryClient();
  const {
    data: apiKeys,
    setData: setApiKeys,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sortBy,
    onSortChange,
    sorting,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<ApiKey>({ initialData });

  const [isPending, startTransition] = useTransition();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isNewKeyModalOpen, setIsNewKeyModalOpen] = useState(false);

  const [newApiKey, setNewApiKey] = useState('');

  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [keyToDelete, setKeyToDelete] = useState<Partial<ApiKey> | null>(null);

  const columns = useMemo<ColumnDef<Partial<ApiKey>>[]>(
    () => [
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'key',
        header: 'API Key',
        cell: ({ row }) => (
          <code className="bg-muted px-2 py-1 rounded">{row.original.key}</code>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: 'Created',
        cell: ({ row }) => (
          <span>{new Date(row.original.createdAt!).toLocaleDateString()}</span>
        ),
      },

      {
        accessorKey: 'updatedAt',
        header: 'Updated',
        cell: ({ row }) => (
          <span>{new Date(row.original.updatedAt!).toLocaleDateString()}</span>
        ),
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <ApiKeyActions
            apiKey={row.original}
            onRegenerateClick={handleRegenerateKey}
            onDeleteClick={(apiKey) => {
              setKeyToDelete(apiKey);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: apiKeys,
    columns,
    state: {
      sorting,
    },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['apiKeys', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getApiKeys(defaultParams);
    },
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const handlePageChange = async (newPage: number) => {
    setPage(newPage);
  };

  const handleSearchChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  const handleCreateKey = async (name: string) => {
    startTransition(async () => {
      try {
        const result = await createApiKey(name);
        const newTotalItems = totalItems + 1;
        // Calculate the new page based on the total items and items per page
        const newPage = calculateNewPage(
          page,
          newTotalItems,
          itemsPerPage,
          true
        );
        setPage(newPage);
        setTotalItems(newTotalItems);
        queryClient.invalidateQueries({ queryKey: ['apiKeys'] });
        await refetch();
        setIsCreateModalOpen(false);
        setNewApiKey(result.key);
        setIsNewKeyModalOpen(true);
      } catch (error) {
        toast.error('Failed to create API key', {
          description: `Failed to create API key. Please try again. ${
            (error as Error).message
          }`,
        });
      }
    });
  };

  const handleDeleteKey = async () => {
    if (!keyToDelete) return;

    startTransition(async () => {
      try {
        await deleteApiKey(keyToDelete.id!);
        setSearchTerm('');
        const newPage = calculateNewPage(page, totalItems - 1, itemsPerPage);
        setTotalItems(totalItems - 1);
        setPage(newPage);
        queryClient.invalidateQueries({ queryKey: ['apiKeys'] });
        await refetch();
        toast.success('API key deleted successfully');
      } catch (error) {
        toast.error('Failed to delete API key', {
          description: `Failed to delete API key. Please try again. ${
            (error as Error).message
          }`,
        });
      } finally {
        setIsDeleteConfirmOpen(false);
        setKeyToDelete(null);
      }
    });
  };

  const handleRegenerateKey = async (apiKey: Partial<ApiKey>) => {
    return toastPromise(
      regenerateApiKey(apiKey.id!).then((result) => {
        queryClient.invalidateQueries({ queryKey: ['apiKeys'] });
        refetch();
        setNewApiKey(result.key);
        setIsNewKeyModalOpen(true);
      }),
      {
        loading: 'Regenerating API key...',
        success: 'API key regenerated successfully',
        error: 'Failed to regenerate API key',
      }
    );
  };

  useEffect(() => {
    if (result) {
      setApiKeys(result.items);
      setTotalItems(result.totalCount);

      // Only update page on initial mount if it differs
      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);

  return (
    <>
      {isEmpty && table.getRowModel().rows?.length === 0 ? (
        <EmptyState
          title="No API Keys"
          description="You haven't created any API keys yet. Create one to get started."
          actionLabel="Create API Key"
          onAction={() => setIsCreateModalOpen(true)}
        />
      ) : (
        <>
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter API keys..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            <EnhancedButton onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New API Key
            </EnhancedButton>
          </div>

          <div className="relative">
            {isLoading && <BluredBgSpinner />}
            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() &&
                              header.column.getIsSorted() && (
                                <span className="ml-2">
                                  {{
                                    asc: '↑',
                                    desc: '↓',
                                  }[header.column.getIsSorted() as string] ??
                                    ''}
                                </span>
                              )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No API keys found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      )}

      <Modal
        title="Create New API Key"
        description="Enter a name for your new API key. You'll be able to copy the key after creation."
        isOpen={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="create-api-key-form"
            isLoading={isPending}
            loadingText="Creating..."
          >
            Create Key
          </EnhancedButton>
        }
      >
        <CreateApiKeyForm onSubmit={handleCreateKey} />
      </Modal>

      <NewApiKeyModal
        isOpen={isNewKeyModalOpen}
        onOpenChange={setIsNewKeyModalOpen}
        apiKey={newApiKey}
        onClose={() => setIsNewKeyModalOpen(false)}
      />

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        isLoading={isPending}
        loadingText="Deleting..."
        onConfirm={handleDeleteKey}
        title="Confirm Delete API Key"
        description="Are you sure you want to delete this API key? This action cannot be undone."
      />
    </>
  );
}
