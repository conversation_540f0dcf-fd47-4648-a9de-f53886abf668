'use client';

import { Copy } from 'lucide-react';
import { toast } from 'sonner';

import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type NewApiKeyModalProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: string;
  onClose?: () => void;
};

export function NewApiKeyModal({
  isOpen,
  onOpenChange,
  apiKey,
  onClose = () => onOpenChange(false),
}: NewApiKeyModalProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey).then(() => {
      toast.success('API key copied to clipboard');
    });
  };

  return (
    <Modal
      title="New API Key Created"
      description="Make sure to copy your API key now. You won't be able to see it again!"
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      footer={<EnhancedButton onClick={onClose}>Close</EnhancedButton>}
    >
      <div className="flex items-center space-x-2">
        <Input value={apiKey} readOnly className="font-mono" />
        <EnhancedButton onClick={handleCopy} variant="outline">
          <Copy className="h-4 w-4" />
        </EnhancedButton>
      </div>
    </Modal>
  );
}
