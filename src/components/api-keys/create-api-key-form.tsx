'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateApiKeySchema, type CreateApiKeyValues } from '@/schemas/api-keys.schemas';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

interface CreateApiKeyFormProps {
  onSubmit: (name: string) => Promise<void>;
}

export function CreateApiKeyForm({ onSubmit }: CreateApiKeyFormProps) {
  const form = useForm<CreateApiKeyValues>({
    resolver: zodResolver(CreateApiKeySchema),
    defaultValues: {
      name: '',
    },
  });

  const handleSubmit = async (values: CreateApiKeyValues) => {
    await onSubmit(values.name);
    form.reset();
  };

  return (
    <Form {...form}>
      <form
        id="create-api-key-form"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="grid gap-4 py-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="My API Key" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}