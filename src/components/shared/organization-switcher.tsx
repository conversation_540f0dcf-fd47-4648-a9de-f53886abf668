'use client';

import React, { useState, useTransition } from 'react';
import Image from 'next/image';
import { createTenant } from '@/actions/tenant/create-tenant.action';
import { switchTenant } from '@/actions/tenant/switch-tenant';
import { type CreateTenantValues } from '@/schemas/tenant.schemas';
import { useTenantStore } from '@/stores/tenant-store';
import { useQueryClient } from '@tanstack/react-query';
import { ChevronsUpDown, Plus } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

import { Tenant } from '@/types/tenant.types';
import { toastPromise } from '@/lib/toast-promise';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Modal } from '@/components/ui/modal';
import { SidebarMenuButton } from '@/components/ui/sidebar';
import { OrgForm } from '@/components/organizations/organization-form';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const defaultLogo = '/default-logo.svg';
interface OrganizationSwitcherProps {
  initialTenant: Tenant;
  tenants: Tenant[];
  currentPlan: string;
}

export function OrganizationSwitcher({
  initialTenant,
  tenants,
  currentPlan,
}: OrganizationSwitcherProps) {
  const queryClient = useQueryClient();
  const { data: session, update: updateSession } = useSession();
  const [isPending, startTransition] = useTransition();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const {
    currentTenant,
    setCurrentTenant,
    setAvailableTenants,
    availableTenants,
  } = useTenantStore();

  const handleTenantSelect = async (tenant: Tenant) => {
    if (tenant.id === initialTenant?.id) return;

    toastPromise(
      switchTenant(tenant.id).then(async () => {
        setCurrentTenant(tenant);
        updateSession({
          ...session,
          user: {
            ...session?.user,
            currentTenantId: tenant.id,
          },
        });

        queryClient.removeQueries({ queryKey: ['users'] });
        queryClient.removeQueries({ queryKey: ['invites'] });
        queryClient.removeQueries({ queryKey: ['organization-members'] });

        // Force a refetch of the data
        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['users'] }),
          queryClient.invalidateQueries({ queryKey: ['invites'] }),
          queryClient.invalidateQueries({ queryKey: ['organization-members'] }),
        ]);
      }),
      {
        loading: 'Switching organization...',
        success: 'Organization switched successfully',
        error: 'Failed to switch organization',
      }
    );
  };

  const handleCreateOrganization = async (data: CreateTenantValues) => {
    startTransition(async () => {
      try {
        const newTenant = await createTenant(data);
        await switchTenant(newTenant.id);

        // Update store
        setAvailableTenants([...tenants, newTenant]);
        setCurrentTenant(newTenant);
        updateSession({
          ...session,
          user: {
            ...session?.user,
            currentTenantId: newTenant.id,
          },
        });
        setIsCreateModalOpen(false);

        // Remove existing data from cache before invalidating
        queryClient.removeQueries({ queryKey: ['users'] });
        queryClient.removeQueries({ queryKey: ['invites'] });
        queryClient.removeQueries({ queryKey: ['organization-members'] });

        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['users'] }),
          queryClient.invalidateQueries({ queryKey: ['invites'] }),
          queryClient.invalidateQueries({ queryKey: ['organization-members'] }),
        ]);

        toast.success('Organization created successfully');
      } catch (error) {
        toast.error(
          error instanceof Error
            ? error.message
            : 'Failed to create organization'
        );
      }
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            size="lg"
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            disabled={isPending}
          >
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Image
                src={currentTenant?.logo || defaultLogo}
                width={32}
                height={32}
                alt={currentTenant?.name || 'Organization Logo'}
                className="h-full w-full rounded-lg object-cover"
              />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">
                {currentTenant?.name}
              </span>
              <span className="truncate text-xs">{currentPlan}</span>
            </div>
            <ChevronsUpDown className="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          side="bottom"
          sideOffset={4}
        >
          <DropdownMenuLabel className="text-xs text-muted-foreground">
            Teams
          </DropdownMenuLabel>
          {availableTenants?.map((tenant, index) => (
            <DropdownMenuItem
              key={tenant.id}
              onClick={() => handleTenantSelect(tenant)}
              className="gap-2 p-2"
            >
              <div className="flex size-6 items-center justify-center rounded-sm border">
                <Image
                  src={tenant.logo || defaultLogo}
                  width={24}
                  height={24}
                  alt={tenant.name}
                  className="h-full w-full rounded-sm object-cover"
                />
              </div>
              {tenant.name}
              <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="gap-2 p-2"
            onClick={() => setIsCreateModalOpen(true)}
          >
            <div className="flex size-6 items-center justify-center rounded-md border bg-background">
              <Plus className="size-4" />
            </div>
            <div className="font-medium text-muted-foreground">
              Add Organization
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Modal
        title="Create Organization"
        description="Set up your organization's profile and details"
        isOpen={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="org-form"
            isLoading={isPending}
            loadingText="Creating..."
          >
            Create Organization
          </EnhancedButton>
        }
      >
        <OrgForm onSubmit={handleCreateOrganization} />
      </Modal>
    </>
  );
}
