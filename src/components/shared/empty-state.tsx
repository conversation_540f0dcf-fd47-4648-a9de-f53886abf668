import { PackageIcon } from 'lucide-react';

import { EnhancedButton } from '@/components/shared/enhanced-button';

interface EmptyStateProps {
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  icon?: React.ReactNode;
}

export default function EmptyState({
  title = 'No items found',
  description = 'There are no items in this list. Try adding some!',
  actionLabel,
  onAction,
  icon = <PackageIcon className="text-muted-foreground w-12 h-12 mb-4" />,
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center h-[400px] bg-muted/40 rounded-lg border border-dashed border-muted-foreground/25 p-8 text-center">
      {icon}
      <h3 className="text-lg font-semibold mt-4 mb-2">{title}</h3>
      <p className="text-sm text-muted-foreground mb-4 max-w-sm">
        {description}
      </p>
      {actionLabel && onAction && (
        <EnhancedButton onClick={onAction}>{actionLabel}</EnhancedButton>
      )}
    </div>
  );
}
