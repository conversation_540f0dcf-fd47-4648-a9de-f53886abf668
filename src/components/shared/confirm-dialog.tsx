import { Modal } from '@/components/ui/modal';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface ConfirmDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void> | void;
  onCancel?: () => void;
  title: string;
  description?: string | React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  loadingText?: string;
}

export function ConfirmDialog({
  isOpen,
  onOpenChange,
  onCancel = () => onOpenChange(false),
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  loadingText = 'Processing...',
  isLoading = false,
}: ConfirmDialogProps) {
  return (
    <Modal
      title={title}
      description={description}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      footer={
        <div className="flex justify-end gap-3">
          <EnhancedButton
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            {cancelText}
          </EnhancedButton>
          <EnhancedButton
            onClick={onConfirm}
            isLoading={isLoading}
            loadingText={loadingText}
          >
            {confirmText}
          </EnhancedButton>
        </div>
      }
    ></Modal>
  );
}
