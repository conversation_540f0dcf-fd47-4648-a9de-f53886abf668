'use client';

import { ComponentPropsWithoutRef } from 'react';
import { Search } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';

type Props = ComponentPropsWithoutRef<'input'> & {
  className: string;
};

export default function SearchBar({ className, ...rest }: Props) {
  return (
    <div className="relative">
      <Search
        className={cn(
          'absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground',
          { className }
        )}
      />
      <Input {...rest} type="search" className="pl-8" />
    </div>
  );
}
