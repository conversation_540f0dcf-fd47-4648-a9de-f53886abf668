import Image, { ImageProps } from 'next/image';

import { getPlaceholderImage } from '@/lib/image-utils/get-placeholder-image';

export type ImageWithPlaceholderProps = Omit<ImageProps, 'width' | 'height'> & {
  src: string;
};

export async function ImageWithPlaceholder({
  alt,
  src,
  ...rest
}: ImageWithPlaceholderProps) {
  const { base64, img } = await getPlaceholderImage(src as string);
  return (
    <Image
      {...img}
      {...rest}
      alt={alt}
      placeholder="blur"
      blurDataURL={base64}
    />
  );
}
