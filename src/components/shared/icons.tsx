import {
  AlertTriangle,
  ArrowR<PERSON>,
  Bell,
  Bookmark,
  Building,
  Calendar1Icon,
  ChartColumnIncreasing,
  ChartLine,
  Check,
  ChevronLeft,
  ChevronRight,
  Command,
  CreditCard,
  File,
  FileText,
  HelpCircle,
  IdCard,
  Image,
  KeyRoundIcon,
  Laptop,
  LayoutDashboard,
  LayoutTemplate,
  Loader2,
  Mail,
  Moon,
  MoreVertical,
  NotebookPen,
  Package,
  Palette,
  Pizza,
  Plus,
  Rss,
  Settings,
  SunMedium,
  Trash,
  User,
  Users,
  X,
  type LucideIcon,
} from 'lucide-react';

export type Icon = LucideIcon;

export const Icons: Record<string, Icon> = {
  logo: Command,
  close: X,
  spinner: Loader2,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  trash: Trash,
  post: FileText,
  page: File,
  media: Image,
  settings: Settings,
  billing: CreditCard,
  ellipsis: MoreVertical,
  add: Plus,
  warning: AlertTriangle,
  user: User,
  arrowRight: ArrowRight,
  help: HelpCircle,
  pizza: Pizza,
  sun: SunMedium,
  moon: Moon,
  laptop: Laptop,
  dashboard: LayoutDashboard,
  check: Check,
  blog: NotebookPen,
  users: Users,
  appearance: Palette,
  notification: Bell,
  key: KeyRoundIcon,
  campaigns: Mail,
  subscriber: Rss,
  templates: LayoutTemplate,
  analytics: ChartLine,
  plans: Package,
  organizations: Building,
  charts: ChartColumnIncreasing,
  invite: IdCard,
  bookmark: Bookmark,
  calendar: Calendar1Icon,
};
