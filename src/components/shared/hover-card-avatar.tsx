import { LinkIcon } from 'lucide-react';

import { Author } from '@/types/post.types';
import { User } from '@/types/user.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { cn, getInitials, renderUserName } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';

type HoverCardAvatarProps = {
  user: User | Author;
  avatarClassName?: string;
};

export function HoverCardAvatar({
  user,
  avatarClassName,
}: HoverCardAvatarProps) {
  const username = getInitials(renderUserName(user));
  const image = user.image || getDeterministicAvatar(user.id);

  return (
    <HoverCard>
      <HoverCardTrigger>
        <Avatar className={cn('cursor-pointer', avatarClassName)}>
          <AvatarImage src={image} />
          <AvatarFallback>{username}</AvatarFallback>
        </Avatar>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="flex justify-between space-x-4">
          <Avatar>
            <AvatarImage src={image} />
            <AvatarFallback>{username}</AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <h4 className="text-sm font-semibold">{renderUserName(user)}</h4>
            {user.bio && (
              <p className="text-sm text-muted-foreground">
                {user.bio.length > 80
                  ? `${user.bio.slice(0, 77)}...`
                  : user.bio}
              </p>
            )}
            <div className="flex flex-col gap-1 mt-2">
              {user.urls?.map((url, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 justify-start hover:bg-transparent"
                  onClick={() =>
                    window.open(url, '_blank', 'noopener,noreferrer')
                  }
                >
                  <LinkIcon className="h-3 w-3 mr-1.5" />
                  <span className="truncate text-xs text-muted-foreground hover:text-primary transition-colors">
                    {url}
                  </span>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
