'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { appConfig } from '@/config';
import { SidebarNavItem } from '@/types';
import { Role } from '@prisma/client';
import { User } from 'lucide-react';

import { Plan } from '@/types/plan.types';
import { Tenant } from '@/types/tenant.types';
import { SessionUser } from '@/types/user.types';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import UpgradeBanner from '@/components/billing/upgrade-banner';
import { Icons } from '@/components/shared/icons';
import { OrganizationSwitcher } from '@/components/shared/organization-switcher';

type SidebarProps = {
  user: SessionUser;
  plans: Plan[];
  tenants: Tenant[];
};

const canAccess = (item: SidebarNavItem, role?: Role) => {
  if (!role) return false;
  if (!item.roles) return true; // No roles specified = accessible to all
  return item.roles.includes(role);
};
export function AppSidebar({ user, plans, tenants }: SidebarProps) {
  const path = usePathname();

  const role = user?.role;
  const hasTenant = Boolean(user?.currentTenantId);

  const currentTenant = tenants?.filter(
    (t) => t.id === user?.currentTenantId
  )[0];

  const canSeeTenantSwitcher = role === Role.ADMIN;

  const currentPlan =
    plans?.find?.(
      (p) =>
        p.stripePriceIdMonthly === user?.stripePriceId ||
        p.stripePriceIdYearly === user?.stripePriceId
    )?.name || 'Free';

  const shouldShowItem = (href: string) => {
    const hiddenPaths = ['/users', '/invitations', '/api-keys'];
    return hasTenant || !hiddenPaths.includes(href);
  };

  return (
    <Sidebar collapsible="icon" variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          {canSeeTenantSwitcher && (
            <SidebarMenuItem>
              <OrganizationSwitcher
                initialTenant={currentTenant}
                tenants={tenants}
                currentPlan={currentPlan}
              />
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Application</SidebarGroupLabel>
          <SidebarMenu>
            {appConfig.sidebarNav.map((item) => {
              if (!canAccess(item, role)) return null;
              if (!shouldShowItem(item.href || '')) return null;

              const Icon = item.icon
                ? Icons[item.icon as keyof typeof Icons]
                : User;

              return (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    isActive={item.href === path}
                  >
                    <Link href={item.href || ''}>
                      <Icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
        {role !== Role.USER && (
          <SidebarGroup>
            <SidebarGroupLabel>Admin</SidebarGroupLabel>
            <SidebarMenu>
              {appConfig.admin.map((item) => {
                if (!canAccess(item, role)) return null;
                if (!shouldShowItem(item.href || '')) return null;

                const Icon = item.icon
                  ? Icons[item.icon as keyof typeof Icons]
                  : User;

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      isActive={item.href === path}
                    >
                      <Link href={item.href || ''}>
                        <Icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroup>
        )}
      </SidebarContent>
      {/* only show the footer if Role is USER */}
      {user.role === Role.USER && (
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <UpgradeBanner />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      )}
      <SidebarRail />
    </Sidebar>
  );
}
