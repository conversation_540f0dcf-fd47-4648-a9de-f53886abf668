'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeftIcon } from 'lucide-react';

import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function BackButton() {
  const router = useRouter();
  return (
    <EnhancedButton
      variant="outline"
      onClick={() => router.back()}
      className="gap-2 font-semibold"
    >
      <ArrowLeftIcon /> Back
    </EnhancedButton>
  );
}
