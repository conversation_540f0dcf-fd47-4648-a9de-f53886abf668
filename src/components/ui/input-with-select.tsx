'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface InputWithSelectProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  selectValue: string;
  onSelectChange: (value: string) => void;
  selectItems: Array<{ value: string; label: string }>;
  selectPlaceholder?: string;
  className?: string;
  inputClassName?: string;
  selectClassName?: string;
}

export function InputWithSelect({
  selectValue,
  onSelectChange,
  selectItems,
  selectPlaceholder = 'Select...',
  className,
  inputClassName,
  selectClassName,
  ...props
}: InputWithSelectProps) {
  return (
    <div className={cn('flex items-stretch', className)}>
      <Input
        {...props}
        className={cn(
          'rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0 h-12 text-base px-4 flex-1',
          inputClassName
        )}
      />
      <Select value={selectValue} onValueChange={onSelectChange}>
        <SelectTrigger
          className={cn(
            'rounded-l-none border-l-0 focus:ring-0 focus:ring-offset-0 h-12 w-[130px]',
            selectClassName
          )}
        >
          <SelectValue placeholder={selectPlaceholder} />
        </SelectTrigger>
        <SelectContent>
          {selectItems.map((item) => (
            <SelectItem key={item.value} value={item.value}>
              {item.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
