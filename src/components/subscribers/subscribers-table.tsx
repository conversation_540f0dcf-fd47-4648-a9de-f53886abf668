'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import { deleteSubscriber, getSubscribers } from '@/actions/subscribers';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Import } from 'lucide-react';
import { toast } from 'sonner';

import { GetPaginatedResult } from '@/types/shared.types';
import { Subscriber } from '@/types/subscriber.types';
import { calculateNewPage } from '@/lib/pagination';
import { useTable } from '@/hooks/use-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';
import { SubscriberActions } from '@/components/subscribers/subscriber-actions';

type SubscribersTableProps = {
  initialData: GetPaginatedResult<Subscriber>;
};

export function SubscribersTable({ initialData }: SubscribersTableProps) {
  const isInitialMount = useRef(true);
  const queryClient = useQueryClient();

  const {
    data: subscribers,
    setData: setSubscribers,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sortBy,
    onSortChange,
    sorting,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<Subscriber>({ initialData });

  const [isPending, startTransition] = useTransition();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [subscriberToDelete, setSubscriberToDelete] =
    useState<Subscriber | null>(null);

  const columns = useMemo<ColumnDef<Subscriber>[]>(
    () => [
      {
        accessorKey: 'email',
        header: 'Email',
        enableSorting: true,
      },
      {
        accessorKey: 'createdAt',
        header: 'Subscribed At',
        enableSorting: true,
        cell: ({ row }) => (
          <div>{new Date(row.original.createdAt).toLocaleString()}</div>
        ),
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <SubscriberActions
            subscriber={row.original}
            onDelete={(subscriber) => {
              setSubscriberToDelete(subscriber);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: subscribers,
    columns,
    state: {
      sorting,
    },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['subscribers', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getSubscribers(defaultParams);
    },
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  const handleDeleteSubscriber = async () => {
    if (!subscriberToDelete) return;

    startTransition(async () => {
      try {
        await deleteSubscriber(subscriberToDelete.id);
        queryClient.invalidateQueries({ queryKey: ['subscribers'] });
        const newTotalItems = totalItems - 1;
        const newPage = calculateNewPage(page, newTotalItems, itemsPerPage);
        setTotalItems(newTotalItems);
        setPage(newPage);
        await refetch();
        toast.success('Subscriber deleted successfully');
      } catch (error) {
        console.error('Error deleting subscriber:', error);
        toast.error('Failed to delete subscriber');
      } finally {
        setIsDeleteConfirmOpen(false);
        setSubscriberToDelete(null);
      }
    });
  };

  const handleImportSubscribers = async () => {
    // TODO: Implement the import functionality
    toast.info('Import functionality not implemented yet');
  };

  useEffect(() => {
    if (result) {
      setSubscribers(result.items);
      setTotalItems(result.totalCount);

      // Only update page on initial mount if it differs
      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);

  return (
    <>
      {isEmpty && table.getRowModel().rows?.length === 0 ? (
        <EmptyState
          title="No Subscribers"
          description="There are no subscribers to display. You can import subscribers from other places."
          actionLabel="Import Subscribers"
          onAction={handleImportSubscribers}
          icon={<Import className="text-muted-foreground w-12 h-12 mb-4" />}
        />
      ) : (
        <>
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter subscribers..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <EnhancedButton onClick={handleImportSubscribers}>
              <Import className="w-4 h-4 mr-2" />
              Import Subscribers
            </EnhancedButton>
          </div>

          <div className="relative">
            {isLoading && <BluredBgSpinner />}

            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() &&
                              header.column.getIsSorted() && (
                                <span className="ml-2">
                                  {{
                                    asc: '↑',
                                    desc: '↓',
                                  }[header.column.getIsSorted() as string] ??
                                    ''}
                                </span>
                              )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No subscribers found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      )}

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteSubscriber}
        isLoading={isPending}
        loadingText="Deleting..."
        title="Confirm Delete Subscriber"
        description="Are you sure you want to delete this subscriber? This action cannot be undone."
      />
    </>
  );
}
