'use client';

import { addSubscriber } from '@/actions/subscribers/add-subscriber.action';
import {
  AddSubscriptionSchema,
  AddSubscriptionValues,
} from '@/schemas/subscription.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function SubscriptionForm() {
  const form = useForm<AddSubscriptionValues>({
    resolver: zodResolver(AddSubscriptionSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: AddSubscriptionValues) {
    try {
      await addSubscriber(values.email);

      form.reset();
      toast.success('Subscribed successfully!');
    } catch (error) {
      toast.error('Failed to subscribe', {
        description: `Failed to subscribe. Please try again. ${
          (error as Error).message
        }`,
      });
    }
  }

  return (
    <div className="w-full max-w-3xl mx-auto p-4">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col sm:flex-row items-start gap-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="w-full max-w-sm">
                <FormControl>
                  <Input placeholder="Enter your email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <EnhancedButton type="submit">Subscribe</EnhancedButton>
        </form>
      </Form>
    </div>
  );
}
