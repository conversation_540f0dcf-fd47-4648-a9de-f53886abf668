import React from 'react';

import { Switch } from '@/components/ui/switch';

interface BillingToggleProps {
  isYearly: boolean;
  setIsYearly: (value: boolean) => void;
}

export function BillingToggle({ isYearly, setIsYearly }: BillingToggleProps) {
  return (
    <div className="flex items-center justify-center my-8">
      <span className={`mr-2 ${!isYearly ? 'font-bold' : ''}`}>Monthly</span>
      <Switch checked={isYearly} onCheckedChange={setIsYearly} />
      <span className={`ml-2 ${isYearly ? 'font-bold' : ''}`}>Yearly</span>
      <span className="ml-2 text-sm bg-muted px-2 py-1 rounded">
        Get 2 free months if paid yearly
      </span>
    </div>
  );
}
