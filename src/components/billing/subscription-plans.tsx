'use client';

import { useCallback, useMemo, useState } from 'react';
import { FREE_PLAN } from '@/constants';
import { UserSubscriptionPlan } from '@/types';

import { BillingToggle } from './billing-toggle';
import { CurrentSubscriptionCard } from './current-subscription-card';
import { PlanCard } from './plan-card';
import { SubscriptionSuccess } from './subscription-success';

type Plan = {
  id: string;
  name: string;
  description: string;
  features: string[];
  monthlyPrice: number;
  yearlyPrice: number;
  stripePriceIdMonthly: string;
  stripePriceIdYearly: string;
  stripeProductId: string;
  isPopular: boolean;
};

interface SubscriptionPlansProps {
  subscription: UserSubscriptionPlan;
  plans?: Plan[];
}

export default function SubscriptionPlans({
  subscription: initialSubscription,
  plans = [],
}: SubscriptionPlansProps) {
  const [isYearly, setIsYearly] = useState(false);
  const [subscription, setSubscription] =
    useState<UserSubscriptionPlan>(initialSubscription);

  const sortedPlans = useMemo(() => {
    return [...plans].sort((a, b) => {
      const priceA = isYearly ? a.yearlyPrice : a.monthlyPrice;
      const priceB = isYearly ? b.yearlyPrice : b.monthlyPrice;
      return priceA - priceB;
    });
  }, [plans, isYearly]);

  const getCurrentPlan = useCallback(() => {
    return plans.find((plan) => plan.name === subscription.name) || FREE_PLAN;
  }, [subscription.name, plans]);

  const currentPlan = getCurrentPlan();

  const handleSubscriptionChange = useCallback(
    (newPriceId: string) => {
      const newPlan = plans.find(
        (plan) =>
          plan.stripePriceIdMonthly === newPriceId ||
          plan.stripePriceIdYearly === newPriceId
      );

      if (newPlan) {
        const isNewPlanYearly = newPlan.stripePriceIdYearly === newPriceId;
        const newPeriodEnd = new Date(
          Date.now() + (isNewPlanYearly ? 365 : 30) * 24 * 60 * 60 * 1000
        );

        setSubscription((prevSubscription) => ({
          ...prevSubscription,
          name: newPlan.name,
          description: newPlan.description,
          stripeCurrentPeriodEnd: newPeriodEnd,
          isSubscribed: true,
          isCanceled: false,
          stripePriceId: newPriceId,
        }));
      } else {
        // If no matching plan is found, revert to the initial subscription
        setSubscription(initialSubscription);
      }
    },
    [initialSubscription, plans]
  );

  const currentPlanPriceId = currentPlan
    ? isYearly
      ? currentPlan.stripePriceIdYearly
      : currentPlan.stripePriceIdMonthly
    : '';

  return (
    <>
      <SubscriptionSuccess />
      <CurrentSubscriptionCard
        subscription={subscription}
        currentPlanPriceId={currentPlanPriceId}
        onSubscriptionChange={handleSubscriptionChange}
      />

      {plans.length ? (
        <BillingToggle isYearly={isYearly} setIsYearly={setIsYearly} />
      ) : null}

      <div className={`grid gap-6 md:grid-cols-2 lg:grid-cols-${plans.length}`}>
        {sortedPlans.map((plan) => (
          <PlanCard
            key={plan.id}
            plan={{
              isPopular: plan.isPopular,
              name: plan.name,
              description: plan.description,
              features: plan.features,
              pricing: {
                monthly: {
                  price: plan.monthlyPrice,
                  priceId: plan.stripePriceIdMonthly,
                },
                yearly: {
                  price: plan.yearlyPrice,
                  priceId: plan.stripePriceIdYearly,
                },
              },
            }}
            isYearly={isYearly}
            isCurrentPlan={
              plan.name === subscription.name &&
              (isYearly
                ? plan.stripePriceIdYearly === subscription.stripePriceId
                : plan.stripePriceIdMonthly === subscription.stripePriceId)
            }
            onSubscriptionChange={handleSubscriptionChange}
          />
        ))}
      </div>
    </>
  );
}
