'use client';

import { useEffect, useMemo, useState } from 'react';
import {
  BillingHistoryItem,
  BillingHistorySortField,
  getBillingHistory,
} from '@/actions/subscriptions/get-billing-history.action';
import { ITEMS_PER_PAGE } from '@/constants';
import { useQuery } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { ArrowDownIcon } from 'lucide-react';
import { toast } from 'sonner';

import { capitalize } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';

type BillingHistoryProps = {
  initialData: {
    items: BillingHistoryItem[];
    totalCount: number;
  };
};

export function BillingHistoryTable({ initialData }: BillingHistoryProps) {
  const [page, setPage] = useState(1);
  const [rawData, setRawData] = useState(initialData.items);
  const [totalItems, setTotalItems] = useState(initialData.totalCount);
  const [sorting, setSorting] = useState<SortingState>([]);

  const columns = useMemo<ColumnDef<BillingHistoryItem>[]>(
    () => [
      {
        id: 'invoice',
        enableSorting: true,
        accessorKey: 'id',
        header: 'Invoice',
      },
      {
        id: 'date',
        enableSorting: true,
        accessorKey: 'date',
        header: 'Date',
      },
      {
        id: 'amount',
        enableSorting: true,
        accessorKey: 'amount',
        header: 'Amount',
        cell: ({ row }) => <div>${row.original.amount.toFixed(2)}</div>,
      },
      {
        id: 'status',
        enableSorting: true,
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => (
          <Badge
            variant={
              row.original.status === 'paid'
                ? 'default'
                : row.original.status === 'draft'
                  ? 'secondary'
                  : 'destructive'
            }
          >
            {capitalize(row.original.status)}
          </Badge>
        ),
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <EnhancedButton
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            disabled={!row.original.invoiceUrl}
            asChild
          >
            {row.original.invoiceUrl ? (
              <a
                href={row.original.invoiceUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center"
              >
                <ArrowDownIcon className="h-4 w-4" />
                <span className="sr-only">Download Invoice</span>
              </a>
            ) : (
              <span className="flex items-center justify-center">
                <ArrowDownIcon className="h-4 w-4" />
                <span className="sr-only">No Invoice Available</span>
              </span>
            )}
          </EnhancedButton>
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: rawData,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    pageCount: Math.ceil(totalItems / ITEMS_PER_PAGE),
  });

  const {
    data: billingHistory,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['billing-history', page, sorting],
    queryFn: async () => {
      const sortField = sorting[0]?.id as BillingHistorySortField;
      const sortDirection = sorting[0]?.desc ? 'desc' : 'asc';

      return getBillingHistory({
        page,
        itemsPerPage: ITEMS_PER_PAGE,
        sortBy: sortField,
        sortOrder: sortDirection,
      });
    },
    initialData: initialData,
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  // Update local state when query data changes
  useEffect(() => {
    if (billingHistory) {
      setRawData(billingHistory.items);
      setTotalItems(billingHistory.totalCount);
    }
  }, [billingHistory]);

  // Handle query errors
  useEffect(() => {
    if (error) {
      toast.error('Failed to fetch billing history');
    }
  }, [error]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  if (!rawData.length) {
    return (
      <EmptyState
        title="No billing history"
        description="You don't have any past invoices yet. They will appear here once you make a payment."
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="relative rounded-md">
        {isLoading && <BluredBgSpinner />}
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={
                      header.column.getCanSort()
                        ? 'cursor-pointer select-none'
                        : ''
                    }
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {header.isPlaceholder ? null : (
                      <div className="flex items-center gap-2">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {header.column.getCanSort() && (
                          <span className="ml-2">
                            {{
                              asc: ' ↑',
                              desc: ' ↓',
                            }[header.column.getIsSorted() as string] ?? null}
                          </span>
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {billingHistory.totalCount > ITEMS_PER_PAGE &&
        table.getRowModel().rows?.length > 0 && (
          <Pagination
            totalItems={totalItems}
            itemsPerPage={ITEMS_PER_PAGE}
            currentPage={page}
            onPageChange={handlePageChange}
          />
        )}
    </div>
  );
}
