"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { fireSideCannons } from "@/lib/confetti";

export function SubscriptionSuccess() {
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const isSuccess = searchParams.get("success");
    if (isSuccess === "true") {
      fireSideCannons();
    }
  }, [searchParams]);

  return null; // This is a utility component that doesn't render anything
}