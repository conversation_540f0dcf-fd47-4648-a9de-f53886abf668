import React from 'react';
import { UserSubscriptionPlan } from '@/types';

import { formatDate } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import ManageSubscriptionButton from './manage-subscription-button';

interface CurrentSubscriptionCardProps {
  subscription: UserSubscriptionPlan;
  currentPlanPriceId: string;
  onSubscriptionChange: (newPriceId: string) => void;
}

export function CurrentSubscriptionCard({
  subscription,
  currentPlanPriceId,
  onSubscriptionChange,
}: CurrentSubscriptionCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Plan</CardTitle>
        <CardDescription>
          You are currently on the <strong>{subscription.name}</strong> plan.
        </CardDescription>
      </CardHeader>
      <CardContent>{subscription.description}</CardContent>
      <CardFooter className="flex flex-col items-start space-y-2 md:flex-row md:justify-between md:space-x-0">
        {subscription.isSubscribed && (
          <ManageSubscriptionButton
            priceId={currentPlanPriceId}
            isCurrentPlan={true}
            onSubscriptionChange={onSubscriptionChange}
          />
        )}
        {subscription.isSubscribed ? (
          <p className="rounded-full text-xs font-medium">
            {subscription.isCanceled
              ? 'Your plan will be canceled on '
              : 'Your plan renews on '}
            {formatDate(
              subscription.stripeCurrentPeriodEnd?.toString() as string
            )}
            .
          </p>
        ) : null}
      </CardFooter>
    </Card>
  );
}
