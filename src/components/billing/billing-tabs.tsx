'use client';

import React from 'react';
import { GetBillingHistoryResult } from '@/actions/subscriptions/get-billing-history.action';
import { useQueryState } from 'nuqs';

import { Plan } from '@/types/plan.types';
import { GetPaginatedResult } from '@/types/shared.types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BillingHistoryTable } from '@/components/billing/billing-history-table';
import SubscriptionPlans from '@/components/billing/subscription-plans';

interface BillingTabsProps {
  subscription: any;
  billingHistory: GetBillingHistoryResult;
  plans: GetPaginatedResult<Plan>;
  initialTab: string;
}

export default function BillingTabs({
  subscription,
  billingHistory,
  plans,
  initialTab,
}: BillingTabsProps) {
  const [tab, setTab] = useQueryState('tab', {
    defaultValue: initialTab,
    clearOnDefault: false,
  });

  return (
    <Tabs value={tab} onValueChange={setTab}>
      <TabsList className="mb-4">
        <TabsTrigger value="subscription">Subscription</TabsTrigger>
        <TabsTrigger value="history">Billing History</TabsTrigger>
      </TabsList>
      <TabsContent value="subscription">
        <SubscriptionPlans subscription={subscription} plans={plans.items} />
      </TabsContent>
      <TabsContent value="history">
        <BillingHistoryTable initialData={billingHistory} />
      </TabsContent>
    </Tabs>
  );
}
