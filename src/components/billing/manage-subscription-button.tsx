'use client';

import { forwardRef, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { manageStripeSubscription } from '@/actions/subscriptions';
import { toast } from 'sonner';

import { EnhancedButton } from '@/components/shared/enhanced-button';

import { ButtonProps } from '../ui/button';

type ManageSubscriptionButtonProps = ButtonProps & {
  priceId: string;
  isCurrentPlan: boolean;
  onSubscriptionChange: (newPriceId: string) => void;
};

const ManageSubscriptionButton = forwardRef<
  HTMLButtonElement,
  ManageSubscriptionButtonProps
>(({ className, isCurrentPlan, priceId, onSubscriptionChange }, ref) => {
  const [isLoading, startTransition] = useTransition();
  const router = useRouter();

  const handleClick = async () => {
    startTransition(async () => {
      try {
        // Optimistic update
        onSubscriptionChange(priceId);

        const result = await manageStripeSubscription({
          priceId,
          isCurrentPlan,
        });

        if (typeof result === 'string') {
          // If a URL is returned, redirect to Stripe
          window.location.href = result;
        } else {
          // If no URL is returned, it means the subscription was updated successfully
          toast.success('Subscription updated successfully');
          // Refresh the page to get the latest subscription data
          router.refresh();
        }
      } catch (error) {
        console.error(error);
        // Revert optimistic update on error
        onSubscriptionChange('');
        toast.error('Failed to update subscription');
      }
    });
  };

  return (
    <EnhancedButton
      ref={ref}
      className={className}
      onClick={handleClick}
      disabled={isLoading}
      isLoading={isLoading}
    >
      {isCurrentPlan ? 'Manage Subscription' : 'Choose Subscription'}
    </EnhancedButton>
  );
});

ManageSubscriptionButton.displayName = 'ManageSubscriptionButton';

export default ManageSubscriptionButton;
