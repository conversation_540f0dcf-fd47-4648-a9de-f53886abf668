import React from 'react';
import { Check } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import ManageSubscriptionButton from './manage-subscription-button';

interface PlanCardProps {
  plan: {
    name: string;
    description: string;
    features: string[];
    isPopular?: boolean;
    pricing: {
      monthly: { price: number; priceId: string | null };
      yearly: { price: number; priceId: string | null };
    };
  };
  isYearly: boolean;
  isCurrentPlan: boolean;
  onSubscriptionChange: (newPriceId: string) => void;
}

export function PlanCard({
  plan,
  isYearly,
  isCurrentPlan,
  onSubscriptionChange,
}: PlanCardProps) {
  const pricing = isYearly ? plan.pricing.yearly : plan.pricing.monthly;

  return (
    <Card className="flex flex-col relative">
      {plan.isPopular && (
        <Badge
          variant="secondary"
          className="absolute top-4 right-4 bg-primary text-primary-foreground"
        >
          Popular
        </Badge>
      )}
      <CardHeader>
        <CardTitle className="text-xl mb-2">{plan.name}</CardTitle>
        <CardDescription>{plan.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-4">
          <span className="text-3xl font-bold">
            {pricing.price === 0 ? 'Free' : `$${pricing.price}`}
          </span>
          {pricing.price !== 0 && (
            <span className="text-sm text-gray-500">
              /{isYearly ? 'year' : 'month'}
            </span>
          )}
        </div>
        <ul className="space-y-2 mb-6">
          {plan.features.map((feature, featureIndex) => (
            <li key={featureIndex} className="flex items-center">
              <Check className="h-5 w-5 mr-2" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        {pricing.price !== 0 && pricing.priceId && (
          <ManageSubscriptionButton
            className="w-full"
            isCurrentPlan={isCurrentPlan}
            priceId={pricing.priceId}
            onSubscriptionChange={onSubscriptionChange}
          />
        )}
      </CardFooter>
    </Card>
  );
}
