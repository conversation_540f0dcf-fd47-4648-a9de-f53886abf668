import Link from 'next/link';
import { Sparkles } from 'lucide-react';

import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function UpgradeBanner() {
  return (
    <div className="w-full rounded-lg border bg-card shadow-xl overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10" />
      <div className="relative p-3 space-y-2">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-primary" />
          <h3 className="font-heading font-semibold text-foreground text-sm">
            Unlock Pro Features
          </h3>
        </div>
        <p className="text-xs text-muted-foreground">
          Get unlimited access to premium features
        </p>
        <EnhancedButton
          asChild
          variant="default"
          className="w-full group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
          size="sm"
        >
          <Link href="/upgrade">
            Upgrade Now
            <Sparkles className="w-4 h-4 ml-1.5 transition-all duration-200 ease-in-out group-hover:rotate-12" />
          </Link>
        </EnhancedButton>
      </div>
    </div>
  );
}
