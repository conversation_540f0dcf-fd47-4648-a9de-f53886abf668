'use client';

import { Plus } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';

interface CreateOrganizationCardProps {
  onClick: () => void;
}

export function CreateOrganizationCard({
  onClick,
}: CreateOrganizationCardProps) {
  return (
    <Card
      className="flex flex-col cursor-pointer hover:shadow-lg transition-shadow border-dashed"
      onClick={onClick}
    >
      <CardContent className="flex flex-col p-6 h-full">
        <div className="flex justify-between items-start mb-4">
          <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center">
            <Plus className="w-6 h-6" />
          </div>
        </div>
        <p className="text-gray-600 mb-4 flex-grow">
          Set up a new workspace for your team
        </p>
        <div className="h-8" /> {/* Spacer to match AvatarCircles height */}
      </CardContent>
    </Card>
  );
}
