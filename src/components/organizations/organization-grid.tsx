'use client';

import React, { useState } from 'react';
import {
  createTenant,
  deleteTenant,
  transferTenantOwnership,
  updateTenant,
} from '@/actions/tenant';
import {
  getTenantMembers,
  type TenantMember,
} from '@/actions/tenant/get-tenant-members.action';
import { switchTenant } from '@/actions/tenant/switch-tenant';
import { uploadFile } from '@/actions/upload';
import {
  CreateTenantValues,
  UpdateTenantValues,
} from '@/schemas/tenant.schemas';
import { useTenantStore } from '@/stores/tenant-store';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

import { Tenant } from '@/types/tenant.types';
import { Modal } from '@/components/ui/modal';
import { CreateOrganizationCard } from '@/components/organizations/create-organization-card';
import { OrganizationCard } from '@/components/organizations/organization-card';
import { OrgForm } from '@/components/organizations/organization-form';
import { TransferOwnershipModal } from '@/components/organizations/transfer-ownership-modal';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export function OrganizationGrid() {
  const {
    currentTenant,
    setCurrentTenant,
    setAvailableTenants,
    availableTenants,
  } = useTenantStore();

  const { data: session, update: updateSession } = useSession();
  const queryClient = useQueryClient();
  const [isPending, startTransition] = React.useTransition();

  const [isNewModalOpen, setIsNewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<Tenant | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<TenantMember | null>(
    null
  );
  const [members, setMembers] = useState<TenantMember[]>([]);

  // Replace the manual members fetching with useQuery
  const { data: organizationMembers = {}, isLoading: isLoadingMembers } =
    useQuery({
      queryKey: ['organization-members'],
      queryFn: async () => {
        const membersPromises = availableTenants.map(async (org) => {
          const members = await getTenantMembers(org.id);
          return [org.id, members];
        });
        const results = await Promise.all(membersPromises);
        return Object.fromEntries(results);
      },
      staleTime: 1000 * 60, // Consider data fresh for 1 minute
    });

  const handleCardClick = (org: Tenant) => {
    console.log('Card clicked', org);
    // TODO: handle card click
  };

  const handleEdit = (org: Tenant) => {
    setSelectedOrg(org);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (e: React.MouseEvent, org: Tenant) => {
    e.stopPropagation();
    setSelectedOrg(org);
    setIsDeleteConfirmOpen(true);
  };

  const handleTransferModalOpen = (org: Tenant) => {
    setSelectedOrg(org);
    setMembers(organizationMembers[org.id] || []);
    setIsTransferModalOpen(true);
  };

  const handleTransferOwnership = async (e: React.MouseEvent, org: Tenant) => {
    e.stopPropagation();
    if (!selectedMember) return;

    startTransition(async () => {
      try {
        await transferTenantOwnership(org.id, selectedMember.id);
        queryClient.invalidateQueries({ queryKey: ['organization-members'] });
        toast.success('Organization ownership transferred successfully');

        setIsTransferModalOpen(false);
      } catch (error) {
        toast.error(
          error instanceof Error
            ? error.message
            : 'Failed to transfer ownership'
        );
      }
    });
  };

  const handleCreateOrganization = async (
    data: CreateTenantValues & { logoFile?: FormData }
  ) => {
    startTransition(async () => {
      try {
        let logoUrl = data.logo;

        if (data.logoFile) {
          const result = await uploadFile(data.logoFile);
          logoUrl = result.url;
        }

        const newTenant = await createTenant({
          ...data,
          logo: logoUrl,
        });

        await switchTenant(newTenant.id);

        setAvailableTenants([...availableTenants, newTenant]);
        setCurrentTenant(newTenant);

        updateSession({
          ...session,
          user: {
            ...session?.user,
            currentTenantId: newTenant.id,
          },
        });

        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['users'] }),
          queryClient.invalidateQueries({ queryKey: ['invites'] }),
          queryClient.invalidateQueries({ queryKey: ['organization-members'] }),
        ]);

        toast.success('Organization created successfully');
        setIsNewModalOpen(false);
      } catch (error) {
        toast.error('Something went wrong', {
          description: `Failed to create organization ${(error as Error).message}`,
        });
      }
    });
  };

  const handleEditOrganization = async (
    data: UpdateTenantValues & { logoFile?: FormData }
  ) => {
    startTransition(async () => {
      if (!selectedOrg) return;

      try {
        let logoUrl = data.logo;

        if (data.logoFile) {
          const result = await uploadFile(data.logoFile);
          logoUrl = result.url;
        }

        const updatedTenant = await updateTenant(selectedOrg.id, {
          ...data,
          logo: logoUrl,
        });

        const newTenants = availableTenants.map((o) =>
          o.id === updatedTenant.id ? updatedTenant : o
        );
        setAvailableTenants(newTenants);

        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['users'] }),
          queryClient.invalidateQueries({ queryKey: ['invites'] }),
          queryClient.invalidateQueries({ queryKey: ['organization-members'] }),
        ]);

        toast.success('Organization updated successfully');
        setIsEditModalOpen(false);
      } catch (error) {
        toast.error('Something went wrong', {
          description: `Failed to update organization ${(error as Error).message}`,
        });
      }
    });
  };

  const handleDeleteOrganization = async () => {
    if (!selectedOrg) return;

    startTransition(async () => {
      try {
        await deleteTenant(selectedOrg.id);
        const newTenants = availableTenants.filter(
          (o) => o.id !== selectedOrg.id
        );
        setAvailableTenants(newTenants);

        queryClient.removeQueries({ queryKey: ['users'] });
        queryClient.removeQueries({ queryKey: ['invites'] });
        queryClient.removeQueries({ queryKey: ['organization-members'] });

        await Promise.all([
          queryClient.invalidateQueries({ queryKey: ['users'] }),
          queryClient.invalidateQueries({ queryKey: ['invites'] }),
          queryClient.invalidateQueries({ queryKey: ['organization-members'] }),
        ]);
        toast.success('Organization deleted successfully');
      } catch (error) {
        toast.error('Something went wrong', {
          description: `Failed to delete organization ${(error as Error).message}`,
        });
      } finally {
        setIsDeleteConfirmOpen(false);
      }
    });
  };

  return (
    <>
      <div className="">
        {/* Use Modal component */}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <CreateOrganizationCard onClick={() => setIsNewModalOpen(true)} />

          {availableTenants.map((org) => (
            <OrganizationCard
              key={org.id}
              currentTenantId={currentTenant?.id}
              org={org}
              organizationMembers={organizationMembers}
              isLoadingMembers={isLoadingMembers}
              onCardClick={handleCardClick}
              onEdit={handleEdit}
              onTransfer={handleTransferModalOpen}
              onDelete={handleDeleteClick}
              organizationsCount={availableTenants.length}
            />
          ))}
        </div>
      </div>

      {/* Edit Organization Modal */}

      <Modal
        title="Create Organization"
        description="Set up your organization's profile and details"
        isOpen={isNewModalOpen}
        onOpenChange={setIsNewModalOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="org-form"
            isLoading={isPending}
            loadingText="Creating..."
          >
            Create Organization
          </EnhancedButton>
        }
      >
        <OrgForm onSubmit={handleCreateOrganization} />
      </Modal>

      <Modal
        title="Edit Organization"
        description="Update your organization details."
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="org-form"
            isLoading={isPending}
            loadingText="Saving..."
          >
            Save
          </EnhancedButton>
        }
      >
        {selectedOrg && (
          <OrgForm
            organization={selectedOrg}
            onSubmit={handleEditOrganization}
          />
        )}
      </Modal>

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteOrganization}
        isLoading={isPending}
        loadingText="Deleting..."
        title="Delete Organization"
        description="Are you sure you want to delete this organization? This action cannot be undone."
      />

      {/* Transfer Ownership Modal */}

      <TransferOwnershipModal
        isOpen={isTransferModalOpen}
        onOpenChange={(open) => {
          setIsTransferModalOpen(open);
          if (open && selectedOrg) {
            setMembers(organizationMembers[selectedOrg.id] || []);
          } else {
            setSelectedMember(null);
          }
        }}
        selectedOrg={selectedOrg}
        members={members}
        isLoadingMembers={isLoadingMembers}
        selectedMember={selectedMember}
        onMemberSelect={setSelectedMember}
        onTransfer={handleTransferOwnership}
      />
    </>
  );
}
