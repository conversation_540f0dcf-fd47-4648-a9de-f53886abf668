'use client';

import * as React from 'react';
import { useRef, useState } from 'react';
import {
  createTenantSchema,
  type CreateTenantValues,
} from '@/schemas/tenant.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { Camera } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Tenant } from '@/types/tenant.types';
import { getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const defaultLogo = '/organization-logo.svg';

interface OrgFormProps {
  organization?: Tenant;
  onSubmit: (
    data: CreateTenantValues & { logoFile?: FormData }
  ) => Promise<void>;
}

export function OrgForm({ organization, onSubmit }: OrgFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [logoFile, setLogoFile] = useState<FormData | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(
    organization?.logo || null
  );

  const form = useForm<CreateTenantValues>({
    resolver: zodResolver(createTenantSchema),
    defaultValues: {
      name: organization?.name || '',
      description: organization?.description || '',
      subdomain: organization?.subdomain || '',
      logo: organization?.logo || '',
    },
  });

  async function handleSubmit(data: CreateTenantValues) {
    await onSubmit({ ...data, logoFile: logoFile || undefined });
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-8"
        id="org-form"
      >
        <FormField
          name="logo"
          control={form.control}
          render={() => (
            <FormItem>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Avatar className="h-20 w-20">
                    <AvatarImage
                      src={logoPreview || defaultLogo}
                      alt="Organization logo"
                    />
                    <AvatarFallback>
                      {getInitials(form.watch('name') || 'Organization Name')}
                    </AvatarFallback>
                  </Avatar>
                  <EnhancedButton
                    type="button"
                    size="icon"
                    className="absolute bottom-0 right-0 h-8 w-8 rounded-full bg-primary text-primary-foreground hover:bg-primary/90"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Camera className="h-4 w-4" />
                    <span className="sr-only">Change organization logo</span>
                  </EnhancedButton>
                </div>
                <Input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const imageUrl = URL.createObjectURL(file);
                      const formData = new FormData();
                      formData.append('file', file);
                      setLogoFile(formData);
                      setLogoPreview(imageUrl);
                    }
                  }}
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Organization Name</FormLabel>
              <FormControl>
                <Input placeholder="Acme Inc." {...field} />
              </FormControl>
              <FormDescription>
                This is your organization&apos;s name as it will appear
                throughout the app.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="subdomain"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subdomain</FormLabel>
              <FormControl>
                <Input placeholder="acme" {...field} />
              </FormControl>
              <FormDescription>
                This will be your unique subdomain (e.g., acme.example.com)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us about your organization..."
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A brief description of your organization.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
