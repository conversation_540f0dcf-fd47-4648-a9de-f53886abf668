'use client';

import { TenantMember } from '@/actions/tenant/get-tenant-members.action';

import { Tenant } from '@/types/tenant.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import { Modal } from '@/components/ui/modal';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import Spinner from '@/components/shared/spinner';

interface TransferOwnershipModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedOrg: Tenant | null;
  members: TenantMember[];
  isLoadingMembers: boolean;
  selectedMember: TenantMember | null;
  onMemberSelect: (member: TenantMember | null) => void;
  onTransfer: (e: React.MouseEvent, org: Tenant) => void;
}

export function TransferOwnershipModal({
  isOpen,
  onOpenChange,
  selectedOrg,
  members,
  isLoadingMembers,
  selectedMember,
  onMemberSelect,
  onTransfer,
}: TransferOwnershipModalProps) {
  return (
    <Modal
      title="Transfer Ownership"
      description={`Transfer ownership of ${selectedOrg?.name} to another member.`}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      footer={
        <div className="flex justify-end gap-3">
          <EnhancedButton
            variant="secondary"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </EnhancedButton>
          <EnhancedButton
            onClick={(e) => selectedOrg && onTransfer(e, selectedOrg)}
            variant="default"
            disabled={!selectedMember || members.length === 0}
          >
            Transfer
          </EnhancedButton>
        </div>
      }
    >
      <div className="grid gap-4 py-4">
        <div className="grid gap-2">
          <Label>Select New Owner</Label>
          <Select
            value={selectedMember?.id}
            onValueChange={(value) => {
              const member = members.find((m) => m.id === value);
              onMemberSelect(member || null);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a member" />
            </SelectTrigger>
            <SelectContent className="max-h-[200px]">
              <ScrollArea className="h-full">
                {isLoadingMembers ? (
                  <div className="relative px-2 py-4 text-center text-sm text-muted-foreground">
                    Loading members...
                    <Spinner />
                  </div>
                ) : members.length > 0 ? (
                  members.map((member) => (
                    <SelectItem
                      key={member.id}
                      value={member.id}
                      className="cursor-pointer pl-2"
                    >
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage
                            src={
                              member.image || getDeterministicAvatar(member.id)
                            }
                            alt={member.name || member.email}
                          />
                          <AvatarFallback>
                            {getInitials(member.name || member.email)}
                          </AvatarFallback>
                        </Avatar>
                        <span>{member.email}</span>
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <div className="relative px-2 py-4 text-center text-sm text-muted-foreground">
                    No members available for transfer
                  </div>
                )}
              </ScrollArea>
            </SelectContent>
          </Select>
        </div>
      </div>
    </Modal>
  );
}
