'use client';

import { TenantMember } from '@/actions/tenant/get-tenant-members.action';
import { MoreVertical } from 'lucide-react';

import { Tenant } from '@/types/tenant.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AvatarCircles } from '@/components/magicui/avatar-circles';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { AvatarCirclesSkeleton } from '@/components/skeletons/avatar-circles.skeleton';

const AVATAR_COUNT = 3;
const defaultLogo = '/organization-logo.svg';

interface OrganizationCardProps {
  org: Tenant;
  organizationMembers: Record<string, TenantMember[]>;
  isLoadingMembers: boolean;
  onCardClick: (org: Tenant) => void;
  onEdit: (org: Tenant) => void;
  onTransfer: (org: Tenant) => void;
  onDelete: (e: React.MouseEvent, org: Tenant) => void;
  organizationsCount: number;
  currentTenantId?: string;
}

export function OrganizationCard({
  org,
  currentTenantId,
  organizationMembers,
  isLoadingMembers,
  onCardClick,
  onEdit,
  onTransfer,
  onDelete,
  organizationsCount,
}: OrganizationCardProps) {
  return (
    <Card
      key={org.id}
      className="flex flex-col cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => onCardClick(org)}
    >
      <CardContent className="flex flex-col p-6">
        <div className="flex justify-between items-start mb-4">
          <Avatar className="w-12 h-12">
            <AvatarImage
              src={org.logo || defaultLogo}
              alt={`${org.name} logo`}
            />
            <AvatarFallback>{getInitials(org.name)}</AvatarFallback>
          </Avatar>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical className="h-4 w-4" />
              </EnhancedButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(org);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onTransfer(org);
                }}
              >
                Transfer Ownership
              </DropdownMenuItem>
              {organizationsCount > 1 && currentTenantId !== org.id && (
                <DropdownMenuItem onClick={(e) => onDelete(e, org)}>
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <h2 className="text-xl font-semibold mb-2">{org.name}</h2>
        <p className="text-gray-600 mb-4 flex-grow">{org.description}</p>

        {isLoadingMembers ? (
          <AvatarCirclesSkeleton size="sm" count={AVATAR_COUNT + 1} />
        ) : (
          <AvatarCircles
            numPeople={
              (organizationMembers[org.id]?.length || 0) > AVATAR_COUNT
                ? organizationMembers[org.id].length - AVATAR_COUNT
                : 0
            }
            avatarUrls={(organizationMembers[org.id] || [])
              .slice(0, AVATAR_COUNT)
              .map((member) => ({
                imageUrl: member.image || getDeterministicAvatar(member.id),
                profileUrl: '#',
              }))}
            size="sm"
          />
        )}
      </CardContent>
      <CardFooter>
        <p className="text-sm text-gray-500">Subdomain: {org.subdomain}</p>
      </CardFooter>
    </Card>
  );
}
