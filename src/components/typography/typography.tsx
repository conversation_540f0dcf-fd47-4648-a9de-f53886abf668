import { cn } from '@/lib/utils';

type Props = {
  children: React.ReactNode;
  className: string;
  as:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'p'
    | 'blockquote'
    | 'ul'
    | 'code'
    | 'lead'
    | 'large'
    | 'small'
    | 'muted';
};

type TypographyProps = {
  children: React.ReactNode;
  className: string;
};

export function TypographyH1({ children, className }: TypographyProps) {
  return (
    <h1
      className={cn(
        'font-heading scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl',
        className
      )}
    >
      {children}
    </h1>
  );
}

export function TypographyH2({ children, className }: TypographyProps) {
  return (
    <h2
      className={cn(
        'font-heading  scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0',
        className
      )}
    >
      {children}
    </h2>
  );
}

export function TypographyH3({ children, className }: TypographyProps) {
  return (
    <h3
      className={cn(
        'font-heading  scroll-m-20 text-2xl font-semibold tracking-tight',
        className
      )}
    >
      {children}
    </h3>
  );
}

export function TypographyH4({ children, className }: TypographyProps) {
  return (
    <h4
      className={cn(
        'font-heading  scroll-m-20 text-xl font-semibold tracking-tight',
        className
      )}
    >
      {children}
    </h4>
  );
}

export function TypographyP({ children, className }: TypographyProps) {
  return (
    <p className={cn('leading-7 [&:not(:first-child)]:mt-6', className)}>
      {children}
    </p>
  );
}

export function TypographyBlockquote({ children, className }: TypographyProps) {
  return (
    <blockquote className={cn('mt-6 border-l-2 pl-6 italic', className)}>
      {children}
    </blockquote>
  );
}

export function TypographyList({ children, className }: TypographyProps) {
  return (
    <ul className={cn('my-6 ml-6 list-disc [&>li]:mt-2', className)}>
      {children}
    </ul>
  );
}

export function TypographyInlineCode({ children, className }: TypographyProps) {
  return (
    <code
      className={cn(
        'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',
        className
      )}
    >
      {children}
    </code>
  );
}

export function TypographyLead({ children, className }: TypographyProps) {
  return (
    <p className={cn('text-xl text-muted-foreground', className)}>{children}</p>
  );
}

export function TypographyLarge({ children, className }: TypographyProps) {
  return (
    <div className={cn('text-lg font-semibold', className)}>{children}</div>
  );
}

export function TypographySmall({ children, className }: TypographyProps) {
  return (
    <small className={cn('text-sm font-medium leading-none', className)}>
      {children}
    </small>
  );
}

export function TypographyMuted({ children, className }: TypographyProps) {
  return (
    <p className={cn('text-sm text-muted-foreground', className)}>{children}</p>
  );
}

export default function Typography({ children, as, ...rest }: Props) {
  switch (as) {
    case 'h1':
      return <TypographyH1 {...rest}>{children}</TypographyH1>;
    case 'h2':
      return <TypographyH2 {...rest}>{children}</TypographyH2>;
    case 'h3':
      return <TypographyH3 {...rest}>{children}</TypographyH3>;
    case 'h4':
      return <TypographyH3 {...rest}>{children}</TypographyH3>;
    // etc for other heading levels
    case 'p':
      return <TypographyP {...rest}>{children}</TypographyP>;
    case 'blockquote':
      return <TypographyBlockquote {...rest}>{children}</TypographyBlockquote>;
    case 'ul':
      return <TypographyList {...rest}>{children}</TypographyList>;
    case 'code':
      return <TypographyInlineCode {...rest}>{children}</TypographyInlineCode>;
    case 'lead':
      return <TypographyLead {...rest}>{children}</TypographyLead>;
    case 'large':
      return <TypographyLarge {...rest}>{children}</TypographyLarge>;
    case 'small':
      return <TypographySmall {...rest}>{children}</TypographySmall>;
    case 'muted':
      return <TypographyMuted {...rest}>{children}</TypographyMuted>;
    default:
      return <TypographyP {...rest}>{children}</TypographyP>;
  }
}
