import React from 'react';

import { Post } from '@/types/post.types';
import PostItem from '@/components/posts/post-item';
import EmptyState from '@/components/shared/empty-state';

type BookmarksGridProps = {
  posts: Post[];
};

export default function BookmarksGrid({ posts }: BookmarksGridProps) {
  if (!posts.length) {
    return (
      <EmptyState
        title="No Bookmarks"
        description="You have not bookmarked any posts yet."
      />
    );
  }

  return (
    <div className="grid gap-10 lg:grid-cols-2 xl:grid-cols-3">
      {posts.map((post) => (
        <PostItem key={post.id} post={post} isBookmarked={true} />
      ))}
    </div>
  );
}
