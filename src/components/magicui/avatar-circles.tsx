'use client';

import Image from 'next/image';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const avatarCircleVariants = cva('', {
  variants: {
    size: {
      lg: 'h-16 w-16',
      md: 'h-10 w-10',
      sm: 'h-8 w-8',
      xs: 'h-6 w-6',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

const spacingVariants = cva('', {
  variants: {
    size: {
      lg: '-space-x-6',
      md: '-space-x-4',
      sm: '-space-x-3',
      xs: '-space-x-2',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

interface Avatar {
  imageUrl: string;
  profileUrl: string;
}

interface AvatarCirclesProps extends VariantProps<typeof avatarCircleVariants> {
  className?: string;
  numPeople?: number;
  avatarUrls: Avatar[];
}

export const AvatarCircles = ({
  numPeople,
  className,
  avatarUrls,
  size,
}: AvatarCirclesProps) => {
  const dimensions =
    size === 'lg' ? 64 : size === 'sm' ? 32 : size === 'xs' ? 24 : 40;

  return (
    <div
      className={cn(
        'z-10 flex rtl:space-x-reverse',
        spacingVariants({ size }),
        className
      )}
    >
      {avatarUrls.map((url, index) => (
        <a
          key={index}
          href={url.profileUrl}
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            key={index}
            className={cn(
              avatarCircleVariants({ size }),
              'rounded-full border-2 border-white dark:border-gray-800'
            )}
            src={url.imageUrl}
            width={dimensions}
            height={dimensions}
            alt={`Avatar ${index + 1}`}
          />
        </a>
      ))}
      {(numPeople ?? 0) > 0 && (
        <a
          className={cn(
            avatarCircleVariants({ size }),
            'flex items-center justify-center rounded-full border-2 border-white bg-black text-center text-xs font-medium text-white hover:bg-gray-600 dark:border-gray-800 dark:bg-white dark:text-black'
          )}
          href=""
        >
          +{numPeople}
        </a>
      )}
    </div>
  );
};
