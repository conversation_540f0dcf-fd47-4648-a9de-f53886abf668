'use client';

import React from 'react';
import { useQueryState } from 'nuqs';

import { SessionUser } from '@/types/user.types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AppearanceForm } from '@/components/appearance/appearance-form';
import { NotificationsForm } from '@/components/notifications/notification-form';
import { ProfileForm } from '@/components/profile/profile-form';

interface SettingsTabsProps {
  user: SessionUser;
  initialTab: string;
}
export default function SettingsTabs({ user, initialTab }: SettingsTabsProps) {
  const [tab, setTab] = useQueryState('tab', {
    defaultValue: initialTab,
    clearOnDefault: false,
  });

  return (
    <Tabs defaultValue={tab} onValueChange={setTab}>
      <TabsList className="mb-4">
        <TabsTrigger value="profile">Profile</TabsTrigger>
        <TabsTrigger value="notifications">Notifications</TabsTrigger>
        <TabsTrigger value="appearance">Appearance</TabsTrigger>
      </TabsList>
      <TabsContent value="profile">
        <ProfileForm data={user || null} />
      </TabsContent>
      <TabsContent value="notifications">
        <NotificationsForm />
      </TabsContent>

      <TabsContent value="appearance">
        <AppearanceForm />
      </TabsContent>
    </Tabs>
  );
}
