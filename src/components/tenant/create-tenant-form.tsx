'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { createTenant } from '@/actions/tenant/create-tenant.action';
import {
  createTenantSchema,
  type CreateTenantValues,
} from '@/schemas/tenant.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export function CreateTenantForm() {
  const router = useRouter();
  const [isPending, startTransition] = React.useTransition();

  const form = useForm<CreateTenantValues>({
    resolver: zodResolver(createTenantSchema),
    defaultValues: {
      name: '',
      description: '',
      subdomain: '',
      logo: '',
    },
  });

  function onSubmit(data: CreateTenantValues) {
    startTransition(async () => {
      try {
        await createTenant(data);
        toast.success('Organization created successfully');
        router.push('/dashboard');
        router.refresh();
      } catch (error) {
        if (error instanceof Error) {
          toast.error(error.message);
        } else {
          toast.error('Something went wrong');
        }
      }
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Organization Name</FormLabel>
              <FormControl>
                <Input placeholder="Acme Inc." {...field} />
              </FormControl>
              <FormDescription>
                This is your organization&apos;s name as it will appear
                throughout the app.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="subdomain"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subdomain</FormLabel>
              <FormControl>
                <Input placeholder="acme" {...field} />
              </FormControl>
              <FormDescription>
                This will be your unique subdomain (e.g., acme.example.com)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us about your organization..."
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A brief description of your organization.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <EnhancedButton type="submit" isLoading={isPending}>
          Create Organization
        </EnhancedButton>
      </form>
    </Form>
  );
}
