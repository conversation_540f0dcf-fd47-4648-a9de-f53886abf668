'use client';

import { useState } from 'react';
import { clearAllNotifications } from '@/actions/notifications/clear-all-notifications.action';
import { deleteNotification } from '@/actions/notifications/delete-notification.action';
import { markAsReadNotification } from '@/actions/notifications/mark-as-read-notification.action';
import { NotificationType } from '@prisma/client';
import { Bell, Check, MoreVertical, Trash } from 'lucide-react';
import { toast } from 'sonner';

import { Notification } from '@/types/notification.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const getNotificationAction = (type: NotificationType) => {
  switch (type) {
    case NotificationType.COMMENT:
      return 'commented on your post';
    case NotificationType.LIKE_COMMENT:
      return 'liked your comment';
    case NotificationType.LIKE_POST:
      return 'liked your post';
    case NotificationType.REPLY:
      return 'replied to your comment';
    case NotificationType.SUBSCRIPTION_PURCHASE:
      return 'purchased a subscription';
    case NotificationType.ANNOUNCEMENT:
      return 'New announcement';
  }
};

const getUserName = (from: Notification['from']) => {
  return from.username || from.name || from.email || 'Unknown User';
};

const getDateGroup = (date: Date) => {
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays <= 7) return 'This Week';
  if (diffDays <= 30) return 'This Month';
  return 'Older';
};

type NotificationProps = {
  initialData: Notification[];
};

export default function Notifications({ initialData }: NotificationProps) {
  const [notifications, setNotifications] =
    useState<Notification[]>(initialData);
  const [selectedNotification, setSelectedNotification] =
    useState<Notification | null>(null);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === 'unread') return !notification.isRead;
    if (filter === 'read') return notification.isRead;
    return true;
  });

  const groupedNotifications = filteredNotifications.reduce(
    (acc, notification) => {
      const group = getDateGroup(notification.createdAt);
      if (!acc[group]) {
        acc[group] = [];
      }
      acc[group].push(notification);
      return acc;
    },
    {} as Record<string, Notification[]>
  );

  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter((n) => !n.isRead);
    if (unreadNotifications.length === 0) return;

    // Optimistically update UI
    setNotifications(notifications.map((n) => ({ ...n, isRead: true })));

    try {
      // Update all notifications in parallel
      await Promise.all(
        unreadNotifications.map((n) => markAsReadNotification(n.id))
      );
    } catch (error) {
      // Rollback on error
      console.error('Failed to mark all notifications as read:', error);
      setNotifications(notifications); // Restore original state
      toast.error('Failed to update notifications');
    }
  };

  const markAsRead = async (id: string) => {
    const notification = notifications.find((n) => n.id === id);
    if (!notification || notification.isRead) return;

    // Optimistically update UI
    setNotifications(
      notifications.map((n) => (n.id === id ? { ...n, isRead: true } : n))
    );

    try {
      await markAsReadNotification(id);
    } catch (error) {
      // Rollback on error
      console.error('Failed to mark notification as read:', error);
      setNotifications(
        notifications.map((n) => (n.id === id ? { ...notification } : n))
      );
      toast.error('Failed to update notification');
    }
  };

  const handleDeleteNotification = async (id: string) => {
    const notificationToDelete = notifications.find((n) => n.id === id);
    if (!notificationToDelete) return;

    // Optimistically update UI
    setNotifications(notifications.filter((n) => n.id !== id));
    if (selectedNotification?.id === id) {
      setSelectedNotification(null);
    }

    try {
      await deleteNotification(id);
      toast.success('Notification deleted successfully');
    } catch (error) {
      // Rollback on error
      console.error('Failed to delete notification:', error);
      setNotifications((prev) => [...prev, notificationToDelete]);
      if (selectedNotification?.id === id) {
        setSelectedNotification(notificationToDelete);
      }
      toast.error('Failed to delete notification');
    }
  };

  const handleClearAllNotifications = async () => {
    const previousNotifications = [...notifications];

    // Optimistically update UI
    setNotifications([]);
    setSelectedNotification(null);

    try {
      const result = await clearAllNotifications();
      if (!result.success) {
        throw new Error('Failed to clear notifications');
      }
    } catch (error) {
      // Rollback on error
      console.error('Failed to clear all notifications:', error);
      setNotifications(previousNotifications);
      toast.error('Failed to clear notifications');
    }
  };

  if (notifications.length === 0) {
    return (
      <EmptyState
        title="No notifications"
        description="You have no notifications at this time."
      />
    );
  }

  return (
    <>
      <div className="flex justify-between items-center py-2">
        <Select
          value={filter}
          onValueChange={(value: 'all' | 'unread' | 'read') => setFilter(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter notifications" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="unread">Unread</SelectItem>
            <SelectItem value="read">Read</SelectItem>
          </SelectContent>
        </Select>
        <div className="space-x-2">
          <EnhancedButton variant="outline" onClick={markAllAsRead}>
            <Check className="mr-2 h-4 w-4" /> Mark all as read
          </EnhancedButton>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <EnhancedButton variant="ghost">
                <Trash className="mr-2 h-4 w-4" /> Delete All
              </EnhancedButton>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete all
                  your notifications.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleClearAllNotifications}>
                  Yes, delete all
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
      <ScrollArea className="h-[400px] pr-4">
        {Object.keys(groupedNotifications).length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-4">
            <p className="text-sm text-muted-foreground">
              No notifications found for the selected filter
            </p>
          </div>
        ) : (
          Object.entries(groupedNotifications).map(([group, notifications]) => (
            <div key={group} className="mb-6">
              <h3 className="mb-3 text-sm font-medium text-muted-foreground">
                {group}
              </h3>
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start space-x-4 p-4 rounded-lg bg-card transition-opacity ${
                      notification.isRead ? 'opacity-60' : 'opacity-100'
                    }`}
                  >
                    {notification.type === NotificationType.ANNOUNCEMENT ? (
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Bell className="h-5 w-5 text-primary" />
                      </div>
                    ) : (
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={
                            notification.from.image ||
                            getDeterministicAvatar(notification.from.id)
                          }
                          alt={getUserName(notification.from)}
                        />
                        <AvatarFallback>
                          {getUserName(notification.from).charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div className="flex-grow space-y-1">
                      <div className="flex items-center justify-between">
                        <span
                          className={`${notification.isRead ? 'font-normal' : 'font-semibold'}`}
                        >
                          {notification.type === NotificationType.ANNOUNCEMENT
                            ? 'System'
                            : getUserName(notification.from)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {notification.createdAt.toLocaleTimeString()}
                        </span>
                      </div>
                      <p
                        className={`text-sm ${notification.isRead ? 'font-normal' : 'font-medium'}`}
                      >
                        {getNotificationAction(notification.type)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {notification.content}
                      </p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <EnhancedButton variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </EnhancedButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedNotification(notification);
                            if (!notification.isRead)
                              markAsRead(notification.id);
                          }}
                        >
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleDeleteNotification(notification.id)
                          }
                        >
                          Delete
                        </DropdownMenuItem>
                        {!notification.isRead && (
                          <DropdownMenuItem
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </ScrollArea>

      {selectedNotification && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle>
              {getNotificationAction(selectedNotification.type)}
            </CardTitle>
            <CardDescription>
              {selectedNotification.createdAt.toLocaleString()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-4">
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={
                    selectedNotification.from.image ||
                    getDeterministicAvatar(selectedNotification.from.id)
                  }
                  alt={getUserName(selectedNotification.from)}
                />
                <AvatarFallback>
                  {getUserName(selectedNotification.from).charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">
                  {getUserName(selectedNotification.from)}
                </p>
                <p className="text-sm text-muted-foreground">
                  {getNotificationAction(selectedNotification.type)}
                </p>
              </div>
            </div>
            <p>{selectedNotification.content}</p>
            {selectedNotification.post && (
              <div className="mt-4">
                <p className="font-medium">Related Post:</p>
                <p className="text-sm text-muted-foreground">
                  {selectedNotification.post.title}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
