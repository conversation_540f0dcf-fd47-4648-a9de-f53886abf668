import Link from 'next/link';
import { FaGithub } from 'react-icons/fa';

import DocsSearch from '@/components/docs/search';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function DocsActions() {
  return (
    <div className="hidden md:flex flex-1 items-center justify-between space-x-2 md:justify-end relative">
      <div className="w-full flex-1 md:w-auto md:flex-none">
        <DocsSearch />
      </div>
      <nav className="flex items-center">
        <EnhancedButton variant="ghost" size="icon" asChild>
          <Link
            href={`https://github.com/Ricka7x/next-saas-boilerplate`}
            target="_blank"
          >
            <span className="sr-only">GitHub</span>
            <FaGithub className="h-5 w-5" />
          </Link>
        </EnhancedButton>
      </nav>
    </div>
  );
}
