'use client';

import { useSession } from 'next-auth/react';

import { SessionUser } from '@/types/user.types';
import { UserDropdown } from '@/components/navigation/user-dropdown';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export function UserNav() {
  const session = useSession();
  const user = session.data?.user;

  if (!user) {
    return null;
  }

  return (
    <UserDropdown
      TriggerButton={EnhancedButton}
      user={user as SessionUser}
      triggerClassName="relative h-8 w-8 rounded-full"
    />
  );
}
