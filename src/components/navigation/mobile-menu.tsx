'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { AnimatePresence, motion } from 'framer-motion';
import { Menu, X } from 'lucide-react';

import { useScrollLock } from '@/hooks/use-scroll-lock';
import DocsSearch from '@/components/docs/search';
import Container from '@/components/layout/container';
import { EnhancedButton } from '@/components/shared/enhanced-button';

import { NavContext, NavItems } from './nav';

const menuVariants = {
  hidden: { opacity: 0, y: -20 },
  visible: { opacity: 1, y: 0 },
};

export default function MobileMenu({
  context,
  navItems,
  pathname,
}: {
  context: NavContext;
  navItems: NavItems;
  pathname: string;
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useScrollLock(isMenuOpen);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const renderActions = () => {
    switch (context) {
      case 'docs':
        return (
          <>
            <div className="px-2 pb-3 pt-8">
              <DocsSearch />
            </div>
          </>
        );

      default:
        return (
          <EnhancedButton className="w-full" asChild>
            <Link href="/signin">Sign In</Link>
          </EnhancedButton>
        );
    }
  };

  return (
    <>
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="fixed top-20 left-0 right-0 botton-0 z-50 md:hidden bg-background shadow-md h-screen"
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={menuVariants}
          >
            <Container>
              <div className="space-y-1 px-2 pb-3 pt-2 ">
                {navItems[context].map((item) => {
                  const isActive =
                    pathname === item.href ||
                    pathname.split('/')[1] === item.href.split('/')[1];
                  return (
                    <motion.div
                      key={item.title}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        href={item.href}
                        className={`block rounded-md px-3 py-2 text-base font-medium ${
                          isActive
                            ? 'bg-accent text-primary'
                            : 'text-foreground/80'
                        }`}
                      >
                        {item.title}
                      </Link>
                    </motion.div>
                  );
                })}
              </div>
              {renderActions()}
            </Container>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="flex md:hidden">
        <motion.button
          type="button"
          className="text-muted-foreground hover:text-gray-600"
          onClick={toggleMenu}
          aria-label="Toggle menu"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          {isMenuOpen ? (
            <X className="h-6 w-6" aria-hidden="true" />
          ) : (
            <Menu className="h-6 w-6" aria-hidden="true" />
          )}
        </motion.button>
      </div>
    </>
  );
}
