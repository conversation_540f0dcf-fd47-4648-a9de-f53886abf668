'use client';

import React from 'react';

import { SessionUser } from '@/types/user.types';

import NotificationButton from '../notifications/notification-button';
import { EnhancedButton } from '../shared/enhanced-button';
import { ThemeToggle } from './theme-toggle';
import { UserDropdown } from './user-dropdown';

export default function TopBarButtons({ user }: { user: SessionUser }) {
  return (
    <div className="flex items-center gap-2">
      <NotificationButton />
      <ThemeToggle />
      <UserDropdown
        TriggerButton={EnhancedButton}
        user={user as SessionUser}
        triggerClassName="relative h-8 w-8 rounded-full"
      />
    </div>
  );
}
