'use client';

import Link from 'next/link';
import { Role } from '@prisma/client';
import { Bell, BookOpen, CreditCard, Flame, LogOut, User } from 'lucide-react';

import { SessionUser } from '@/types/user.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { getInitials, renderAvatar, renderUserName } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import Signout from '../auth/sign-out';

interface UserDropdownProps {
  user: SessionUser;
  triggerClassName?: string;
  contentClassName?: string;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  TriggerButton: React.ComponentType<any>;
}

export function UserDropdown({
  user,
  triggerClassName,
  contentClassName = 'w-56',
  align = 'end',
  side = 'bottom',
  TriggerButton,
}: UserDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <TriggerButton className={triggerClassName}>
          <Avatar className="h-8 w-8 rounded-lg">
            <AvatarImage src={renderAvatar(user)} alt={renderUserName(user)} />
            <AvatarFallback className="rounded-lg">
              {getInitials(renderUserName(user))}
            </AvatarFallback>
          </Avatar>
        </TriggerButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className={contentClassName}
        side={side}
        align={align}
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="h-8 w-8 rounded-lg">
              <AvatarImage
                src={user.image || getDeterministicAvatar(user.id!)}
                alt={user.name || user.email || 'U'}
              />
              <AvatarFallback className="rounded-lg">
                {getInitials(renderUserName(user))}
              </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">
                {renderUserName(user)}
              </span>
              <span className="text-muted-foreground truncate text-xs">
                {user.email}
              </span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/blog" className="cursor-pointer">
              <BookOpen className="text-muted-foreground w-4 h-4 mr-2" />
              Blog
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/upgrade" className="cursor-pointer">
              <Flame className="text-muted-foreground w-4 h-4 mr-2" />
              Upgrade
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/settings" className="cursor-pointer">
              <User className="text-muted-foreground w-4 h-4 mr-2" />
              Account
            </Link>
          </DropdownMenuItem>
          {user.role === Role.ADMIN && (
            <DropdownMenuItem asChild>
              <Link href="/billing" className="cursor-pointer">
                <CreditCard className="text-muted-foreground w-4 h-4 mr-2" />
                Billing
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem asChild>
            <Link href="/notifications" className="cursor-pointer">
              <Bell className="text-muted-foreground w-4 h-4 mr-2" />
              Notifications
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />

        <DropdownMenuItem>
          <Signout className="w-full flex items-center cursor-pointer">
            <LogOut className="text-muted-foreground w-4 h-4 mr-2" />
            <span>Sign out</span>
          </Signout>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
