'use client';

import { usePathname } from 'next/navigation';
import { landingConfig } from '@/config';
import { MainNavItem } from '@/types';

import { SessionUser } from '@/types/user.types';

import DocsActions from './docs-actions';
import Logo from './logo';
import DesktopMenu from './menu';
import MobileMenu from './mobile-menu';
import { ThemeToggle } from './theme-toggle';
import { UserNav } from './user-nav';

interface NavProps {
  user?: SessionUser;
}

export type NavContext = 'landing' | 'docs' | 'blog';
export type NavItems = {
  [key in NavContext]: MainNavItem[];
};
const navItems = {
  landing: landingConfig.mainNav,
  docs: landingConfig.mainNav,
  app: [],
  blog: [],
};

export default function Nav({ user }: NavProps) {
  const pathname = usePathname();
  let context: NavContext = 'landing';

  if (pathname.startsWith('/docs')) {
    context = 'docs';
  }

  return (
    <div className="mx-auto flex  items-center justify-between py-3">
      <div className="flex items-center space-x-4">
        <Logo />

        {/* Menus */}

        <DesktopMenu
          context={context}
          pathname={pathname}
          navItems={navItems}
        />
      </div>

      {/* Right side buttons */}
      <div className="flex items-center space-x-4 ">
        {context === 'docs' && <DocsActions />}

        {user && (
          <div className="hidden md:flex">
            <UserNav />
          </div>
        )}

        {!user && (
          <div className="hidden md:flex">
            <a
              href="/signin"
              className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90"
            >
              Sign In
            </a>
          </div>
        )}

        <div className="hidden md:flex">
          <ThemeToggle />
        </div>
      </div>
      <MobileMenu context={context} pathname={pathname} navItems={navItems} />
    </div>
  );
}
