import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTheme } from 'next-themes';

const Logo = () => {
  const { theme, resolvedTheme } = useTheme();
  const [currentTheme, setCurrentTheme] = useState('system');

  useEffect(() => {
    setCurrentTheme(resolvedTheme || theme || 'system');
  }, [theme, resolvedTheme]);

  return (
    <Link className="mr-6 flex items-center space-x-2" href="/">
      <div style={{ width: '200px', height: '40px', position: 'relative' }}>
        <Image
          src={
            currentTheme === 'dark'
              ? '/saastarter-logo-white.svg'
              : '/saastarter-logo-black.svg'
          }
          alt="Logo"
          priority
          fill
          style={{
            objectFit: 'contain',
          }}
        />
      </div>
    </Link>
  );
};

export default Logo;
