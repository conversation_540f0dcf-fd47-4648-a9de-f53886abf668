import Link from 'next/link';

import { NavContext, NavItems } from './nav';

export default function DesktopMenu({
  context,
  navItems,
  pathname,
}: {
  context: NavContext;
  navItems: NavItems;
  pathname: string;
}) {
  return (
    <div className="hidden md:flex md:items-center md:space-x-1">
      {navItems[context].map((item) => {
        const isActive =
          pathname === item.href ||
          pathname.split('/')[1] === item.href.split('/')[1];
        return (
          <div key={item.title} className="relative px-1 py-2">
            <Link
              href={item.href || '#'}
              className={`relative z-10 px-3 py-2 text-sm font-medium transition-colors ${
                isActive ? 'text-primary' : 'text-primary/70 hover:text-primary'
              }`}
            >
              {item.title}
            </Link>
          </div>
        );
      })}
    </div>
  );
}
