'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useSettingsStore } from '@/stores/settings-store';
import { useTenantStore } from '@/stores/tenant-store';
import { Settings } from '@prisma/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SessionProvider } from 'next-auth/react';
import { AbstractIntlMessages, NextIntlClientProvider } from 'next-intl';
import { ThemeProvider } from 'next-themes';
import { NuqsAdapter } from 'nuqs/adapters/react';

import { Tenant } from '@/types/tenant.types';
import { cn } from '@/lib/utils';
import { SidebarProvider } from '@/components/ui/sidebar';

const queryClient = new QueryClient();
type SessionProviderProps = {
  children: React.ReactNode;
  settings: Settings | null;
  messages: AbstractIntlMessages | undefined;
  locale: string;
  initialTenant: Tenant | null;
  tenants: Tenant[];
};

export default function Providers({
  children,
  settings,
  messages,
  locale,
  initialTenant,
  tenants,
}: SessionProviderProps) {
  const pathname = usePathname() || '/'; // Fallback

  const isDashboard = pathname === '/onboarding';

  // Initialize settings and tenant store (Zustand)
  useEffect(() => {
    try {
      useSettingsStore.setState({ settings });
      useTenantStore.setState({
        currentTenant: initialTenant,
        availableTenants: tenants,
      });
    } catch (error) {
      console.error('Error initializing Zustand stores:', error);
    }
  }, [settings, initialTenant, tenants]);

  return (
    <SessionProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        storageKey="theme"
      >
        <QueryClientProvider client={queryClient}>
          <NextIntlClientProvider messages={messages} locale={locale || 'en'}>
            <NuqsAdapter>
              <SidebarProvider className={cn({ block: isDashboard })}>
                {children}
              </SidebarProvider>
            </NuqsAdapter>
          </NextIntlClientProvider>
        </QueryClientProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}
