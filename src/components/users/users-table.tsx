'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import Image from 'next/image';
import { getUsers } from '@/actions/users';
import { changeUserRole } from '@/actions/users/change-user-role.action';
import { deleteUser } from '@/actions/users/delete-user.action';
import { toggleUserStatus } from '@/actions/users/toggle-user-status.action';
import { Role } from '@prisma/client';
import { useQuery } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { toast } from 'sonner';

import { GetPaginatedResult } from '@/types/shared.types';
import { User } from '@/types/user.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { capitalize } from '@/lib/utils';
import { useTable } from '@/hooks/use-table';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import EmptyState from '@/components/shared/empty-state';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';
import { UserActions } from '@/components/users/user-actions';

type UsersTableProps = {
  initialData: GetPaginatedResult<User>;
};
export function UsersTable({ initialData }: UsersTableProps) {
  const isInitialMount = useRef(true);

  const {
    data: users,
    setData: setUsers,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sortBy,
    sorting,
    onSortChange,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<User>({ initialData });
  const [isPending, startTransition] = useTransition();

  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isToggleStatusConfirmOpen, setIsToggleStatusConfirmOpen] =
    useState(false);
  const [isChangeRoleConfirmOpen, setIsChangeRoleConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [userToToggle, setUserToToggle] = useState<User | null>(null);
  const [userToChangeRole, setUserToChangeRole] = useState<User | null>(null);
  const [newRole, setNewRole] = useState<Role>(Role.COLLABORATOR);

  const columns = useMemo<ColumnDef<User>[]>(
    () => [
      {
        accessorKey: 'image',
        header: 'Image',
        enableSorting: false,
        cell: ({ row }) => (
          <div>
            <div className="relative w-12 h-12">
              <Image
                src={
                  row.original.image || getDeterministicAvatar(row.original.id)
                }
                alt={`Avatar for ${row.original.name || row.original.email}`}
                fill
                sizes="50px"
                className="rounded-sm object-cover"
              />
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'email',
        header: 'Email',
      },
      {
        accessorKey: 'role',
        header: 'Role',
        cell: ({ row }) => {
          const role = row.original.role;
          let variant: 'default' | 'secondary' | 'outline' = 'default';

          switch (role) {
            case Role.ADMIN:
              variant = 'secondary';
              break;
            case Role.COLLABORATOR:
              variant = 'outline';
              break;
            case Role.USER:
              variant = 'default';
              break;
          }

          return <Badge variant={variant}>{capitalize(role)}</Badge>;
        },
      },
      {
        accessorKey: 'isActive',
        header: 'Status',
        cell: ({ row }) => {
          const isActive = row.original.isActive;
          return (
            <Badge variant={isActive ? 'default' : 'secondary'}>
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
          );
        },
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <UserActions
            user={row.original}
            onChangeRole={handleChangeRoleClick}
            onToggleStatus={(user) => {
              setUserToToggle(user);
              setIsToggleStatusConfirmOpen(true);
            }}
            onDelete={(user) => {
              setUserToDelete(user);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: users,
    columns,
    state: {
      sorting,
    },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['users', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getUsers(defaultParams);
    },
    staleTime: 1000 * 60,
    refetchOnWindowFocus: false,
  });

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    startTransition(async () => {
      try {
        await deleteUser(userToDelete.id);
        await refetch();
        toast.success('User deleted successfully');
      } catch (error) {
        console.error('Error deleting user:', error);
        toast.error('Failed to delete user');
      } finally {
        setIsDeleteConfirmOpen(false);
        setUserToDelete(null);
      }
    });
  };

  const handleToggleUserStatus = async () => {
    if (!userToToggle) return;

    const newStatus = !userToToggle.isActive;
    const actionText = newStatus ? 'activating' : 'deactivating';
    const actionTextPast = newStatus ? 'activated' : 'deactivated';

    startTransition(async () => {
      try {
        await toggleUserStatus(userToToggle.id);
        await refetch();
        toast.success(`User ${actionTextPast} successfully`);
      } catch (error) {
        console.error('Error toggling user status:', error);
        toast.error(`Failed to ${actionText} user`);
      } finally {
        setIsToggleStatusConfirmOpen(false);
        setUserToToggle(null);
      }
    });
  };

  const handleChangeRoleClick = (user: User) => {
    setUserToChangeRole(user);
    setIsChangeRoleConfirmOpen(true);
  };

  const handleChangeUserRole = async () => {
    if (!userToChangeRole) return;

    startTransition(async () => {
      try {
        await changeUserRole(userToChangeRole.id, newRole);
        await refetch();
        toast.success('User role changed successfully');
      } catch (error) {
        console.error('Error changing user role:', error);
        toast.error('Failed to change user role');
      } finally {
        setIsChangeRoleConfirmOpen(false);
        setUserToChangeRole(null);
      }
    });
  };

  useEffect(() => {
    if (result) {
      setUsers(result.items);
      setTotalItems(result.totalCount);

      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);

  return (
    <>
      {isEmpty && table.getRowModel().rows?.length === 0 ? (
        <EmptyState
          title="No Users"
          description="There are no users to display."
        />
      ) : (
        <>
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter users..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
          <div className="relative">
            {isLoading && <BluredBgSpinner />}

            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <span className="ml-2">
                                {{
                                  asc: '↑',
                                  desc: '↓',
                                }[header.column.getIsSorted() as string] ?? ''}
                              </span>
                            )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No users found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      )}

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onCancel={() => {
          setIsDeleteConfirmOpen(false);
          setUserToDelete(null);
        }}
        isLoading={isPending}
        loadingText="Deleting..."
        confirmText="Delete"
        onConfirm={handleDeleteUser}
        title="Delete User"
        description="Are you sure you want to delete this user? This action cannot be undone."
      />

      <ConfirmDialog
        isOpen={isToggleStatusConfirmOpen}
        onOpenChange={setIsToggleStatusConfirmOpen}
        onCancel={() => {
          setIsToggleStatusConfirmOpen(false);
          setUserToToggle(null);
        }}
        isLoading={isPending}
        loadingText={`${userToToggle?.isActive ? 'Deactivating' : 'Activating'}...`}
        confirmText={userToToggle?.isActive ? 'Deactivate' : 'Activate'}
        onConfirm={handleToggleUserStatus}
        title={`${userToToggle?.isActive ? 'Deactivate' : 'Activate'} User`}
        description={
          userToToggle?.isActive
            ? 'Are you sure you want to deactivate this user? They will no longer be able to access the system.'
            : 'Are you sure you want to activate this user? They will regain access to the system.'
        }
      />

      <ConfirmDialog
        isOpen={isChangeRoleConfirmOpen}
        onOpenChange={setIsChangeRoleConfirmOpen}
        onCancel={() => {
          setIsChangeRoleConfirmOpen(false);
          setUserToChangeRole(null);
        }}
        isLoading={isPending}
        loadingText="Changing..."
        confirmText="Change Role"
        onConfirm={handleChangeUserRole}
        title="Change User Role"
        description={
          <div className="space-y-4">
            <p>Select the new role for this user:</p>
            <Select
              value={newRole}
              onValueChange={(value) => setNewRole(value as Role)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Role.ADMIN}>Admin</SelectItem>
                <SelectItem value={Role.COLLABORATOR}>Collaborator</SelectItem>
                <SelectItem value={Role.USER}>User</SelectItem>
              </SelectContent>
            </Select>
          </div>
        }
      />
    </>
  );
}
