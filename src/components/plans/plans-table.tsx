'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import { createPlan, deletePlan, getPlans } from '@/actions/plans';
import { CreatePlanValues } from '@/schemas/plans.schemas';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';

import { Plan } from '@/types/plan.types';
import { GetPaginatedResult } from '@/types/shared.types';
import { calculateNewPage } from '@/lib/pagination';
import { formatDate } from '@/lib/utils';
import { useTable } from '@/hooks/use-table';
import { Modal } from '@/components/ui/modal';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AddPlanForm } from '@/components/plans/add-plan-form';
import { PlanActions } from '@/components/plans/plan-actions';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';

type PlansTableProps = {
  initialData: GetPaginatedResult<Plan>;
};

export function PlansTable({ initialData }: PlansTableProps) {
  const isInitialMount = useRef(true);
  const queryClient = useQueryClient();
  const {
    data: plans,
    setData: setPlans,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    sortBy,
    sorting,
    onSortChange,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<Plan>({ initialData });
  const [isPending, startTransition] = useTransition();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [planToDelete, setPlanToDelete] = useState<Plan | null>(null);
  const [isAddPlanDialogOpen, setIsAddPlanDialogOpen] = useState(false);

  const columns = useMemo<ColumnDef<Plan>[]>(
    () => [
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'monthlyPrice',
        header: 'Monthly Price',
        cell: ({ row }) => <span>${row.original.monthlyPrice}</span>,
      },
      {
        accessorKey: 'yearlyPrice',
        header: 'Yearly Price',
        cell: ({ row }) => <span>${row.original.yearlyPrice}</span>,
      },
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        cell: ({ row }) => (
          <span>{formatDate(row.original.createdAt.toISOString())}</span>
        ),
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <PlanActions
            plan={row.original}
            onDelete={(plan) => {
              setPlanToDelete(plan);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: plans,
    columns,
    state: { sorting },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['plans', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getPlans(defaultParams);
    },
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  const handleDelete = async () => {
    if (!planToDelete) return;
    startTransition(async () => {
      try {
        await deletePlan(planToDelete.id);
        setSearchTerm('');
        const newTotalItems = totalItems - 1;
        const newPage = calculateNewPage(page, newTotalItems, itemsPerPage);
        setPage(newPage);
        queryClient.invalidateQueries({ queryKey: ['plans'] });
        await refetch();
        toast.success('Plan deleted successfully');
      } catch (error) {
        console.error('Error deleting plan:', error);
        toast.error('Failed to delete plan');
      } finally {
        setIsDeleteConfirmOpen(false);
        setPlanToDelete(null);
      }
    });
  };

  const handleAddPlan = async (data: CreatePlanValues) => {
    startTransition(async () => {
      try {
        const features = data.features
          .split(',')
          .map((feature) => feature.trim())
          .filter(Boolean);
        await createPlan({ ...data, features });
        const newTotalItems = totalItems + 1;
        const newPage = calculateNewPage(
          page,
          newTotalItems,
          itemsPerPage,
          true
        );
        setPage(newPage);
        queryClient.invalidateQueries({ queryKey: ['plans'] });
        await refetch();
        toast.success('Plan created successfully');
        setIsAddPlanDialogOpen(false);
      } catch (error) {
        console.error('Error creating plan:', error);
        toast.error('Failed to create plan');
      }
    });
  };

  useEffect(() => {
    if (result) {
      setPlans(result.items);
      setTotalItems(result.totalCount);

      // Only update page on initial mount if it differs
      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);
  return (
    <>
      {isEmpty && table.getRowModel().rows?.length === 0 ? (
        <EmptyState
          title="No Plans"
          description="There are no plans to display."
        />
      ) : (
        <>
          {' '}
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter plans..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <EnhancedButton onClick={() => setIsAddPlanDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Plan
            </EnhancedButton>
          </div>
          <div className="relative">
            {isLoading && <BluredBgSpinner />}

            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() &&
                              header.column.getIsSorted() && (
                                <span className="ml-2">
                                  {{
                                    asc: '↑',
                                    desc: '↓',
                                  }[header.column.getIsSorted() as string] ??
                                    ''}
                                </span>
                              )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No plans found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={setPage}
        />
      )}

      <Modal
        title="Create Plan"
        description="Create a new subscription plan"
        isOpen={isAddPlanDialogOpen}
        onOpenChange={setIsAddPlanDialogOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="add-plan-form"
            isLoading={isPending}
            loadingText="Creating..."
          >
            Create Plan
          </EnhancedButton>
        }
      >
        <AddPlanForm onSubmit={handleAddPlan} />
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        isLoading={isPending}
        loadingText="Deleting..."
        onConfirm={handleDelete}
        title="Confirm Delete Plan"
        description="Are you sure you want to delete this plan? This action cannot be undone."
      />
    </>
  );
}
