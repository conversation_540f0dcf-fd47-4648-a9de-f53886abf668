'use client';

import { MoreHorizontal, Trash } from 'lucide-react';

import { Plan } from '@/types/plan.types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface PlanActionsProps {
  plan: Plan;
  onDelete: (plan: Plan) => void;
}

export function PlanActions({ plan, onDelete }: PlanActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </EnhancedButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onDelete(plan)}>
          <Trash className="mr-2 h-4 w-4" /> Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
