import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Icons } from '@/components/shared/icons';

type BillingCardProps = {
  title: string;
  price: number;
  description: string;
  features: string[];
};
export default function BillingCard({
  title,
  price,
  description,
  features,
}: BillingCardProps) {
  return (
    <Card className="flex flex-col">
      <CardContent className="">
        <p className="text-primary font-bold my-6">{title}</p>
        <h3 className="font-bold text-5xl mb-6">
          {price > 0 ? `$${price.toFixed(2)}` : 'Free'}
          {price > 0 && (
            <span className="text-gray-500 text-sm font-normal">/month</span>
          )}
        </h3>
        <p className="text-gray-500 mb-6">{description}</p>
        <ul className="mb-auto flex-grow">
          {features.map((feature, index) => (
            <li className="flex items-center" key={feature}>
              <Icons.check className="mr-2 h-4 w-4" /> {feature}
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter className="flex items-center justify-between mt-auto">
        <EnhancedButton>Subscribe to pro</EnhancedButton>
      </CardFooter>
    </Card>
  );
}
