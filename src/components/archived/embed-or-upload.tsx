'use client';

import { useCallback, useRef, useState } from 'react';
import { FancySwitch, OptionValue } from '@omit/react-fancy-switch';
import { Link, UploadCloud } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type Props = {
  onFileSelect: (file: File | string) => void;
};

export default function EmbedOrUpload({ onFileSelect }: Props) {
  const [selectedOption, setSelectedOption] = useState<OptionValue>('upload');
  const isEmbed = selectedOption === 'embed';
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const options = ['upload', 'embed'];

  const [imageUrl, setImageUrl] = useState('');

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && droppedFile.type.startsWith('image/')) {
      onFileSelect(droppedFile);
    }
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && selectedFile.type.startsWith('image/')) {
      onFileSelect(selectedFile);
    }
  };

  const handleEmbedImage = () => {
    if (imageUrl) {
      onFileSelect(imageUrl);
    }
  };

  return (
    <div className=" w-full max-w-md mx-auto space-y-6">
      <div className="flex justify-center">
        <FancySwitch
          options={options}
          value={selectedOption}
          onChange={setSelectedOption}
          className="inline-flex rounded-full bg-muted p-2"
          highlighterClassName="bg-primary rounded-full"
          radioClassName={cn(
            'relative mx-2 flex h-9 cursor-pointer items-center justify-center rounded-full px-3.5 text-sm font-medium transition-colors focus:outline-none data-[checked]:text-primary-foreground'
          )}
          highlighterIncludeMargin={true}
        />
      </div>

      <div
        className={cn(
          'border-2 border-dashed border-muted-foreground rounded-lg p-6 text-center h-[250px] flex flex-col justify-center items-center',
          { 'bg-gray-200': isDragging }
        )}
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onDragEnter={() => setIsDragging(true)}
        onDragLeave={() => setIsDragging(false)}
      >
        {isEmbed ? (
          <div className="space-y-4 w-full">
            <Link className="mx-auto h-8 w-8 text-muted-foreground" />
            <div className="space-y-2 w-full">
              <Label htmlFor="image-url" className="sr-only">
                Image URL
              </Label>
              <Input
                id="image-url"
                placeholder="Paste the image link"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full max-w-sm mx-auto"
              />
              <EnhancedButton
                onClick={handleEmbedImage}
                className="w-full max-w-sm mx-auto"
              >
                Embed image
              </EnhancedButton>
            </div>
          </div>
        ) : (
          <div className="space-y-4 w-full">
            <UploadCloud className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Drag and drop an image here, or click to select an image
            </p>
            <Input
              ref={fileInputRef}
              id="file-upload"
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept="image/*"
            />
            <EnhancedButton
              onClick={() => fileInputRef.current?.click()}
              className="w-full max-w-sm mx-auto"
            >
              Select an image
            </EnhancedButton>
          </div>
        )}
      </div>
    </div>
  );
}
