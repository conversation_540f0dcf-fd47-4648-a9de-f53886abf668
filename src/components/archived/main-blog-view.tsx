import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Bookmark, MessageCircle, MoreHorizontal } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import { EnhancedButton } from '@/components/shared/enhanced-button';

export default function MainBlogView() {
  const blogPosts = [
    {
      author: '<PERSON><PERSON><PERSON>',
      publication: 'Data Engineer',
      title: 'Count(*) vs Count(1) in SQL',
      excerpt:
        "If you've spent any time writing SQL queries, you've probably seen both `COUNT(*)` and `COUNT(1)` used to count rows in ...",
      date: 'Mar 8',
      readTime: '1.2K',
      comments: 32,
      image:
        'https://doodleipsum.com/1200x800/hand-drawn?bg=D96363&i=58da86eb7517ea5d67d5a975bb2ba8a8',
    },
    {
      author: '<PERSON>',
      title: 'Async Await Is The Worst Thing To Happen To Programming',
      excerpt: 'I recently saw this meme about async and await.',
      date: 'Jun 21',
      readTime: '3.2K',
      comments: 192,
      image:
        'https://doodleipsum.com/1200x800/hand-drawn?bg=D96363&i=58da86eb7517ea5d67d5a975bb2ba8a8',
    },
    {
      author: 'Gao Dalie (高大烈)',
      publication: 'Towards AI',
      title:
        'Why Llama 3.1 405B Is So Much Better Than GPT-4o And Claude 3.5 Sonnet— Here The Result',
      excerpt:
        'the AI news in the past 7 days has been insane, with so much happening in the world of AI',
      date: 'Jul 26',
      readTime: '1.6K',
      comments: 10,
      image:
        'https://doodleipsum.com/1200x800/hand-drawn?bg=D96363&i=58da86eb7517ea5d67d5a975bb2ba8a8',
    },
    {
      author: 'Mike Shakhomirov',
      publication: 'Towards Data Science',
      title: 'Advanced SQL for Data Science',
      excerpt: 'Expert techniques to elevate your analysis',
      date: 'Aug 23',
      readTime: '685',
      comments: 2,
      image:
        'https://doodleipsum.com/1200x800/hand-drawn?bg=D96363&i=58da86eb7517ea5d67d5a975bb2ba8a8',
    },
  ];

  const topPicks = [
    {
      author: 'Marianne Bellotti',
      title:
        'Three Critical Questions to Turn the Tables During Technical Interviews',
    },
    {
      author: 'Jeffrey Harvey',
      publication: 'The Riff',
      title: 'When the Music of Our Lives Starts to Fade',
    },
    {
      author: 'The Medium Newsletter',
      publication: 'The Medium Blog',
      title: 'English is three languages wearing a trench coat',
    },
  ];

  const recentlyAdded = [
    {
      author: 'Marianne Bellotti',
      title:
        'Three Critical Questions to Turn the Tables During Technical Interviews',
    },
    {
      author: 'Jeffrey Harvey',
      publication: 'The Riff',
      title: 'When the Music of Our Lives Starts to Fade',
    },
    {
      author: 'The Medium Newsletter',
      publication: 'The Medium Blog',
      title: 'English is three languages wearing a trench coat',
    },
  ];

  const recommendedTopics = [
    'Data Science',
    'Self Improvement',
    'Technology',
    'Writing',
    'Relationships',
    'Politics',
    'Cryptocurrency',
  ];

  return (
    <div className="">
      {/* <header className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Medium</h1>
        <div className="flex items-center gap-4">
          <EnhancedButton variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </EnhancedButton>
          <EnhancedButton variant="ghost" size="icon">
            <PenSquare className="h-5 w-5" />
          </EnhancedButton>
          <Avatar>
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
        </div>
      </header> */}

      {/* <>
        <nav className="mb-8">
          <ul className="flex gap-4 text-sm">
            <li className="font-bold">For you</li>
            <li className="text-gray-500">Following</li>
            <li className="text-gray-500">Visual Design</li>
            <li className="text-gray-500">Gaming</li>
            <li className="text-gray-500">Programming</li>
          </ul>
        </nav>
      </> */}

      <div className="mb-12">
        <h1 className="text-4xl font-semibold mb-4">Blog</h1>
        <p className="leading-relaxed text-gray-500 text-lg">
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Tenetur,
          ullam minima reiciendis.
        </p>
      </div>
      <div className="flex gap-12">
        <main className="w-2/3">
          {blogPosts.map((post, index) => (
            <Card
              key={index}
              className="mb-8 shadow-none rounded-none border-none"
            >
              <CardHeader className="flex flex-row items-center gap-4 pt-0">
                <Avatar className="w-8 h-8">
                  <AvatarImage
                    src={`/placeholder.svg?text=${post.author[0]}`}
                  />
                  <AvatarFallback>{post.author[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-semibold">{post.author}</p>
                </div>
              </CardHeader>
              <CardContent className="flex gap-4">
                <div className="flex-grow max-w-xl">
                  <h2 className="text-xl font-bold mb-2">{post.title}</h2>
                  <p className="text-gray-600 mb-4">{post.excerpt}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <span>{post.date}</span>
                    <span className="mx-1">·</span>
                    <span>{post.readTime} read</span>
                    {post.comments > 0 && (
                      <>
                        <span className="mx-1">·</span>
                        <MessageCircle className="w-4 h-4 mr-1" />
                        <span>{post.comments}</span>
                      </>
                    )}
                  </div>
                </div>
                <Image
                  src={post.image}
                  alt={post.title}
                  width={300}
                  height={160}
                  className="w-40 h-24 object-cover rounded flex-shrink-0"
                />
              </CardContent>
              <CardFooter className="flex justify-between pb-0">
                <div className="flex gap-2">
                  <Badge variant="secondary">SQL</Badge>
                </div>
                <div className="flex gap-2">
                  <EnhancedButton variant="ghost" size="icon">
                    <Bookmark className="h-4 w-4" />
                  </EnhancedButton>
                  <EnhancedButton variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </EnhancedButton>
                </div>
              </CardFooter>
            </Card>
          ))}
        </main>
        <aside className="w-1/3">
          <div className="mb-8">
            <h2 className="text-lg font-bold mb-4">Staff Picks</h2>
            {topPicks.map((pick, index) => (
              <div key={index} className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage
                      src={`/placeholder.svg?text=${pick.author[0]}`}
                    />
                    <AvatarFallback>{pick.author[0]}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-semibold">{pick.author}</span>
                </div>
                <h3 className="text-base font-bold">{pick.title}</h3>
              </div>
            ))}

            <EnhancedButton
              variant="link"
              asChild
              className="p-0 mt-4 text-primary"
            >
              <Link href="#">See the full list</Link>
            </EnhancedButton>
          </div>
          <div>
            <h2 className="text-lg font-bold mb-4">Recommended topics</h2>
            <div className="flex flex-wrap gap-2">
              {recommendedTopics.map((topic, index) => (
                <Badge key={index} variant="secondary">
                  {topic}
                </Badge>
              ))}
            </div>
            <EnhancedButton
              variant="link"
              asChild
              className="p-0 mt-4 text-primary"
            >
              <Link href="#">See more topics</Link>
            </EnhancedButton>
          </div>
          <div className="mb-8 mt-8">
            <h2 className="text-lg font-bold mb-4">Recently added</h2>
            {recentlyAdded.map((pick, index) => (
              <div key={index} className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage
                      src={`/placeholder.svg?text=${pick.author[0]}`}
                    />
                    <AvatarFallback>{pick.author[0]}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-semibold">{pick.author}</span>
                </div>
                <h3 className="text-base font-bold">{pick.title}</h3>
              </div>
            ))}

            <EnhancedButton
              variant="link"
              asChild
              className="p-0 mt-4 text-primary"
            >
              <Link href="#">See the full list</Link>
            </EnhancedButton>
          </div>
        </aside>
      </div>
    </div>
  );
}
