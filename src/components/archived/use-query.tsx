import { useCallback, useEffect, useState } from 'react';

//TODO: DELETE THIS FILE AFTER CHECKING NOTIFICATION CONTEXT WORKS

// Define a type for the query function
export type QueryFunction<TData, TParams> = (params: TParams) => Promise<TData>;

// Define the return type of the useQuery hook
interface QueryResult<TData, TParams> {
  data: TData | null;
  isLoading: boolean;
  error: Error | null;
  refetch: (newParams?: Partial<TParams>) => void;
}

function useQuery<TData, TParams = {}>(
  queryFn: QueryFunction<TData, TParams>,
  initialParams: TParams = {} as TParams
): QueryResult<TData, TParams> {
  const [data, setData] = useState<TData | null>(null);
  const [params, setParams] = useState<TParams>(initialParams);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (currentParams: TParams) => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await queryFn(currentParams);
        setData(result);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('An unknown error occurred')
        );
      } finally {
        setIsLoading(false);
      }
    },
    [queryFn]
  );

  useEffect(() => {
    fetchData(params);
  }, [fetchData, params]);

  const refetch = useCallback(
    (newParams?: Partial<TParams>) => {
      if (newParams) {
        setParams((prevParams) => ({ ...prevParams, ...newParams }));
      } else {
        fetchData(params);
      }
    },
    [fetchData, params]
  );

  return { data, isLoading, error, refetch };
}

export default useQuery;
