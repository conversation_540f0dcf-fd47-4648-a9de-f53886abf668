'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

const tags = [
  'React',
  'Next.js',
  'TypeScript',
  'JavaScript',
  'Tailwind CSS',
  'Node.js',
  'Express',
  'MongoDB',
  'PostgreSQL',
  'GraphQL',
  'REST API',
  'Docker',
  'Kubernetes',
  'AWS',
  'Azure',
  'Firebase',
  'Redux',
  'Zustand',
  'React Query',
  'Styled Components',
];

export default function TagCarousel() {
  const [width, setWidth] = useState(0);
  const carousel = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (carousel.current) {
      setWidth(carousel.current.scrollWidth - carousel.current.offsetWidth);
    }
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-4 overflow-hidden relative">
      <motion.div ref={carousel} className="cursor-grab active:cursor-grabbing">
        <motion.div
          drag="x"
          dragConstraints={{ right: 0, left: -width }}
          className="flex"
        >
          {tags.map((tag, index) => (
            <motion.div
              key={index}
              className="min-w-max px-4 py-2 m-2 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 rounded-full whitespace-nowrap shadow-sm hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              {tag}
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
      <div className="absolute inset-y-0 left-0 w-32 bg-gradient-to-r from-white via-white to-transparent dark:from-gray-900 dark:via-gray-900 dark:to-transparent pointer-events-none" />
      <div className="absolute inset-y-0 right-0 w-32 bg-gradient-to-l from-white via-white to-transparent dark:from-gray-900 dark:via-gray-900 dark:to-transparent pointer-events-none" />
    </div>
  );
}
