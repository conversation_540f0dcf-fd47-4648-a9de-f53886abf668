'use client';

import React, { createContext, useContext, useState } from 'react';
import { updateUserSettings } from '@/actions/settings';

type Settings = {
  blogNotifications: boolean;
  subscriptionNotifications: boolean;
  marketingNotifications: boolean;
  marketingEmails: boolean;
  securityEmails: boolean;
};

type SettingsContextType = {
  settings: Settings | null;
  updateSettings: (newSettings: Partial<Settings>) => Promise<void>;
};

const SettingsContext = createContext<SettingsContextType | undefined>(
  undefined
);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider: React.FC<{
  children: React.ReactNode;
  initialData: Settings | null;
}> = ({ children, initialData }) => {
  const [settings, setSettings] = useState<Settings | null>(initialData);

  const updateSettings = async (newSettings: Partial<Settings>) => {
    const updatedSettings = await updateUserSettings(newSettings);
    setSettings(updatedSettings);
  };

  return (
    <SettingsContext.Provider value={{ settings, updateSettings }}>
      {children}
    </SettingsContext.Provider>
  );
};
