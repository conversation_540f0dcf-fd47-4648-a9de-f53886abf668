'use client';

import { useState } from 'react';
import Image from 'next/image';
import { AnimatePresence, motion } from 'framer-motion';

import { EnhancedButton } from '@/components/shared/enhanced-button';

const steps = [
  {
    title: 'Welcome to your dashboard',
    description:
      "We're glad to have you onboard. Here are some quick tips to get you up and running.",
  },
  {
    title: 'Customize your experience',
    description: 'Set up your preferences to make the most of our platform.',
  },
  {
    title: 'Start exploring',
    description:
      "You're all set! Click 'Finish' to begin using your new dashboard.",
  },
];

export default function ImprovedOnboardingModal() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isOpen, setIsOpen] = useState(true);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsOpen(false);
    }
  };

  const handleSkip = () => {
    setIsOpen(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background/80 flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-card rounded-lg shadow-xl w-full max-w-md overflow-hidden"
      >
        <div className="p-6">
          <div className="mb-6">
            <Image
              src="/placeholder.svg?height=200&width=300"
              alt="Laptop illustration"
              className="w-full h-auto"
              width={300}
              height={200}
            />
          </div>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -20, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-center mb-2">
                {steps[currentStep].title}
              </h2>
              <p className="text-center text-muted-foreground">
                {steps[currentStep].description}
              </p>
            </motion.div>
          </AnimatePresence>
          <div className="flex justify-between space-x-4">
            <EnhancedButton
              variant="outline"
              onClick={handleSkip}
              className="flex-1 py-2 text-base font-semibold"
            >
              Skip
            </EnhancedButton>
            <EnhancedButton
              onClick={handleNext}
              className="flex-1 py-2 text-base font-semibold"
            >
              {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
            </EnhancedButton>
          </div>
        </div>
        <div className="flex justify-center pb-4">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full mx-1 ${
                index === currentStep ? 'bg-primary' : 'bg-muted'
              }`}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}
