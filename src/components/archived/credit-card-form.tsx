'use client';

import { useEffect, useState } from 'react';
import { Calendar, CreditCard, Lock, User } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function Component() {
  const [cardNumber, setCardNumber] = useState('•••• •••• •••• ••••');
  const [cardHolder, setCardHolder] = useState('FULL NAME');
  const [expiry, setExpiry] = useState('MM/YY');
  const [cvv, setCvv] = useState('');
  const [isFlipped, setIsFlipped] = useState(false);
  const [focusedInput, setFocusedInput] = useState('');
  const [cardType, setCardType] = useState('');

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue || '•••• •••• •••• ••••');
    detectCardType(formattedValue);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 4) {
      const formattedValue = value.replace(/(\d{2})(\d{2})/, '$1/$2');
      setExpiry(formattedValue || 'MM/YY');
    }
  };

  const detectCardType = (number: string) => {
    const cleanNumber = number.replace(/\D/g, '');
    let type = '';

    if (cleanNumber.startsWith('4')) {
      type = 'Visa';
    } else if (/^5[1-5]/.test(cleanNumber)) {
      type = 'Mastercard';
    } else if (/^3[47]/.test(cleanNumber)) {
      type = 'American Express';
    } else if (/^6(?:011|5)/.test(cleanNumber)) {
      type = 'Discover';
    }

    setCardType(type);
  };

  useEffect(() => {
    setIsFlipped(focusedInput === 'cvv');
  }, [focusedInput]);

  return (
    <div className="w-full max-w-md mx-auto p-4">
      <div className="relative w-full h-56 [perspective:1000px]">
        <div
          className={`relative w-full h-full transition-transform duration-500 [transform-style:preserve-3d] ${
            isFlipped ? '[transform:rotateY(180deg)]' : ''
          }`}
        >
          {/* Front of the card */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-900 rounded-xl p-6 flex flex-col justify-between shadow-lg [backface-visibility:hidden]">
            <div className="flex justify-between items-start">
              <CreditCard className="h-10 w-10 text-white" />
              <span className="text-white text-lg font-semibold">
                {cardType || 'CREDIT CARD'}
              </span>
            </div>
            <div className="mt-4">
              <span className="text-white text-2xl tracking-wider font-mono">
                {cardNumber}
              </span>
            </div>
            <div className="flex justify-between items-center mt-4">
              <div>
                <span className="text-white text-xs">Card Holder</span>
                <p className="text-white font-semibold">{cardHolder}</p>
              </div>
              <div>
                <span className="text-white text-xs">Expires</span>
                <p className="text-white font-semibold">{expiry}</p>
              </div>
            </div>
          </div>
          {/* Back of the card */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-900 rounded-xl p-6 flex flex-col justify-between shadow-lg [backface-visibility:hidden] [transform:rotateY(180deg)]">
            <div className="w-full h-12 bg-gray-800 mt-4"></div>
            <div className="relative mt-4">
              <div className="w-full h-8 bg-white"></div>
              <div className="absolute right-0 top-0 bottom-0 w-16 flex items-center justify-center bg-white">
                <span className="text-gray-900 font-mono">{cvv || '•••'}</span>
              </div>
            </div>
            <div className="text-white text-xs mt-4">
              This card is property of Your Bank. Misuse is criminal offence. If
              found, please return to Your Bank or to the nearest bank branch.
            </div>
          </div>
        </div>
      </div>
      <div className="mt-8 space-y-4">
        <div>
          <Label
            htmlFor="card-number-input"
            className="text-sm font-medium text-gray-700"
          >
            Card Number
          </Label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <CreditCard className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              id="card-number-input"
              className="pl-10 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="1234 5678 9012 3456"
              value={cardNumber === '•••• •••• •••• ••••' ? '' : cardNumber}
              onChange={handleCardNumberChange}
              onFocus={() => setFocusedInput('number')}
              onBlur={() => setFocusedInput('')}
              maxLength={19}
            />
          </div>
        </div>
        <div>
          <Label
            htmlFor="card-holder-input"
            className="text-sm font-medium text-gray-700"
          >
            Card Holder
          </Label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              id="card-holder-input"
              className="pl-10 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Full Name"
              value={cardHolder === 'FULL NAME' ? '' : cardHolder}
              onChange={(e) =>
                setCardHolder(e.target.value.toUpperCase() || 'FULL NAME')
              }
              onFocus={() => setFocusedInput('name')}
              onBlur={() => setFocusedInput('')}
            />
          </div>
        </div>
        <div className="flex space-x-4">
          <div className="flex-1">
            <Label
              htmlFor="expiry-input"
              className="text-sm font-medium text-gray-700"
            >
              Expiry Date
            </Label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                id="expiry-input"
                className="pl-10 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="MM/YY"
                value={expiry === 'MM/YY' ? '' : expiry}
                onChange={handleExpiryChange}
                onFocus={() => setFocusedInput('expiry')}
                onBlur={() => setFocusedInput('')}
                maxLength={5}
              />
            </div>
          </div>
          <div className="flex-1">
            <Label
              htmlFor="cvv-input"
              className="text-sm font-medium text-gray-700"
            >
              CVV
            </Label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                id="cvv-input"
                className="pl-10 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="123"
                value={cvv}
                onChange={(e) => setCvv(e.target.value)}
                onFocus={() => setFocusedInput('cvv')}
                onBlur={() => setFocusedInput('')}
                maxLength={3}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
