'use client';

import { useState } from 'react';
import {
  BarChart3,
  Check,
  HardDrive,
  HeadphonesIcon,
  Key,
  Lock,
  Paintbrush,
  Plug,
  Shield,
  Users,
  Users2,
  X,
} from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const plans = [
  {
    name: 'Starter',
    price: { monthly: 9, annual: 90 },
    description: 'Perfect for individuals and small teams',
    features: [
      'Up to 5 users',
      '5GB storage',
      'Basic support',
      'Limited integrations',
    ],
  },
  {
    name: 'Pro',
    price: { monthly: 29, annual: 290 },
    description: 'Great for growing businesses',
    features: [
      'Up to 20 users',
      '50GB storage',
      'Priority support',
      'Advanced integrations',
      'Analytics',
    ],
  },
  {
    name: 'Enterprise',
    price: { monthly: 99, annual: 990 },
    description: 'For large-scale operations',
    features: [
      'Unlimited users',
      '500GB storage',
      '24/7 dedicated support',
      'Custom integrations',
      'Advanced analytics',
      'SLA',
    ],
  },
];

const benefitsTable = [
  {
    feature: 'Users',
    icon: Users,
    starter: 'Up to 5',
    pro: 'Up to 20',
    enterprise: 'Unlimited',
  },
  {
    feature: 'Storage',
    icon: HardDrive,
    starter: '5GB',
    pro: '50GB',
    enterprise: '500GB',
  },
  {
    feature: 'Support',
    icon: HeadphonesIcon,
    starter: 'Basic',
    pro: 'Priority',
    enterprise: '24/7 Dedicated',
  },
  {
    feature: 'Integrations',
    icon: Plug,
    starter: 'Limited',
    pro: 'Advanced',
    enterprise: 'Custom',
  },
  {
    feature: 'Analytics',
    icon: BarChart3,
    starter: false,
    pro: true,
    enterprise: 'Advanced',
  },
  {
    feature: 'API Access',
    icon: Key,
    starter: false,
    pro: true,
    enterprise: true,
  },
  {
    feature: 'SLA',
    icon: Shield,
    starter: false,
    pro: false,
    enterprise: true,
  },
  {
    feature: 'Custom Branding',
    icon: Paintbrush,
    starter: false,
    pro: true,
    enterprise: true,
  },
  {
    feature: 'Team Collaboration',
    icon: Users2,
    starter: 'Basic',
    pro: 'Advanced',
    enterprise: 'Enterprise-grade',
  },
  {
    feature: 'Security Features',
    icon: Lock,
    starter: 'Standard',
    pro: 'Advanced',
    enterprise: 'Enterprise-grade',
  },
];

export default function UpgradePlans() {
  const [isAnnual, setIsAnnual] = useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <h2 className="text-3xl font-bold text-center mb-4">Upgrade Your Plan</h2>
      <p className="text-center text-muted-foreground mb-8">
        Choose the perfect plan for your needs
      </p>

      <div className="flex items-center justify-center mb-8">
        <span className="mr-2 text-sm font-medium">Monthly</span>
        <Switch
          checked={isAnnual}
          onCheckedChange={setIsAnnual}
          aria-label="Toggle annual billing"
        />
        <span className="ml-2 text-sm font-medium">Annual (Save 17%)</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {plans.map((plan) => (
          <Card key={plan.name} className="flex flex-col">
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <div className="text-3xl font-bold mb-4">
                ${isAnnual ? plan.price.annual : plan.price.monthly}
                <span className="text-sm font-normal text-muted-foreground">
                  {isAnnual ? '/year' : '/month'}
                </span>
              </div>
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <Check className="mr-2 h-4 w-4" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <EnhancedButton className="w-full">
                Upgrade to {plan.name}
              </EnhancedButton>
            </CardFooter>
          </Card>
        ))}
      </div>

      <h3 className="text-2xl font-bold text-center mb-6">
        Compare Plan Benefits
      </h3>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Feature</TableHead>
              <TableHead>Starter</TableHead>
              <TableHead>Pro</TableHead>
              <TableHead>Enterprise</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {benefitsTable.map((row) => (
              <TableRow key={row.feature}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <row.icon className="h-4 w-4 mr-2" />
                    {row.feature}
                  </div>
                </TableCell>
                <TableCell>{renderFeature(row.starter)}</TableCell>
                <TableCell>{renderFeature(row.pro)}</TableCell>
                <TableCell>{renderFeature(row.enterprise)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

function renderFeature(feature: boolean | string) {
  if (typeof feature === 'boolean') {
    return feature ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />;
  }
  return feature;
}
