'use client';

import * as React from 'react';
import { Command as CommandPrimitive } from 'cmdk';
import { X } from 'lucide-react';

import { slugify } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';

type Tag = Record<'value' | 'label', string>;

const TAGS = [
  {
    value: 'next-js',
    label: 'Next.js',
  },
  {
    value: 'svelte-kit',
    label: 'SvelteKit',
  },
  {
    value: 'nuxt-js',
    label: 'Nuxt.js',
  },
  {
    value: 'remix',
    label: 'Remix',
  },
  {
    value: 'astro',
    label: 'Astro',
  },
  {
    value: 'wordpress',
    label: 'WordPress',
  },
  {
    value: 'express-js',
    label: 'Express.js',
  },
  {
    value: 'nest-js',
    label: 'Nest.js',
  },
] satisfies Tag[];

export default function MultiSelect() {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [openOptions, setOpenOptions] = React.useState(true);
  const [selected, setSelected] = React.useState<Tag[]>([]);
  const [inputValue, setInputValue] = React.useState('');

  const handleUnselect = React.useCallback((tag: Tag) => {
    setSelected((prev) => prev.filter((s) => s.value !== tag.value));
  }, []);

  const selectables = TAGS.filter((tag) => !selected.includes(tag));

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (input) {
        if (e.key === 'Delete' || e.key === 'Backspace') {
          if (input.value === '') {
            setSelected((prev) => {
              const newSelected = [...prev];
              newSelected.pop();
              return newSelected;
            });
          }
        }

        if (e.key === 'Escape') {
          input.blur();
        }

        if (e.key == 'Enter') {
          if (
            !selectables.find((t) =>
              t.label.toLowerCase().includes(inputValue.toLowerCase())
            )
          ) {
            setSelected([
              ...selected,
              {
                value: slugify(inputValue),
                label: inputValue,
              },
            ]);
            setInputValue('');
          }
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [inputValue]
  );

  return (
    <Command
      onKeyDown={handleKeyDown}
      className="overflow-visible bg-transparent"
    >
      <div className="group rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
        <div className="flex flex-wrap gap-1">
          {selected.map((tag) => {
            return (
              <Badge key={tag.value} variant="secondary">
                {tag.label}
                <button
                  className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleUnselect(tag);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={() => handleUnselect(tag)}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              </Badge>
            );
          })}
          <CommandPrimitive.Input
            ref={inputRef}
            value={inputValue}
            onValueChange={setInputValue}
            onBlur={() => setOpenOptions(false)}
            onFocus={() => setOpenOptions(true)}
            placeholder="Select tags..."
            className="ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground"
          />
        </div>
      </div>
      <div className="relative mt-2">
        <CommandList>
          {openOptions && selectables.length > 0 && (
            <div className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
              <CommandGroup className="h-full overflow-auto">
                {selectables.map((tag) => {
                  return (
                    <CommandItem
                      key={tag.value}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onSelect={(value) => {
                        setInputValue('');
                        setSelected((prev) => [...prev, tag]);
                      }}
                      className={'cursor-pointer'}
                    >
                      {tag.label}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </div>
          )}
        </CommandList>
      </div>
    </Command>
  );
}
