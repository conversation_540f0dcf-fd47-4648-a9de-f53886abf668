'use client';

import { useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { uploadFile } from '@/actions/upload';
import { updateUser } from '@/actions/users';
import {
  UpdateProfileSchema,
  UpdateProfileValues,
} from '@/schemas/users.schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Camera } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useFieldArray, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { SessionUser } from '@/types/user.types';
import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { fileToFormData } from '@/lib/image-utils';
import { cn, getInitials, renderUserName } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { EnhancedButton } from '@/components/shared/enhanced-button';

type ProfileFormProps = {
  data: SessionUser | null;
  isOnboarding?: boolean;
};

export function ProfileForm({ data, isOnboarding = false }: ProfileFormProps) {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: session, update: updateSession } = useSession();
  const [avatar, setAvatar] = useState<FormData | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(data?.image || '');
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<UpdateProfileValues>({
    resolver: zodResolver(UpdateProfileSchema),
    defaultValues: {
      name: data?.name || '',
      username: data?.username || '',
      bio: data?.bio || '',
      urls: data?.urls?.map((url) => ({ value: url })) || [{ value: '' }],
    },
    mode: 'onChange',
  });

  const { fields, append, remove } = useFieldArray({
    name: 'urls',
    control: form.control,
  });

  async function onSubmit(data: UpdateProfileValues) {
    setIsLoading(true);
    try {
      let image;
      if (avatar) {
        const { url } = await uploadFile(avatar as FormData);
        image = url;
      }
      const updatedUser = await updateUser({ ...data, ...{ image } });

      if (updatedUser) {
        await updateSession({
          ...session,
          user: {
            ...session?.user,
            ...updatedUser,
          },
        });

        toast.success('Profile updated successfully');

        // Only redirect if this is the onboarding flow
        if (isOnboarding && updatedUser.name && updatedUser.username) {
          router.push('/dashboard');
        }
        router.refresh();
      }
    } catch (error) {
      toast.error('Error', {
        description: `There was an error updating your profile. ${(error as Error).message}`,
      });
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          name="image"
          control={form.control}
          render={({}) => (
            <FormItem>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Avatar className="w-20 h-20">
                    {session?.user?.id && (
                      <AvatarImage
                        src={
                          avatarUrl || getDeterministicAvatar(session?.user?.id)
                        }
                        alt="Profile picture"
                      />
                    )}

                    <AvatarFallback>
                      {getInitials(
                        renderUserName(session?.user as SessionUser)
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <EnhancedButton
                    type="button"
                    size="icon"
                    className="absolute bottom-0 right-0 rounded-full w-8 h-8 bg-primary text-primary-foreground hover:bg-primary/90"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Camera className="w-4 h-4" />
                    <span className="sr-only">Change profile picture</span>
                  </EnhancedButton>
                </div>
                <Input
                  ref={fileInputRef}
                  id="avatar"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const imageUrl = URL.createObjectURL(file);
                      const formData = fileToFormData(file);
                      // field.onChange(formData);
                      setAvatar(formData);
                      setAvatarUrl(imageUrl);
                    }
                  }}
                />
              </div>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Your name" {...field} />
              </FormControl>
              <FormDescription>
                This is your full name or the name you’d like others to see.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="username" {...field} />
              </FormControl>
              <FormDescription>
                This is your public username. It can only contain letters,
                numbers, and underscores.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bio</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us a little bit about yourself"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Brief description for your profile. URLs are hyperlinked.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          {fields.map((field, index) => (
            <FormField
              control={form.control}
              key={field.id}
              name={`urls.${index}.value`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={cn(index !== 0 && 'sr-only')}>
                    URLs
                  </FormLabel>
                  <FormDescription className={cn(index !== 0 && 'sr-only')}>
                    Add links to your website, blog, or social media profiles.
                  </FormDescription>
                  <div className="flex items-center">
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <EnhancedButton
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="ml-2"
                      onClick={() => remove(index)}
                    >
                      Remove
                    </EnhancedButton>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
          <EnhancedButton
            type="button"
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => append({ value: '' })}
          >
            Add URL
          </EnhancedButton>
        </div>
        <EnhancedButton isLoading={isLoading} type="submit">
          Update profile
        </EnhancedButton>
      </form>
    </Form>
  );
}
