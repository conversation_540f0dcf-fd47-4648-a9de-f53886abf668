import { Skeleton } from '@/components/ui/skeleton';

export default function NotificationsPageSkeleton() {
  return (
    <div className="grid lg:grid-cols-5 h-screen">
      {/* Sidebar Skeleton */}
      <div className="lg:col-span-2 border-r border-gray-200 p-4 overflow-y-auto">
        <Skeleton className="h-8 w-3/4 mb-6" />
        <div className="space-y-4">
          {[...Array(10)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
          ))}
        </div>
      </div>

      {/* Notification Detail Skeleton */}
      <div className="lg:col-span-3 p-6 overflow-y-auto">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center space-x-3 mb-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-1/3" />
              <Skeleton className="h-4 w-1/4" />
            </div>
          </div>
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    </div>
  );
}
