import { Skeleton } from '@/components/ui/skeleton';

export default function BookmarksGridSkeleton() {
  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="flex flex-col space-y-4">
          <Skeleton className="h-48 w-full rounded-lg" /> {/* Image placeholder */}
          <Skeleton className="h-6 w-3/4" /> {/* Title placeholder */}
          <Skeleton className="h-4 w-full" /> {/* Excerpt line 1 */}
          <Skeleton className="h-4 w-5/6" /> {/* Excerpt line 2 */}
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-10 rounded-full" /> {/* Author avatar */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" /> {/* Author name */}
              <Skeleton className="h-3 w-24" /> {/* Date */}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}