import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

const avatarCircleVariants = cva('', {
  variants: {
    size: {
      lg: 'h-16 w-16',
      md: 'h-10 w-10',
      sm: 'h-8 w-8',
      xs: 'h-6 w-6',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

const spacingVariants = cva('', {
  variants: {
    size: {
      lg: '-space-x-6',
      md: '-space-x-4',
      sm: '-space-x-3',
      xs: '-space-x-2',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

interface AvatarCirclesSkeletonProps {
  count?: number;
  size?: 'lg' | 'md' | 'sm' | 'xs';
  className?: string;
}

export function AvatarCirclesSkeleton({
  count = 6,
  size = 'md',
  className,
}: AvatarCirclesSkeletonProps) {
  return (
    <div
      className={cn(
        'z-10 flex rtl:space-x-reverse',
        spacingVariants({ size }),
        className
      )}
    >
      {Array.from({ length: count }).map((_, index) => (
        <Skeleton
          key={index}
          className={cn(
            avatarCircleVariants({ size }),
            'rounded-full border-2 border-white dark:border-gray-800'
          )}
        />
      ))}
    </div>
  );
}