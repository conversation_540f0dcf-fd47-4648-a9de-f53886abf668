import { Skeleton } from '@/components/ui/skeleton';
import Container from '@/components/layout/container';

export default function PostsListPageSkeleton() {
  return (
    <Container narrow className="px-0">
      {/* Header with Back Button and Action Buttons */}
      <header className="flex h-16 shrink-0 items-center gap-2 justify-between relative flex-1">
        {/* Back Button Skeleton */}
        <Skeleton className="h-9 w-24 rounded-md" />

        {/* Action Buttons Container */}
        <div className="md:items-center md:space-x-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-9 w-24 rounded-md" /> {/* Save draft */}
            <Skeleton className="h-9 w-24 rounded-md" /> {/* Publish */}
          </div>
        </div>
      </header>

      <Container narrow className="px-0 py-0">
        <div className="px-4">
          {/* Blog Posts Grid */}
          <div className="grid gap-10 sm:grid-cols-2">
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className="group overflow-hidden bg-background rounded-lg"
              >
                <div className="space-y-4">
                  {/* Author info at the top */}
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32 mt-1" />
                    </div>
                  </div>

                  {/* Cover image */}
                  <Skeleton className="h-48 w-full rounded-lg" />

                  {/* Title and tags */}
                  <div className="space-y-2">
                    <Skeleton className="h-7 w-3/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-5 w-16 rounded-full" />
                      <Skeleton className="h-5 w-16 rounded-full" />
                      <Skeleton className="h-5 w-16 rounded-full" />
                    </div>
                  </div>

                  {/* Excerpt */}
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>

                  {/* Stats at the bottom */}
                  <div className="flex justify-end gap-3">
                    <Skeleton className="h-4 w-8" />
                    <Skeleton className="h-4 w-8" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </Container>
  );
}
