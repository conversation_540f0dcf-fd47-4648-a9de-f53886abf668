import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface TableSkeletonProps {
  columnCount: number;
  rowCount: number;
  columnWidths?: (string | number)[];
  cellHeight?: string | number;
  showHeader?: boolean;
}

const formatWidth = (width: string | number): string => {
  if (typeof width === 'number') {
    return `w-${width}`;
  }
  if (width === '100%') {
    return 'w-full';
  }
  return `w-[${width}]`;
};

const formatHeight = (height: string | number): string => {
  if (typeof height === 'number') {
    return `h-${height}`;
  }
  return `h-[${height}]`;
};

export function TableSkeleton({
  columnCount,
  rowCount,
  columnWidths = [],
  cellHeight = '40px',
  showHeader = true,
}: TableSkeletonProps) {
  return (
    <Table>
      {showHeader && (
        <TableHeader>
          <TableRow>
            {Array.from({ length: columnCount }).map((_, index) => (
              <TableHead key={index}>
                <Skeleton
                  className={`${formatHeight(cellHeight)} ${
                    columnWidths[index]
                      ? formatWidth(columnWidths[index])
                      : 'w-[100px]'
                  }`}
                />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
      )}
      <TableBody>
        {Array.from({ length: rowCount }).map((_, rowIndex) => (
          <TableRow key={rowIndex}>
            {Array.from({ length: columnCount }).map((_, colIndex) => (
              <TableCell key={colIndex}>
                <Skeleton
                  className={`${formatHeight(cellHeight)} ${
                    columnWidths[colIndex]
                      ? formatWidth(columnWidths[colIndex])
                      : 'w-[100px]'
                  }`}
                />
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
