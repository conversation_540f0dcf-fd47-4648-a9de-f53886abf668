import React from 'react';

import { Skeleton } from '@/components/ui/skeleton';

import Container from '../layout/container';

export function EditorSkeleton() {
  return (
    <>
      <Container>
        {/* Header with Back Button and Action Buttons */}
        <header className="flex h-16 shrink-0 items-center gap-2 justify-between relative flex-1">
          {/* Back Button Skeleton */}
          <Skeleton className="h-9 w-24 rounded-md" />

          {/* Action Buttons Container */}
          <div className="md:items-center md:space-x-4">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-9 w-24 rounded-md" /> {/* Save draft */}
              <Skeleton className="h-9 w-24 rounded-md" /> {/* Publish */}
            </div>
          </div>
        </header>

        <Container narrow className="px-0 py-0">
          <form className="space-y-8">
            {/* Cover Image Skeleton */}
            <Skeleton className="w-full aspect-video rounded-lg" />

            {/* Title Input Skeleton */}
            <Skeleton className="h-12 w-full" />

            {/* Tags Input Skeleton */}
            <div className="flex items-center space-x-4">
              <Skeleton className="h-8 w-24 rounded-md" />
              <Skeleton className="h-8 w-24 rounded-md" />
              <Skeleton className="h-8 w-24 rounded-md" />
            </div>

            {/* Editor Content Skeleton */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-5/6" />
            </div>
          </form>
        </Container>
      </Container>
    </>
  );
}
