'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { login, loginOAuth } from '@/actions/auth';
import { SigninSchema, SigninValues } from '@/schemas/auth.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import OauthButton from '@/components/auth/oauth-button';
import { EnhancedButton } from '@/components/shared/enhanced-button';

const getErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'OAuthAccountNotLinked':
      return 'This email is already associated with another account. Please use a different login method.';
    case 'CredentialsSignin':
      return 'Invalid credentials. Please check your email and password.';
    default:
      return 'An error occurred during login. Please try again.';
  }
};

export default function LoginForm() {
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('callbackUrl') || '';

  const [authError, setAuthError] = useState('');
  const error = searchParams.get('error');
  const router = useRouter();
  const form = useForm<SigninValues>({
    resolver: zodResolver(SigninSchema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    if (error) {
      setAuthError(error);
    }
  }, [error, searchParams]);

  useEffect(() => {
    if (authError) {
      toast.error(getErrorMessage(authError));
    }
    // Clear the error from the URL after showing the toast
    const params = new URLSearchParams(searchParams);
    params.delete('error'); // Remove the error parameter
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.replace(newUrl); // Replace URL without triggering a page refresh
  }, [authError]);

  const handleLogin = async (values: SigninValues) => {
    try {
      await login(values, redirectUrl);
    } catch (error) {
      toast.error('An error occurred during login. Please try again.', {
        description: `Failed to login. Please try again. ${
          (error as Error).message
        }`,
      });
    }
  };

  const handleOAuthLogin = async (provider: 'github' | 'google') => {
    try {
      await loginOAuth(provider, redirectUrl);
    } catch (error) {
      toast.error('An error occurred during login. Please try again.', {
        description: `Failed to login. Please try again. ${
          (error as Error).message
        }`,
      });
    }
  };

  return (
    <Card className="w-[350px] ">
      <CardHeader>
        <CardTitle>Sign in </CardTitle>
        <CardDescription>Sign in to your account.</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-4">
        <div className="grid grid-cols-2 gap-6">
          <OauthButton
            provider="github"
            onClick={() => handleOAuthLogin('github')}
          />
          <OauthButton
            provider="google"
            onClick={() => handleOAuthLogin('google')}
          />
        </div>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t"></span>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or continue with
            </span>
          </div>
        </div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((values) => handleLogin(values))}
            className="space-y-8 pt-0"
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>This is your email address</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <EnhancedButton
              type="submit"
              className="w-full"
              isLoading={form.formState.isSubmitting}
            >
              Send magic link
            </EnhancedButton>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
