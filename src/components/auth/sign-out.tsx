import { logOut } from '@/actions/auth';
import { signOut } from 'next-auth/react';

import { cn } from '@/lib/utils';

export default function Signout({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <form
      className="w-full"
      action={async () => {
        await logOut();
        await signOut({ redirect: false });
      }}
    >
      {/* remove all browser styles */}
      <button type="submit" className={cn('w-full', className)}>
        {children}
      </button>
    </form>
  );
}
