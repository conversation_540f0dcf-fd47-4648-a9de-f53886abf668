'use client';

import { useTransition } from 'react';
import { FaGithub, FaGoogle } from 'react-icons/fa';

import { EnhancedButton } from '@/components/shared/enhanced-button';

const providers = {
  github: <FaGithub />,
  google: <FaGoogle />,
};

type Provider = keyof typeof providers;
interface OauthButtonProps {
  provider: Provider;
  onClick: () => void;
  children?: React.ReactNode;
}
export default function OauthButton({
  children,
  provider,
  onClick,
}: OauthButtonProps) {
  const [isPending, startTransition] = useTransition();
  return (
    <EnhancedButton
      aria-label={`Sign in with ${provider}`}
      isLoading={isPending}
      variant="outline"
      onClick={() =>
        startTransition(() => {
          onClick();
        })
      }
    >
      {providers[provider]} {children}
    </EnhancedButton>
  );
}
