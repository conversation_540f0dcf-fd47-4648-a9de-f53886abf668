import React from 'react';
import Link, { LinkProps } from 'next/link';
import { MDXComponents } from 'mdx/types';

import { Callout } from '@/components/docs/custom/callout';
import CopyCodeSnippet from '@/components/docs/custom/copy-code-snippet';
import Steps, { Step } from '@/components/docs/custom/steps';
import Tabs, { Tab } from '@/components/docs/custom/tabs';

export const customMDXComponents: MDXComponents = {
  a: ({ href, ...props }) => (
    <Link {...(props as LinkProps)} href={href as LinkProps['href']} />
  ),
  pre: ({ children, ...props }) => (
    <CopyCodeSnippet>
      <pre {...props}>{children}</pre>
    </CopyCodeSnippet>
  ),
  Callout,
  CopyCodeSnippet,
  Tabs,
  Tab,
  Steps,
  Step,
};
