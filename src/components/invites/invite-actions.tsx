import { Invite } from '@prisma/client';
import { MoreHorizontal, Send, Trash } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedButton } from '@/components/shared/enhanced-button';

interface InviteActionsProps {
  invite: Invite;
  onResend: (inviteId: string) => Promise<void>;
  onDelete: (invite: Invite) => void;
}

export function InviteActions({
  invite,
  onResend,
  onDelete,
}: InviteActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <EnhancedButton variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </EnhancedButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onResend(invite.id)}>
          <Send className="mr-2 h-4 w-4" />
          Resend Invite
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onDelete(invite)}>
          <Trash className="mr-2 h-4 w-4" /> Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
