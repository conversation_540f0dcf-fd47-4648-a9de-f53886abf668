'use client';

import * as React from 'react';
import {
  InviteListSchema,
  InviteRole,
  type InviteListFormValues,
  type SendInviteValues,
} from '@/schemas/invitations.schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { Role } from '@prisma/client';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { InputWithSelect } from '@/components/ui/input-with-select';
import { ScrollArea } from '@/components/ui/scroll-area';

export function InviteListForm({
  id,
  onSubmit,
}: {
  id?: string;
  onSubmit: (values: InviteListFormValues) => Promise<void>;
  className?: string;
}) {
  const [invites, setInvites] = React.useState<SendInviteValues[]>([]);
  const [newInviteEmail, setNewInviteEmail] = React.useState('');
  const [selectedRole, setSelectedRole] = React.useState<Role>(
    Role.COLLABORATOR
  );
  const [inputError, setInputError] = React.useState<string>('');

  const form = useForm<InviteListFormValues>({
    resolver: zodResolver(InviteListSchema),
    defaultValues: {
      invites: [],
    },
  });

  React.useEffect(() => {
    form.setValue('invites', invites);
  }, [invites, form]);

  const addInvite = (e?: React.KeyboardEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setInputError('');

    if (!newInviteEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      setInputError('Please enter a valid email address');
      return;
    }

    if (invites.some((invite) => invite.email === newInviteEmail)) {
      setInputError('This email has already been added');
      return;
    }

    setInvites([
      ...invites,
      { email: newInviteEmail, role: selectedRole as InviteRole },
    ]);
    setNewInviteEmail('');
  };

  const removeInvite = (email: string) => {
    setInvites(invites.filter((invite) => invite.email !== email));
  };

  const handleSubmit = async (values: InviteListFormValues) => {
    await onSubmit(values);
    setInvites([]);
    form.reset();
  };

  return (
    <Form {...form}>
      <form
        id={id}
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="invites"
          render={() => (
            <FormItem>
              <FormControl>
                <InputWithSelect
                  placeholder="Enter email address and press Enter"
                  value={newInviteEmail}
                  onChange={(e) => {
                    setNewInviteEmail(e.target.value);
                    setInputError(''); // Clear error when input changes
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      addInvite(e);
                    }
                  }}
                  selectValue={selectedRole}
                  onSelectChange={(value) => setSelectedRole(value as Role)}
                  selectItems={[
                    { value: Role.ADMIN, label: 'Admin' },
                    { value: Role.COLLABORATOR, label: 'Collaborator' },
                    { value: Role.USER, label: 'User' },
                  ]}
                  selectPlaceholder="Role"
                  className="w-full"
                />
              </FormControl>
              {inputError && (
                <p className="text-sm font-medium text-destructive mt-2">
                  {inputError}
                </p>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        {invites.length > 0 && (
          <div className=" space-y-2">
            <h4 className="font-medium text-sm my-4">
              Pending Invites ({invites.length})
            </h4>
            <ScrollArea className="h-[240px] w-full">
              <div className="space-y-2">
                {invites.map((invite) => (
                  <div
                    key={invite.email}
                    className="flex items-center justify-between bg-muted/50 p-2 rounded-md"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{invite.email}</span>
                      <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                        {invite.role.toLowerCase()}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeInvite(invite.email)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </form>
    </Form>
  );
}
