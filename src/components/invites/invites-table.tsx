'use client';

import { useEffect, useMemo, useRef, useState, useTransition } from 'react';
import { deleteInvite } from '@/actions/invitations/delete-invite.action';
import { getInvites } from '@/actions/invitations/get-invites.action';
import { resendInvite } from '@/actions/invitations/resend-invite.action';
import { sendInvites } from '@/actions/invitations/send-invite.action';
import { InviteListFormValues } from '@/schemas/invitations.schemas';
import { Invite, InviteStatus } from '@prisma/client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Send } from 'lucide-react';
import { toast } from 'sonner';

import { GetPaginatedResult } from '@/types/shared.types';
import { calculateNewPage } from '@/lib/pagination';
import { toastPromise } from '@/lib/toast-promise';
import { capitalize } from '@/lib/utils';
import { useTable } from '@/hooks/use-table';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/ui/modal';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { InviteActions } from '@/components/invites/invite-actions';
import { InviteListForm } from '@/components/invites/invite-list-form';
import BluredBgSpinner from '@/components/shared/blured-bg-spinner';
import { ConfirmDialog } from '@/components/shared/confirm-dialog';
import EmptyState from '@/components/shared/empty-state';
import { EnhancedButton } from '@/components/shared/enhanced-button';
import { Pagination } from '@/components/shared/pagination';
import SearchBar from '@/components/shared/searchbar';

type InvitesTableProps = {
  initialData: GetPaginatedResult<Invite>;
};

export function InvitesTable({ initialData }: InvitesTableProps) {
  const isInitialMount = useRef(true);
  const queryClient = useQueryClient();
  const {
    data: invites,
    setData: setInvites,
    searchTerm,
    setSearchTerm,
    debouncedSearchTerm,
    sortOrder,
    onSortChange,
    sortBy,
    sorting,
    page,
    setPage,
    totalItems,
    setTotalItems,
    itemsPerPage,
    isEmpty,
    isPaginationVisible,
    defaultParams,
  } = useTable<Invite>({ initialData });
  const [isPending, startTransition] = useTransition();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [inviteToDelete, setInviteToDelete] = useState<Invite | null>(null);

  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Columns definition
  const columns = useMemo<ColumnDef<Invite>[]>(
    () => [
      {
        accessorKey: 'email',
        header: 'Email',
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.status;
          let variant: 'default' | 'secondary' | 'outline' = 'default';

          switch (status) {
            case InviteStatus.ACCEPTED:
              variant = 'default';
              break;
            case InviteStatus.PENDING:
              variant = 'secondary';
              break;
            case InviteStatus.REJECTED:
              variant = 'outline';
              break;
          }

          return <Badge variant={variant}>{capitalize(status)}</Badge>;
        },
      },
      {
        accessorKey: 'createdAt',
        header: 'Invited At',
        cell: ({ row }) =>
          new Date(row.original.createdAt).toLocaleDateString(),
      },
      {
        id: 'actions',
        enableSorting: false,
        cell: ({ row }) => (
          <InviteActions
            invite={row.original}
            onResend={handleResendInvite}
            onDelete={(invite) => {
              setInviteToDelete(invite);
              setIsDeleteConfirmOpen(true);
            }}
          />
        ),
      },
    ],
    []
  );

  // Table instance
  const table = useReactTable({
    data: invites,
    columns,
    state: { sorting },
    onSortingChange: onSortChange,
    getCoreRowModel: getCoreRowModel(),
    enableSortingRemoval: false,
  });

  // Fetch invites
  const {
    data: result,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['invites', page, sortBy, sortOrder, debouncedSearchTerm],
    queryFn: async () => {
      return getInvites(defaultParams);
    },
    staleTime: 1000 * 60, // Consider data fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle search change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    setSearchTerm(e.target.value);
  };

  // Handle resend invite
  const handleResendInvite = async (inviteId: string) => {
    return toastPromise(resendInvite(inviteId), {
      loading: 'Resending invitation...',
      success: 'Invitation resent successfully',
      error: 'Failed to resend invitation',
    });
  };

  // Handle delete invite
  const handleDeleteInvite = async () => {
    if (!inviteToDelete) return;

    startTransition(async () => {
      try {
        await deleteInvite(inviteToDelete.id);
        const newTotalItems = totalItems - 1;
        const newPage = calculateNewPage(page, newTotalItems, itemsPerPage);
        setPage(newPage);
        setTotalItems(newTotalItems);
        queryClient.invalidateQueries({ queryKey: ['invites'] });
        toast.success('Invite deleted successfully');
        setInvites(invites.filter((invite) => invite.id !== inviteToDelete.id));
      } catch (error) {
        console.error('Error deleting invite:', error);
        toast.error('Failed to delete invite');
      } finally {
        setIsDeleteConfirmOpen(false);
        setInviteToDelete(null);
      }
    });
  };

  const handleInvite = async (values: InviteListFormValues) => {
    startTransition(async () => {
      try {
        // Handle multiple invites
        await sendInvites(values.invites);
        const newTotalItems = totalItems + values.invites.length;
        const newPage = calculateNewPage(
          page,
          newTotalItems,
          itemsPerPage,
          true
        );
        setPage(newPage);
        setTotalItems(newTotalItems);
        queryClient.invalidateQueries({ queryKey: ['invites'] });
        await refetch();
        toast.success('Invitations sent successfully');
        setIsInviteModalOpen(false);
      } catch (error) {
        console.error('Error sending invitations:', error);
        toast.error('Failed to send invitations');
      }
    });
  };

  useEffect(() => {
    if (result) {
      setInvites(result.items);
      setTotalItems(result.totalCount);

      // Only update page on initial mount if it differs
      if (isInitialMount.current && result.page !== page) {
        isInitialMount.current = false;
        setPage(result.page);
      }
    }

    return () => {
      isInitialMount.current = true;
    };
  }, [result]);

  return (
    <>
      {isEmpty ? (
        <EmptyState
          title="No invites"
          description="You currently have no invites. Go to the users page to invite new users."
          actionLabel="Invite User"
          onAction={() => setIsInviteModalOpen(true)}
        />
      ) : (
        <>
          <div className="flex md:items-center justify-between gap-4 mb-4">
            <div className="md:min-w-[300px]">
              <SearchBar
                className="w-full"
                placeholder="Filter invites..."
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            <EnhancedButton onClick={() => setIsInviteModalOpen(true)}>
              <Send className="w-4 h-4 mr-2" />
              Invite User
            </EnhancedButton>
          </div>
          <div className="relative">
            {isLoading && <BluredBgSpinner />}
            <Table className="mb-4">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {header.isPlaceholder ? null : (
                          <div className="flex items-center">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <span className="ml-2">
                                {{
                                  asc: '↑',
                                  desc: '↓',
                                }[header.column.getIsSorted() as string] ?? ''}
                              </span>
                            )}
                          </div>
                        )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No invites found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </>
      )}

      {isPaginationVisible && (
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      )}

      <Modal
        title="Invite Users"
        description="Enter email addresses and select roles for the users you want to invite."
        isOpen={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        footer={
          <EnhancedButton
            type="submit"
            form="invite-list-form"
            isLoading={isPending}
            loadingText="Sending..."
          >
            Send Invites
          </EnhancedButton>
        }
      >
        <InviteListForm id="invite-list-form" onSubmit={handleInvite} />
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteInvite}
        title="Confirm Delete Invite"
        isLoading={isPending}
        loadingText="Deleting..."
        description="Are you sure you want to delete this invite? This action cannot be undone."
      />
    </>
  );
}
