import React from 'react';
import { Repeat } from 'lucide-react';

import { DashboardData } from '@/types/dashboard.types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { DashboardCard } from './dashboard-card';
import { DashboardSection } from './dashboard-section';
import { GeographicSalesGraph } from './geographic-sales-graph';
import { TopProductsGraph } from './top-products';

interface AnalyticsTabProps {
  dashboardData: DashboardData;
}

export function AnalyticsTab({ dashboardData }: AnalyticsTabProps) {
  const geographicDistributionData = Object.entries(
    dashboardData?.geographicDistribution
  ).map(([country, count]) => ({
    country,
    count,
  }));

  const popularPlansData = dashboardData?.popularPlans?.map((plan) => ({
    name: plan.name,
    value: plan.count,
  }));

  return (
    <div className="space-y-4">
      <DashboardSection>
        {/* <DashboardCard
          title="Payment Success Rate"
          value={`${dashboardData.paymentSuccessRate}%`}
          icon={CheckCircle}
          description="Of all attempted payments"
        /> */}
        <DashboardCard
          title="Recurring Revenue"
          value={`$${dashboardData.recurringRevenue.toFixed(2)}`}
          icon={Repeat}
          description="Monthly recurring revenue"
        />
        {/* <DashboardCard
          title="Sales"
          value={`+${dashboardData.recentSales.length}`}
          icon={CreditCard}
          description="+19% from last month"
        /> */}
        {/* <DashboardCard
          title="Subscription Growth"
          value={`${dashboardData.subscriptionGrowth}%`}
          icon={TrendingUp}
          description="Month-over-month growth"
        /> */}
      </DashboardSection>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-full lg:col-span-5">
          <CardHeader>
            <CardTitle>Geographic Sales Distribution</CardTitle>
            <CardDescription>
              You&apos;ve made sales to customers in{' '}
              {geographicDistributionData.length} countries this month.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GeographicSalesGraph data={geographicDistributionData} />
          </CardContent>
        </Card>
        <Card className="col-span-full lg:col-span-2">
          <CardHeader>
            <CardTitle>Top Products</CardTitle>
            <CardDescription>
              Top 5 most popular products this month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TopProductsGraph data={popularPlansData} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
