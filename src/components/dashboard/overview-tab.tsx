import React from 'react';
import { CreditCard, DollarSign, Repeat, Users } from 'lucide-react';

import { DashboardData } from '@/types/dashboard.types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { DashboardCard } from './dashboard-card';
import { DashboardSection } from './dashboard-section';
import { GeographicSalesGraph } from './geographic-sales-graph';
import { OverviewGraph } from './overview-graph';
import { RecentSalesGraph } from './recent-sales-graph';

interface OverviewTabProps {
  dashboardData: DashboardData;
}

export function OverviewTab({ dashboardData }: OverviewTabProps) {
  // Transform geographic data for the graph
  const geographicData = Object.entries(
    dashboardData.geographicDistribution
  ).map(([country, count]) => ({
    country,
    count,
  }));

  return (
    <div className="space-y-4">
      <DashboardSection>
        <DashboardCard
          title="Total Revenue"
          value={`$${dashboardData.totalRevenue.toFixed(2)}`}
          icon={DollarSign}
          description={`${Number(dashboardData.metrics.revenueChange) > 0 ? '+' : ''}${
            dashboardData.metrics.revenueChange
          }% from last month`}
        />
        <DashboardCard
          title="Subscriptions"
          value={`+${dashboardData.totalSubscriptions}`}
          icon={Users}
          description={`${dashboardData.subscriptionGrowth}% growth`}
        />
        <DashboardCard
          title="Sales"
          value={`+${dashboardData.recentSales.length}`}
          icon={CreditCard}
          description={`${Number(dashboardData.metrics.salesChange ?? 0) > 0 ? '+' : ''}${
            dashboardData.metrics.salesChange ?? 0
          }% from last month`}
        />
        <DashboardCard
          title="Recurring Revenue"
          value={`$${dashboardData.recurringRevenue.toFixed(2)}`}
          icon={Repeat}
          description="Monthly recurring revenue"
        />
      </DashboardSection>

      <div className="grid gap-4">
        <Card className="col-span-full">
          <CardHeader>
            <CardTitle>Monthly Sales Overview</CardTitle>
            <CardDescription>
              Total sales amount by month for the past year
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <OverviewGraph data={dashboardData.monthlySales} />
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Geographic Distribution</CardTitle>
            <CardDescription>
              Sales distribution across countries
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GeographicSalesGraph data={geographicData} />
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Recent Sales</CardTitle>
            <CardDescription>
              You made {dashboardData.recentSales.length} sales this month.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentSalesGraph sales={dashboardData.recentSales} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
