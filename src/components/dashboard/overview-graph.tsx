'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON> } from 'recharts';

import { DashboardData } from '@/types/dashboard.types';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

export function OverviewGraph({
  data,
}: {
  data: DashboardData['monthlySales'];
}) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <ChartContainer config={{}}>
        <BarChart data={data}>
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `$${value}`}
          />
          <ChartTooltip
            content={<ChartTooltipContent />}
            cursor={{ fill: 'var(--muted)' }}
          />
          <Bar
            dataKey="total"
            fill="currentColor"
            radius={[4, 4, 0, 0]}
            className="fill-primary"
          />
        </BarChart>
      </ChartContainer>
    </ResponsiveContainer>
  );
}
