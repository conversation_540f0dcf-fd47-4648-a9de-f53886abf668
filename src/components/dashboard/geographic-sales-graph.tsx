'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

export function GeographicSalesGraph({
  data,
}: {
  data: {
    country: string;
    count: number;
  }[];
}) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart
        width={600}
        height={300}
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="country" />
        <YAxis />
        <Tooltip
          contentStyle={{
            backgroundColor: 'hsl(var(--background))',
            borderColor: 'hsl(var(--border))',
            borderRadius: '0.5rem',
          }}
          itemStyle={{
            color: 'hsl(var(--foreground))',
          }}
          cursor={{ fill: 'hsl(var(--muted))' }}
        />
        <Legend />
        <Bar dataKey="count" fill="currentColor" className="fill-primary" />
      </BarChart>
    </ResponsiveContainer>
  );
}
