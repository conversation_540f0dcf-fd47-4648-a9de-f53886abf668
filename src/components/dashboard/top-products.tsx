import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';

export function TopProductsGraph({
  data,
}: {
  data: {
    name: string;
    value: number;
  }[];
}) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <PieChart width={400} height={400}>
        <Pie
          data={data}
          cx={200}
          cy={200}
          labelLine={false}
          outerRadius={80}
          fill="currentColor"
          className="fill-primary"
          dataKey="value"
        >
          {data?.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill="currentColor"
              className="fill-primary"
            />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  );
}
