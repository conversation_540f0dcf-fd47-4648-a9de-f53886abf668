'use client';

import React from 'react';
import { useQueryState } from 'nuqs';

import { DashboardData } from '@/types/dashboard.types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import EmptyState from '../shared/empty-state';
import { AnalyticsTab } from './analytics-tab';
import { OverviewTab } from './overview-tab';

interface DashboardTabsProps {
  dashboardData: DashboardData;
  initialTab: string;
}

export default function DashboardTabs({
  dashboardData,
  initialTab,
}: DashboardTabsProps) {
  const [tab, setTab] = useQueryState('tab', {
    defaultValue: initialTab,
    clearOnDefault: false,
  });

  if (!dashboardData) {
    return (
      <EmptyState
        title="No Data Available"
        description="There is no subscription data to display. Check back soon!"
      />
    );
  }

  return (
    <Tabs value={tab} onValueChange={setTab} className="space-y-4">
      <TabsList className="grid w-full grid-cols-2 md:w-auto md:inline-flex">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
        <TabsTrigger value="reports" disabled className="hidden md:inline-flex">
          Reports
        </TabsTrigger>
      </TabsList>
      <TabsContent value="overview" className="space-y-4">
        <OverviewTab dashboardData={dashboardData} />
      </TabsContent>
      <TabsContent value="analytics" className="space-y-4">
        <AnalyticsTab dashboardData={dashboardData} />
      </TabsContent>
    </Tabs>
  );
}
