import { getDeterministicAvatar } from '@/lib/avatar-utils';
import { getInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { ScrollArea } from '../ui/scroll-area';

interface Sale {
  amount: number;
  email: string | null;
  date: string;
}

interface RecentSalesProps {
  sales: Sale[];
}

export function RecentSalesGraph({ sales }: RecentSalesProps) {
  return (
    <ScrollArea className="h-[350px]">
      <div className="space-y-8">
        {sales.map((sale, index) => (
          <div key={index} className="flex items-center">
            <Avatar className="h-9 w-9">
              <AvatarImage
                src={getDeterministicAvatar(sale.email! || 'unknown')}
                alt="Avatar"
              />
              <AvatarFallback>
                {getInitials(sale.email || 'Unknown')}
              </AvatarFallback>
            </Avatar>
            <div className="ml-4 space-y-1">
              <p className="text-sm font-medium leading-none">
                {sale.email || 'Unknown'}
              </p>
              <p className="text-sm text-muted-foreground">{sale.date}</p>
            </div>
            <div className="ml-auto font-medium">
              +${sale.amount.toFixed(2)}
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}
