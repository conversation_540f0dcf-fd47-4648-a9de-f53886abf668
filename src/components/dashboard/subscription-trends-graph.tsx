'use client';

import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface SubscriptionTrendsGraphProps {
  data: Array<{
    name: string;
    subscriptions: number;
    churn: number;
  }>;
}

export function SubscriptionTrendsGraph({
  data,
}: SubscriptionTrendsGraphProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[350px] text-muted-foreground">
        No subscription data available
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={350}>
      <LineChart data={data}>
        <CartesianGrid
          strokeDasharray="3 3"
          className="stroke-border opacity-30"
        />
        <XAxis dataKey="name" className="text-muted-foreground" />
        <YAxis className="text-muted-foreground" />
        <Tooltip
          contentStyle={{
            backgroundColor: 'hsl(var(--background))',
            borderColor: 'hsl(var(--border))',
            borderRadius: '0.5rem',
          }}
          itemStyle={{
            color: 'hsl(var(--foreground))',
          }}
        />
        <Legend className="text-muted-foreground" />
        <Line
          type="monotone"
          dataKey="subscriptions"
          stroke="hsl(var(--primary))"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 8, fill: 'hsl(var(--primary))' }}
        />
        <Line
          type="monotone"
          dataKey="churn"
          stroke="hsl(var(--destructive))"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 8, fill: 'hsl(var(--destructive))' }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
