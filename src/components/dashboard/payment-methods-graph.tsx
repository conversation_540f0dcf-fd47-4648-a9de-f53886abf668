'use client';

import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  Toolt<PERSON>,
} from 'recharts';

// Using colors from your theme
const COLORS = [
  'hsl(var(--primary))',
  'hsl(var(--accent))',
  'hsl(var(--secondary))',
  'hsl(var(--muted))',
  'hsl(var(--destructive))',
];

// Capitalize and format payment method names
const formatPaymentMethod = (name: string) => {
  return name
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

interface PaymentMethodsGraphProps {
  data: {
    name: string;
    value: number;
  }[];
}

export const PaymentMethodsGraph = ({ data }: PaymentMethodsGraphProps) => {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) =>
              `${formatPaymentMethod(name)} ${(percent * 100).toFixed(0)}%`
            }
          >
            {data.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
                className="dark:opacity-80"
              />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: 'hsl(var(--background))',
              borderColor: 'hsl(var(--border))',
              borderRadius: '0.5rem',
            }}
            itemStyle={{
              color: 'hsl(var(--foreground))',
            }}
          />
          <Legend
            formatter={(value) => formatPaymentMethod(value.toString())}
            className="text-foreground"
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};
