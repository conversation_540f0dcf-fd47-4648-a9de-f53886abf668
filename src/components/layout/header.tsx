import { cn } from '@/lib/utils';

type Props = {
  children: React.ReactNode;
  className?: string;
};
export default function Header({ children, className }: Props) {
  return (
    <div
      data-testid="header"
      className={cn(
        'sticky top-0 z-50 w-full  backdrop-blur supports-[backdrop-filter]:bg-background/60 py-2',
        className
      )}
    >
      {children}
    </div>
  );
}
