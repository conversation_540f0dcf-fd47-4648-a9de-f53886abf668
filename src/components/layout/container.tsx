import { cn } from '@/lib/utils';

type Props = {
  children: React.ReactNode;
  className?: string;
  narrow?: boolean;
  hasVerticalPadding?: boolean;
};
export default function Container({
  children,
  className,
  narrow,
  hasVerticalPadding,
}: Props) {
  return (
    <div
      className={cn(
        'container relative px-4 sm:px-16 flex-1',
        {
          'max-w-4xl mx-auto': narrow,
        },
        {
          'py-4 sm:py-16': hasVerticalPadding,
        },
        className
      )}
    >
      {children}
    </div>
  );
}
