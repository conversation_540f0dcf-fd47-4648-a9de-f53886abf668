import { cn } from '@/lib/utils';

type Props = {
  children: React.ReactNode;
  className?: string;
};

export default function Prose({ children, className }: Props) {
  return (
    <div
      data-testid="prose"
      className={cn(
        'prose prose-md dark:prose-invert prose-headings:font-title font-default focus:outline-none',
        className
      )}
    >
      {children}
    </div>
  );
}
