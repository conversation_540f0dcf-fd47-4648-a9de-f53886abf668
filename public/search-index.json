{"index": {"version": "2.3.9", "fields": ["title", "content"], "fieldVectors": [["title/documentation/delployment", [0, 0.408, 1, 0.218, 2, 0.663, 3, 0.066, 4, 0.663]], ["content/documentation/delployment", [0, 0.947, 1, 0.506, 2, 1.182, 3, 0.135, 4, 1.817, 5, 1.351, 6, 0.28, 7, 1.351, 8, 1.872, 9, 1.351, 10, 1.351, 11, 2.149, 12, 1.351, 13, 1.872, 14, 1.351, 15, 1.351, 16, 1.872, 17, 1.351, 18, 1.351, 19, 0.853, 20, 0.853, 21, 1.872, 22, 1.872, 23, 1.351, 24, 1.872, 25, 1.351, 26, 0.853, 27, 1.351, 28, 1.465, 29, 0.853, 30, 1.465, 31, 1.351, 32, 1.351, 33, 0.835, 34, 1.872, 35, 1.351, 36, 1.539, 37, 1.182, 38, 1.351, 39, 1.351, 40, 0.853, 41, 1.351, 42, 1.351, 43, 2.32, 44, 1.351, 45, 0.947, 46, 1.351, 47, 1.351, 48, 1.872, 49, 1.872, 50, 2.149, 51, 1.465, 52, 0.853, 53, 0.853, 54, 1.351, 55, 1.351, 56, 1.872, 57, 2.149, 58, 1.351, 59, 1.351, 60, 2.149, 61, 1.182, 62, 1.351, 63, 1.351, 64, 1.351, 65, 1.351, 66, 1.351, 67, 1.351, 68, 0.853, 69, 1.351, 70, 1.351, 71, 1.351, 72, 1.351, 73, 1.872, 74, 1.182, 75, 1.351, 76, 1.351, 77, 1.351, 78, 1.872, 79, 1.351, 80, 1.351, 81, 1.351, 82, 0.728, 83, 0.853, 84, 1.351, 85, 0.525, 86, 1.351, 87, 0.853, 88, 0.853, 89, 0.853, 90, 0.853, 91, 0.853]], ["title/documentation/installation", [6, 0.39]], ["content/documentation/installation", [6, 0.479, 33, 0.897, 85, 0.897, 92, 1.456]], ["title/documentation/search", [3, 0.059, 93, 0.367, 94, 0.367, 95, 0.367, 96, 0.597, 97, 0.367]], ["content/documentation/search", [1, 0.437, 3, 0.082, 40, 0.82, 45, 0.994, 52, 1.329, 61, 1.329, 82, 0.708, 93, 0.818, 94, 1.056, 95, 0.968, 96, 0.82, 97, 0.505, 98, 0.82, 99, 0.82, 100, 1.299, 101, 0.82, 102, 1.44, 103, 1.822, 104, 1.15, 105, 1.675, 106, 1.299, 107, 1.648, 108, 1.299, 109, 1.299, 110, 1.299, 111, 1.15, 112, 1.329, 113, 1.822, 114, 1.299, 115, 0.82, 116, 1.822, 117, 1.329, 118, 0.82, 119, 1.822, 120, 1.299, 121, 1.299, 122, 1.299, 123, 1.299, 124, 1.299, 125, 1.517, 126, 1.299, 127, 1.517, 128, 1.44, 129, 1.822, 130, 2.61, 131, 1.822, 132, 1.15, 133, 1.299, 134, 1.517, 135, 0.82, 136, 1.15, 137, 1.299, 138, 1.299, 139, 1.299, 140, 1.15, 141, 1.299, 142, 1.299, 143, 1.15, 144, 1.822, 145, 1.822, 146, 1.299, 147, 1.299, 148, 1.299, 149, 0.82, 150, 1.299, 151, 1.299, 152, 1.299, 153, 1.299, 154, 1.299, 155, 1.299, 156, 1.299, 157, 1.299, 158, 1.15, 159, 1.822, 160, 0.82, 161, 1.299, 162, 1.299, 163, 1.299, 164, 1.822, 165, 0.82, 166, 1.299, 167, 0.82, 168, 1.299, 169, 1.299, 170, 1.299, 171, 1.299, 172, 1.299, 173, 1.299, 174, 1.299, 175, 1.299, 176, 1.299, 177, 0.82, 178, 1.299, 179, 0.82]], ["title/documentation/testing", [180, 1.188]], ["content/documentation/testing", [181, 2.296, 182, 2.296, 183, 1.654, 184, 2.296]], ["title/index", [185, 1.188]], ["content/index", [0, 0.703, 1, 0.409, 3, 0.081, 6, 0.267, 19, 0.812, 20, 0.515, 26, 1.143, 28, 1.006, 29, 0.515, 30, 0.812, 33, 0.317, 36, 0.515, 37, 0.515, 45, 0.703, 51, 0.515, 53, 0.812, 68, 0.515, 74, 0.515, 82, 0.814, 83, 0.515, 85, 0.5, 87, 0.515, 88, 0.515, 89, 0.515, 90, 0.515, 91, 0.515, 92, 0.812, 93, 0.619, 94, 0.317, 95, 0.5, 97, 0.317, 98, 0.515, 99, 0.515, 101, 0.515, 102, 0.515, 104, 0.515, 105, 1.006, 107, 0.812, 111, 1.244, 112, 0.515, 115, 1.006, 117, 1.143, 118, 1.006, 125, 0.515, 127, 0.515, 128, 0.515, 132, 0.812, 134, 0.515, 135, 0.812, 136, 0.515, 140, 1.006, 143, 0.515, 149, 0.812, 158, 0.812, 160, 0.812, 165, 1.006, 167, 1.476, 177, 0.515, 179, 0.812, 180, 0.812, 183, 0.515, 185, 0.515, 186, 1.969, 187, 0.815, 188, 1.593, 189, 2.191, 190, 0.815, 191, 1.809, 192, 0.815, 193, 1.969, 194, 0.815, 195, 0.815, 196, 0.815, 197, 0.815, 198, 1.593, 199, 0.815, 200, 0.815, 201, 0.815, 202, 1.593, 203, 0.815, 204, 1.969, 205, 0.815, 206, 1.593, 207, 1.286, 208, 0.815, 209, 0.815, 210, 0.815, 211, 1.286, 212, 1.286, 213, 1.809, 214, 2.093, 215, 0.815, 216, 1.593, 217, 1.809, 218, 1.286, 219, 1.286, 220, 0.815, 221, 0.815, 222, 0.815, 223, 1.593, 224, 0.815, 225, 0.815, 226, 1.286, 227, 0.815, 228, 1.286, 229, 1.286, 230, 1.286, 231, 0.815, 232, 0.815, 233, 0.815, 234, 1.809, 235, 0.815, 236, 1.809, 237, 0.815, 238, 1.593, 239, 0.815, 240, 0.815, 241, 1.593, 242, 0.815, 243, 0.815, 244, 0.815, 245, 0.815, 246, 1.286, 247, 1.286, 248, 0.815, 249, 0.815, 250, 1.286, 251, 0.815, 252, 0.815, 253, 0.815, 254, 0.815, 255, 0.815, 256, 1.286, 257, 0.815, 258, 1.286, 259, 1.593, 260, 0.815, 261, 0.815, 262, 0.815, 263, 1.593, 264, 0.815, 265, 1.286, 266, 0.815, 267, 0.815, 268, 0.815, 269, 0.815, 270, 0.815, 271, 0.815, 272, 0.815, 273, 0.815, 274, 0.815, 275, 0.815, 276, 0.815, 277, 0.815, 278, 0.815, 279, 0.815, 280, 0.815, 281, 1.286, 282, 0.815, 283, 1.286, 284, 0.815, 285, 0.815, 286, 0.815, 287, 0.815, 288, 0.815, 289, 0.815, 290, 0.815, 291, 0.815, 292, 0.815, 293, 0.815, 294, 0.815, 295, 0.815, 296, 0.815, 297, 1.286, 298, 0.815, 299, 0.815, 300, 0.815, 301, 0.815, 302, 1.286, 303, 0.815, 304, 0.815, 305, 0.815, 306, 0.815, 307, 0.815, 308, 0.815, 309, 0.815, 310, 0.815, 311, 0.815, 312, 0.815, 313, 0.815, 314, 0.815, 315, 0.815, 316, 0.815, 317, 0.815, 318, 1.809, 319, 0.815, 320, 0.815, 321, 0.815, 322, 0.815, 323, 0.815, 324, 0.815, 325, 0.815, 326, 0.815, 327, 1.593, 328, 1.286, 329, 0.815, 330, 0.815, 331, 0.815, 332, 0.815, 333, 0.815, 334, 0.815, 335, 0.815, 336, 0.815, 337, 1.286, 338, 1.286, 339, 0.815, 340, 1.286, 341, 0.815, 342, 0.815, 343, 0.815, 344, 0.815, 345, 0.815, 346, 0.815, 347, 0.815, 348, 0.815, 349, 0.815, 350, 0.815, 351, 0.815, 352, 0.815, 353, 0.815, 354, 0.815, 355, 0.815, 356, 1.286, 357, 0.815, 358, 0.815, 359, 0.815, 360, 0.815, 361, 0.815, 362, 0.815, 363, 0.815, 364, 0.815, 365, 0.815, 366, 0.815, 367, 0.815, 368, 0.815, 369, 0.815, 370, 0.815, 371, 0.815, 372, 0.815, 373, 0.815, 374, 0.815, 375, 0.815, 376, 0.815, 377, 1.286, 378, 0.815, 379, 0.815, 380, 0.815, 381, 0.815, 382, 0.815, 383, 0.815, 384, 0.815, 385, 0.815, 386, 0.815, 387, 0.815, 388, 0.815, 389, 0.815, 390, 0.815, 391, 0.815, 392, 0.815, 393, 0.815, 394, 0.815, 395, 1.286, 396, 0.815, 397, 0.815, 398, 1.593, 399, 0.815, 400, 0.815]]], "invertedIndex": [["1", {"_index": 15, "title": {}, "content": {"documentation/delployment": {"position": [[154, 2]]}}}], ["13", {"_index": 2, "title": {"documentation/delployment": {"position": [[20, 3]]}}, "content": {"documentation/delployment": {"position": [[82, 3], [1643, 3]]}}}], ["14", {"_index": 201, "title": {}, "content": {"index": {"position": [[301, 3]]}}}], ["2", {"_index": 23, "title": {}, "content": {"documentation/delployment": {"position": [[246, 2]]}}}], ["3", {"_index": 31, "title": {}, "content": {"documentation/delployment": {"position": [[400, 2]]}}}], ["3000", {"_index": 49, "title": {}, "content": {"documentation/delployment": {"position": [[722, 4], [756, 4]]}}}], ["4", {"_index": 42, "title": {}, "content": {"documentation/delployment": {"position": [[596, 2]]}}}], ["5", {"_index": 55, "title": {}, "content": {"documentation/delployment": {"position": [[864, 2]]}}}], ["6", {"_index": 66, "title": {}, "content": {"documentation/delployment": {"position": [[1129, 2]]}}}], ["access", {"_index": 53, "title": {}, "content": {"documentation/delployment": {"position": [[802, 6]]}, "index": {"position": [[1289, 10], [1392, 10]]}}}], ["<PERSON><PERSON><PERSON>", {"_index": 113, "title": {}, "content": {"documentation/search": {"position": [[269, 7], [1321, 8]]}}}], ["adapt", {"_index": 229, "title": {}, "content": {"index": {"position": [[772, 8], [1434, 5]]}}}], ["add", {"_index": 302, "title": {}, "content": {"index": {"position": [[2236, 3], [3830, 3]]}}}], ["addit", {"_index": 368, "title": {}, "content": {"index": {"position": [[3863, 10]]}}}], ["algolia", {"_index": 159, "title": {}, "content": {"documentation/search": {"position": [[1367, 7], [1933, 8]]}}}], ["algoliaindex", {"_index": 163, "title": {}, "content": {"documentation/search": {"position": [[1438, 15]]}}}], ["algolia’", {"_index": 164, "title": {}, "content": {"documentation/search": {"position": [[1467, 9], [1553, 9]]}}}], ["allow", {"_index": 52, "title": {}, "content": {"documentation/delployment": {"position": [[786, 8]]}, "documentation/search": {"position": [[118, 6], [1644, 6], [2019, 6]]}}}], ["alt", {"_index": 184, "title": {}, "content": {"documentation/testing": {"position": [[17, 3]]}}}], ["api", {"_index": 165, "title": {}, "content": {"documentation/search": {"position": [[1477, 3]]}, "index": {"position": [[1708, 3], [1736, 3], [1852, 3]]}}}], ["app", {"_index": 211, "title": {}, "content": {"index": {"position": [[463, 3], [3470, 3]]}}}], ["applic", {"_index": 30, "title": {}, "content": {"documentation/delployment": {"position": [[363, 12], [822, 11], [1347, 11], [1701, 11]]}, "index": {"position": [[2789, 11], [3968, 11]]}}}], ["application/app", {"_index": 352, "title": {}, "content": {"index": {"position": [[3445, 16]]}}}], ["applicationcustomizationth", {"_index": 363, "title": {}, "content": {"index": {"position": [[3718, 27]]}}}], ["approach", {"_index": 87, "title": {}, "content": {"documentation/delployment": {"position": [[1674, 8]]}, "index": {"position": [[1151, 9]]}}}], ["authent", {"_index": 226, "title": {}, "content": {"index": {"position": [[729, 14], [867, 14]]}}}], ["automat", {"_index": 291, "title": {}, "content": {"index": {"position": [[2082, 9]]}}}], ["aw", {"_index": 318, "title": {}, "content": {"index": {"position": [[2646, 3], [2677, 3], [2741, 3], [4020, 4]]}}}], ["backend", {"_index": 274, "title": {}, "content": {"index": {"position": [[1755, 7]]}}}], ["base", {"_index": 152, "title": {}, "content": {"documentation/search": {"position": [[1095, 5]]}}}], ["beauti", {"_index": 245, "title": {}, "content": {"index": {"position": [[1098, 9]]}}}], ["begin", {"_index": 331, "title": {}, "content": {"index": {"position": [[3049, 5]]}}}], ["benefit", {"_index": 206, "title": {}, "content": {"index": {"position": [[395, 7], [967, 7], [1897, 7]]}}}], ["best", {"_index": 198, "title": {}, "content": {"index": {"position": [[266, 4], [1923, 4], [2942, 4]]}}}], ["better", {"_index": 220, "title": {}, "content": {"index": {"position": [[609, 6]]}}}], ["blaze", {"_index": 294, "title": {}, "content": {"index": {"position": [[2154, 7]]}}}], ["blog", {"_index": 98, "title": {}, "content": {"documentation/search": {"position": [[8, 4]]}, "index": {"position": [[1592, 4]]}}}], ["boilerpl", {"_index": 189, "title": {}, "content": {"index": {"position": [[99, 12], [139, 11], [3066, 12], [3314, 11], [3746, 11], [4174, 12], [4406, 12]]}}}], ["boilerplatewelcom", {"_index": 187, "title": {}, "content": {"index": {"position": [[24, 18]]}}}], ["box.ui", {"_index": 257, "title": {}, "content": {"index": {"position": [[1264, 6]]}}}], ["break", {"_index": 108, "title": {}, "content": {"documentation/search": {"position": [[217, 5]]}}}], ["build", {"_index": 28, "title": {}, "content": {"documentation/delployment": {"position": [[336, 8], [403, 5], [471, 5], [629, 8]]}, "index": {"position": [[1169, 5], [1283, 5], [4390, 5]]}}}], ["built", {"_index": 281, "title": {}, "content": {"index": {"position": [[1910, 5], [2297, 5]]}}}], ["check", {"_index": 375, "title": {}, "content": {"index": {"position": [[4043, 5]]}}}], ["clean", {"_index": 348, "title": {}, "content": {"index": {"position": [[3379, 5]]}}}], ["clone", {"_index": 339, "title": {}, "content": {"index": {"position": [[3218, 5]]}}}], ["code", {"_index": 214, "title": {}, "content": {"index": {"position": [[500, 4], [546, 4], [616, 4], [2092, 4], [3374, 4], [3433, 4]]}}}], ["coding!licensethi", {"_index": 397, "title": {}, "content": {"index": {"position": [[4425, 18]]}}}], ["collaboration.authent", {"_index": 224, "title": {}, "content": {"index": {"position": [[673, 29]]}}}], ["combin", {"_index": 116, "title": {}, "content": {"documentation/search": {"position": [[352, 11], [1814, 9]]}}}], ["command", {"_index": 34, "title": {}, "content": {"documentation/delployment": {"position": [[443, 7], [704, 7]]}}}], ["command:thi", {"_index": 46, "title": {}, "content": {"documentation/delployment": {"position": [[691, 12]]}}}], ["commun", {"_index": 381, "title": {}, "content": {"index": {"position": [[4139, 9]]}}}], ["communityjoin", {"_index": 379, "title": {}, "content": {"index": {"position": [[4113, 13]]}}}], ["complex", {"_index": 80, "title": {}, "content": {"documentation/delployment": {"position": [[1472, 7]]}}}], ["compon", {"_index": 111, "title": {}, "content": {"documentation/search": {"position": [[236, 10], [1743, 9]]}, "index": {"position": [[1271, 11], [1403, 10], [1657, 9], [2460, 10], [3838, 11]]}}}], ["components.search", {"_index": 168, "title": {}, "content": {"documentation/search": {"position": [[1596, 17]]}}}], ["components/lib", {"_index": 355, "title": {}, "content": {"index": {"position": [[3526, 15]]}}}], ["compos", {"_index": 78, "title": {}, "content": {"documentation/delployment": {"position": [[1454, 8], [1515, 7]]}}}], ["comprehens", {"_index": 188, "title": {}, "content": {"index": {"position": [[50, 13], [1361, 13], [2409, 13]]}}}], ["configur", {"_index": 327, "title": {}, "content": {"index": {"position": [[2918, 13], [3266, 9], [3673, 13]]}}}], ["configuration/typ", {"_index": 360, "title": {}, "content": {"index": {"position": [[3616, 20]]}}}], ["consid", {"_index": 81, "title": {}, "content": {"documentation/delployment": {"position": [[1493, 8]]}}}], ["consist", {"_index": 89, "title": {}, "content": {"documentation/delployment": {"position": [[1718, 12]]}, "index": {"position": [[2805, 10]]}}}], ["contain", {"_index": 51, "title": {}, "content": {"documentation/delployment": {"position": [[775, 10], [954, 9], [1419, 9], [1542, 10]]}, "index": {"position": [[3408, 8]]}}}], ["container", {"_index": 323, "title": {}, "content": {"index": {"position": [[2771, 12]]}}}], ["container:addit", {"_index": 71, "title": {}, "content": {"documentation/delployment": {"position": [[1283, 20]]}}}], ["containeraft", {"_index": 44, "title": {}, "content": {"documentation/delployment": {"position": [[614, 14]]}}}], ["content", {"_index": 107, "title": {}, "content": {"documentation/search": {"position": [[200, 8], [496, 7], [632, 7], [801, 7], [1087, 7], [1279, 7], [1454, 8], [1494, 7]]}, "index": {"position": [[1498, 7], [1614, 7]]}}}], ["content.cont", {"_index": 137, "title": {}, "content": {"documentation/search": {"position": [[757, 15]]}}}], ["continu", {"_index": 328, "title": {}, "content": {"index": {"position": [[2972, 10], [2999, 10]]}}}], ["contribut", {"_index": 383, "title": {}, "content": {"index": {"position": [[4225, 10]]}}}], ["convert", {"_index": 146, "title": {}, "content": {"documentation/search": {"position": [[941, 7]]}}}], ["core", {"_index": 298, "title": {}, "content": {"index": {"position": [[2191, 4]]}}}], ["<PERSON><PERSON>", {"_index": 18, "title": {}, "content": {"documentation/delployment": {"position": [[204, 9]]}}}], ["craft", {"_index": 244, "title": {}, "content": {"index": {"position": [[1092, 5]]}}}], ["creat", {"_index": 265, "title": {}, "content": {"index": {"position": [[1486, 6], [1748, 6]]}}}], ["css", {"_index": 247, "title": {}, "content": {"index": {"position": [[1133, 3], [3612, 3]]}}}], ["custom", {"_index": 160, "title": {}, "content": {"documentation/search": {"position": [[1380, 6]]}, "index": {"position": [[1175, 6], [1947, 13]]}}}], ["customiz", {"_index": 258, "title": {}, "content": {"index": {"position": [[1304, 12], [3783, 13]]}}}], ["databas", {"_index": 238, "title": {}, "content": {"index": {"position": [[946, 8], [990, 8], [1064, 8]]}}}], ["definitions/config", {"_index": 361, "title": {}, "content": {"index": {"position": [[3653, 19]]}}}], ["depend", {"_index": 341, "title": {}, "content": {"index": {"position": [[3248, 13]]}}}], ["deploy", {"_index": 0, "title": {"documentation/delployment": {"position": [[0, 9]]}}, "content": {"documentation/delployment": {"position": [[935, 6], [1132, 6], [1480, 12], [1623, 6], [1793, 7]]}, "index": {"position": [[2832, 10], [3010, 10], [3948, 9], [4057, 10]]}}}], ["design", {"_index": 191, "title": {}, "content": {"index": {"position": [[154, 8], [1182, 7], [1448, 6], [3761, 8]]}}}], ["detail", {"_index": 377, "title": {}, "content": {"index": {"position": [[4079, 8], [4512, 8]]}}}], ["develop", {"_index": 193, "title": {}, "content": {"index": {"position": [[194, 12], [373, 9], [663, 9], [2816, 11], [4152, 10]]}}}], ["differ", {"_index": 90, "title": {}, "content": {"documentation/delployment": {"position": [[1738, 9]]}, "index": {"position": [[1054, 9]]}}}], ["digitalocean", {"_index": 374, "title": {}, "content": {"index": {"position": [[4029, 13]]}}}], ["directori", {"_index": 40, "title": {}, "content": {"documentation/delployment": {"position": [[525, 9]]}, "documentation/search": {"position": [[869, 10]]}}}], ["discussions.we'r", {"_index": 393, "title": {}, "content": {"index": {"position": [[4345, 17]]}}}], ["display", {"_index": 269, "title": {}, "content": {"index": {"position": [[1569, 7]]}}}], ["doc", {"_index": 96, "title": {"documentation/search": {"position": [[41, 7]]}}, "content": {"documentation/search": {"position": [[90, 5]]}}}], ["docker", {"_index": 4, "title": {"documentation/delployment": {"position": [[37, 6]]}}, "content": {"documentation/delployment": {"position": [[413, 6], [481, 6], [607, 6], [642, 6], [768, 6], [876, 6], [894, 6], [947, 6], [1010, 6], [1028, 6], [1049, 6], [1069, 6], [1089, 6], [1111, 6], [1197, 6], [1219, 6], [1255, 6], [1276, 6], [1412, 6], [1508, 6], [1661, 7]]}}}], ["dockerfil", {"_index": 11, "title": {}, "content": {"documentation/delployment": {"position": [[101, 10], [190, 10], [569, 10]]}}}], ["docker<PERSON><PERSON><PERSON><PERSON>", {"_index": 17, "title": {}, "content": {"documentation/delployment": {"position": [[168, 16]]}}}], ["document", {"_index": 105, "title": {}, "content": {"documentation/search": {"position": [[151, 13], [322, 13], [482, 13], [595, 13], [787, 13], [1028, 13], [1191, 13], [1294, 14], [1999, 14]]}, "index": {"position": [[64, 13], [1471, 14], [1577, 14]]}}}], ["down", {"_index": 109, "title": {}, "content": {"documentation/search": {"position": [[223, 4]]}}}], ["e", {"_index": 76, "title": {}, "content": {"documentation/delployment": {"position": [[1440, 1]]}}}], ["each", {"_index": 285, "title": {}, "content": {"index": {"position": [[1977, 4]]}}}], ["eas", {"_index": 304, "title": {}, "content": {"index": {"position": [[2276, 4]]}}}], ["easi", {"_index": 241, "title": {}, "content": {"index": {"position": [[1024, 4], [1942, 4], [2696, 4]]}}}], ["easier", {"_index": 91, "title": {}, "content": {"documentation/delployment": {"position": [[1772, 6]]}, "index": {"position": [[630, 6]]}}}], ["easili", {"_index": 263, "title": {}, "content": {"index": {"position": [[1427, 6], [1551, 6], [3805, 6]]}}}], ["editor", {"_index": 268, "title": {}, "content": {"index": {"position": [[1543, 7]]}}}], ["effici", {"_index": 132, "title": {}, "content": {"documentation/search": {"position": [[661, 9], [1949, 11]]}, "index": {"position": [[478, 9], [936, 9]]}}}], ["enabl", {"_index": 131, "title": {}, "content": {"documentation/search": {"position": [[654, 6], [1229, 6]]}}}], ["endpoint", {"_index": 273, "title": {}, "content": {"index": {"position": [[1712, 9]]}}}], ["engin", {"_index": 286, "title": {}, "content": {"index": {"position": [[2008, 6]]}}}], ["enhanc", {"_index": 177, "title": {}, "content": {"documentation/search": {"position": [[2075, 9]]}, "index": {"position": [[555, 8]]}}}], ["enjoy", {"_index": 216, "title": {}, "content": {"index": {"position": [[530, 5], [1221, 5], [2076, 5]]}}}], ["ensur", {"_index": 88, "title": {}, "content": {"documentation/delployment": {"position": [[1683, 7]]}, "index": {"position": [[601, 7]]}}}], ["environ", {"_index": 74, "title": {}, "content": {"documentation/delployment": {"position": [[1364, 11], [1748, 13]]}, "index": {"position": [[2843, 12]]}}}], ["environment.project", {"_index": 342, "title": {}, "content": {"index": {"position": [[3281, 19]]}}}], ["example:step", {"_index": 22, "title": {}, "content": {"documentation/delployment": {"position": [[233, 12], [387, 12]]}}}], ["excit", {"_index": 394, "title": {}, "content": {"index": {"position": [[4363, 7]]}}}], ["experi", {"_index": 179, "title": {}, "content": {"documentation/search": {"position": [[2099, 11]]}, "index": {"position": [[383, 11], [4208, 12]]}}}], ["explor", {"_index": 100, "title": {}, "content": {"documentation/search": {"position": [[27, 7]]}}}], ["fast", {"_index": 295, "title": {}, "content": {"index": {"position": [[2162, 4]]}}}], ["featur", {"_index": 101, "title": {}, "content": {"documentation/search": {"position": [[110, 7]]}, "index": {"position": [[325, 8]]}}}], ["featuresnext.j", {"_index": 200, "title": {}, "content": {"index": {"position": [[285, 15]]}}}], ["fetch", {"_index": 151, "title": {}, "content": {"documentation/search": {"position": [[1075, 7]]}}}], ["file", {"_index": 140, "title": {}, "content": {"documentation/search": {"position": [[831, 5], [886, 5]]}, "index": {"position": [[2932, 5], [3687, 5], [4503, 4]]}}}], ["files.search", {"_index": 166, "title": {}, "content": {"documentation/search": {"position": [[1519, 12]]}}}], ["files.stat", {"_index": 126, "title": {}, "content": {"documentation/search": {"position": [[527, 12]]}}}], ["find", {"_index": 104, "title": {}, "content": {"documentation/search": {"position": [[142, 4], [2043, 4]]}, "index": {"position": [[3181, 4]]}}}], ["first", {"_index": 248, "title": {}, "content": {"index": {"position": [[1145, 5]]}}}], ["fit", {"_index": 369, "title": {}, "content": {"index": {"position": [[3886, 3]]}}}], ["flag.dock", {"_index": 77, "title": {}, "content": {"documentation/delployment": {"position": [[1442, 11]]}}}], ["flexibl", {"_index": 190, "title": {}, "content": {"index": {"position": [[130, 8]]}}}], ["follow", {"_index": 33, "title": {}, "content": {"documentation/delployment": {"position": [[433, 9], [681, 9], [1579, 9]]}, "documentation/installation": {"position": [[0, 6]]}, "index": {"position": [[3326, 7]]}}}], ["font", {"_index": 293, "title": {}, "content": {"index": {"position": [[2132, 4]]}}}], ["form", {"_index": 278, "title": {}, "content": {"index": {"position": [[1834, 4]]}}}], ["foundat", {"_index": 195, "title": {}, "content": {"index": {"position": [[226, 10]]}}}], ["full", {"_index": 270, "title": {}, "content": {"index": {"position": [[1633, 4]]}}}], ["fulli", {"_index": 253, "title": {}, "content": {"index": {"position": [[1229, 5]]}}}], ["function", {"_index": 95, "title": {"documentation/search": {"position": [[20, 13]]}}, "content": {"documentation/search": {"position": [[69, 13], [301, 13], [1066, 8], [1147, 8], [1243, 14], [1978, 13]]}, "index": {"position": [[1763, 13], [3550, 9]]}}}], ["gener", {"_index": 127, "title": {}, "content": {"documentation/search": {"position": [[540, 11], [582, 8], [1015, 8], [1156, 9], [1843, 11]]}, "index": {"position": [[443, 11]]}}}], ["generatestaticparam", {"_index": 155, "title": {}, "content": {"documentation/search": {"position": [[1126, 20]]}}}], ["generationnext.j", {"_index": 148, "title": {}, "content": {"documentation/search": {"position": [[975, 17]]}}}], ["get", {"_index": 336, "title": {}, "content": {"index": {"position": [[3142, 7]]}}}], ["getdocbyslug", {"_index": 150, "title": {}, "content": {"documentation/search": {"position": [[1053, 12]]}}}], ["github", {"_index": 387, "title": {}, "content": {"index": {"position": [[4271, 6]]}}}], ["global", {"_index": 358, "title": {}, "content": {"index": {"position": [[3585, 6]]}}}], ["growth", {"_index": 385, "title": {}, "content": {"index": {"position": [[4253, 7]]}}}], ["guid", {"_index": 376, "title": {}, "content": {"index": {"position": [[4068, 6]]}}}], ["guidanc", {"_index": 338, "title": {}, "content": {"index": {"position": [[3199, 8], [3936, 8]]}}}], ["handl", {"_index": 277, "title": {}, "content": {"index": {"position": [[1825, 8]]}}}], ["happi", {"_index": 396, "title": {}, "content": {"index": {"position": [[4419, 5]]}}}], ["help", {"_index": 382, "title": {}, "content": {"index": {"position": [[4191, 5]]}}}], ["here", {"_index": 21, "title": {}, "content": {"documentation/delployment": {"position": [[222, 4], [376, 4]]}}}], ["here’", {"_index": 120, "title": {}, "content": {"documentation/search": {"position": [[418, 6]]}}}], ["high", {"_index": 121, "title": {}, "content": {"documentation/search": {"position": [[427, 4]]}}}], ["highli", {"_index": 364, "title": {}, "content": {"index": {"position": [[3776, 6]]}}}], ["home", {"_index": 181, "title": {}, "content": {"documentation/testing": {"position": [[0, 4]]}}}], ["host", {"_index": 50, "title": {}, "content": {"documentation/delployment": {"position": [[735, 4], [976, 5], [1170, 5]]}}}], ["host.a", {"_index": 10, "title": {}, "content": {"documentation/delployment": {"position": [[67, 6]]}}}], ["hoston", {"_index": 67, "title": {}, "content": {"documentation/delployment": {"position": [[1151, 6]]}}}], ["html", {"_index": 252, "title": {}, "content": {"index": {"position": [[1211, 5]]}}}], ["html.static", {"_index": 147, "title": {}, "content": {"documentation/search": {"position": [[963, 11]]}}}], ["http://localhost:3000.step", {"_index": 54, "title": {}, "content": {"documentation/delployment": {"position": [[837, 26]]}}}], ["hub.login", {"_index": 62, "title": {}, "content": {"documentation/delployment": {"position": [[1056, 9]]}}}], ["hub:tag", {"_index": 63, "title": {}, "content": {"documentation/delployment": {"position": [[1076, 7]]}}}], ["i18n", {"_index": 306, "title": {}, "content": {"index": {"position": [[2306, 4]]}}}], ["imag", {"_index": 36, "title": {}, "content": {"documentation/delployment": {"position": [[488, 6], [649, 6], [883, 5], [1017, 5], [1204, 5]]}, "index": {"position": [[2108, 5]]}}}], ["image:push", {"_index": 64, "title": {}, "content": {"documentation/delployment": {"position": [[1096, 10]]}}}], ["image:run", {"_index": 70, "title": {}, "content": {"documentation/delployment": {"position": [[1262, 9]]}}}], ["image:step", {"_index": 65, "title": {}, "content": {"documentation/delployment": {"position": [[1118, 10]]}}}], ["imagerun", {"_index": 32, "title": {}, "content": {"documentation/delployment": {"position": [[420, 8]]}}}], ["implement", {"_index": 93, "title": {"documentation/search": {"position": [[0, 12]]}}, "content": {"documentation/search": {"position": [[51, 10], [1720, 11], [1961, 9]]}, "index": {"position": [[781, 9], [1687, 9], [2501, 9]]}}}], ["improv", {"_index": 223, "title": {}, "content": {"index": {"position": [[654, 8], [1988, 7], [2182, 8]]}}}], ["includ", {"_index": 26, "title": {}, "content": {"documentation/delployment": {"position": [[301, 8]]}, "index": {"position": [[2550, 9], [2668, 8], [2909, 8], [4002, 9]]}}}], ["index", {"_index": 130, "title": {}, "content": {"documentation/search": {"position": [[622, 9], [643, 7], [749, 7], [1269, 5], [1345, 8], [1484, 5], [1784, 8], [1911, 8]]}}}], ["indexingto", {"_index": 157, "title": {}, "content": {"documentation/search": {"position": [[1218, 10]]}}}], ["inform", {"_index": 176, "title": {}, "content": {"documentation/search": {"position": [[2052, 11]]}}}], ["input", {"_index": 170, "title": {}, "content": {"documentation/search": {"position": [[1660, 5]]}}}], ["instal", {"_index": 6, "title": {"documentation/installation": {"position": [[0, 12]]}}, "content": {"documentation/delployment": {"position": [[20, 9]]}, "documentation/installation": {"position": [[22, 7]]}, "index": {"position": [[3099, 12], [3240, 7]]}}}], ["instruct", {"_index": 335, "title": {}, "content": {"index": {"position": [[3122, 12]]}}}], ["instructions.support", {"_index": 378, "title": {}, "content": {"index": {"position": [[4088, 20]]}}}], ["integr", {"_index": 167, "title": {}, "content": {"documentation/search": {"position": [[1543, 9]]}, "index": {"position": [[589, 11], [714, 9], [1856, 13], [2432, 11], [2488, 12], [2611, 12], [2704, 9], [2983, 11], [3853, 9]]}}}], ["interact", {"_index": 174, "title": {}, "content": {"documentation/search": {"position": [[1758, 9]]}}}], ["interfac", {"_index": 134, "title": {}, "content": {"documentation/search": {"position": [[685, 10], [705, 9], [1532, 10], [1570, 9], [1634, 9]]}, "index": {"position": [[1108, 10]]}}}], ["interfaceth", {"_index": 169, "title": {}, "content": {"documentation/search": {"position": [[1614, 12]]}}}], ["intl", {"_index": 307, "title": {}, "content": {"index": {"position": [[2329, 4]]}}}], ["issu", {"_index": 389, "title": {}, "content": {"index": {"position": [[4297, 7]]}}}], ["it.pul", {"_index": 69, "title": {}, "content": {"documentation/delployment": {"position": [[1243, 7]]}}}], ["jest", {"_index": 310, "title": {}, "content": {"index": {"position": [[2374, 4]]}}}], ["jumpstart", {"_index": 192, "title": {}, "content": {"index": {"position": [[166, 9]]}}}], ["keep", {"_index": 347, "title": {}, "content": {"index": {"position": [[3364, 4]]}}}], ["key", {"_index": 110, "title": {}, "content": {"documentation/search": {"position": [[232, 3]]}}}], ["lambda", {"_index": 321, "title": {}, "content": {"index": {"position": [[2723, 7]]}}}], ["languag", {"_index": 303, "title": {}, "content": {"index": {"position": [[2261, 9]]}}}], ["latest", {"_index": 203, "title": {}, "content": {"index": {"position": [[318, 6]]}}}], ["layout", {"_index": 255, "title": {}, "content": {"index": {"position": [[1246, 6]]}}}], ["layouts/compon", {"_index": 353, "title": {}, "content": {"index": {"position": [[3491, 19]]}}}], ["leav", {"_index": 251, "title": {}, "content": {"index": {"position": [[1198, 7]]}}}], ["level", {"_index": 122, "title": {}, "content": {"documentation/search": {"position": [[432, 5]]}}}], ["leverag", {"_index": 202, "title": {}, "content": {"index": {"position": [[305, 8], [1350, 8], [2624, 8]]}}}], ["librari", {"_index": 112, "title": {}, "content": {"documentation/search": {"position": [[251, 9], [407, 10], [1402, 9]]}, "index": {"position": [[2397, 7]]}}}], ["licens", {"_index": 398, "title": {}, "content": {"index": {"position": [[4455, 8], [4478, 8], [4495, 7]]}}}], ["link", {"_index": 182, "title": {}, "content": {"documentation/testing": {"position": [[6, 4]]}}}], ["load", {"_index": 296, "title": {}, "content": {"index": {"position": [[2167, 4]]}}}], ["local", {"_index": 7, "title": {}, "content": {"documentation/delployment": {"position": [[38, 5]]}}}], ["locat", {"_index": 141, "title": {}, "content": {"documentation/search": {"position": [[837, 7]]}}}], ["located.step", {"_index": 41, "title": {}, "content": {"documentation/delployment": {"position": [[583, 12]]}}}], ["logic/styl", {"_index": 357, "title": {}, "content": {"index": {"position": [[3571, 13]]}}}], ["login", {"_index": 231, "title": {}, "content": {"index": {"position": [[798, 6]]}}}], ["lunr.js.exampl", {"_index": 162, "title": {}, "content": {"documentation/search": {"position": [[1417, 15]]}}}], ["machin", {"_index": 8, "title": {}, "content": {"documentation/delployment": {"position": [[44, 7], [740, 7]]}}}], ["main", {"_index": 350, "title": {}, "content": {"index": {"position": [[3421, 4]]}}}], ["maintainable:/src", {"_index": 349, "title": {}, "content": {"index": {"position": [[3389, 18]]}}}], ["make", {"_index": 37, "title": {}, "content": {"documentation/delployment": {"position": [[495, 4], [1762, 6]]}, "index": {"position": [[2686, 6]]}}}], ["manag", {"_index": 82, "title": {}, "content": {"documentation/delployment": {"position": [[1526, 6], [1782, 6]]}, "documentation/search": {"position": [[470, 11], [507, 7]]}, "index": {"position": [[831, 10], [901, 11], [1036, 10], [1558, 6], [1785, 8], [2573, 10]]}}}], ["managementour", {"_index": 138, "title": {}, "content": {"documentation/search": {"position": [[773, 13]]}}}], ["map", {"_index": 47, "title": {}, "content": {"documentation/delployment": {"position": [[712, 4]]}}}], ["markdown", {"_index": 125, "title": {}, "content": {"documentation/search": {"position": [[518, 8], [822, 8], [949, 8], [1510, 8], [1877, 8]]}, "index": {"position": [[1638, 8]]}}}], ["mdx", {"_index": 267, "title": {}, "content": {"index": {"position": [[1511, 3]]}}}], ["media", {"_index": 289, "title": {}, "content": {"index": {"position": [[2035, 5]]}}}], ["metadata", {"_index": 284, "title": {}, "content": {"index": {"position": [[1964, 8]]}}}], ["migrat", {"_index": 240, "title": {}, "content": {"index": {"position": [[1008, 11]]}}}], ["mit", {"_index": 400, "title": {}, "content": {"index": {"position": [[4474, 3]]}}}], ["modern", {"_index": 196, "title": {}, "content": {"index": {"position": [[242, 6]]}}}], ["modifi", {"_index": 365, "title": {}, "content": {"index": {"position": [[3812, 6]]}}}], ["more", {"_index": 79, "title": {}, "content": {"documentation/delployment": {"position": [[1467, 4]]}}}], ["more.seo", {"_index": 280, "title": {}, "content": {"index": {"position": [[1874, 8]]}}}], ["multipl", {"_index": 83, "title": {}, "content": {"documentation/delployment": {"position": [[1533, 8]]}, "index": {"position": [[2252, 8]]}}}], ["necessari", {"_index": 27, "title": {}, "content": {"documentation/delployment": {"position": [[314, 9]]}}}], ["need", {"_index": 61, "title": {}, "content": {"documentation/delployment": {"position": [[992, 4], [1180, 4]]}, "documentation/search": {"position": [[170, 4], [1261, 4], [2069, 5]]}}}], ["needs.deploymentw", {"_index": 371, "title": {}, "content": {"index": {"position": [[3909, 18]]}}}], ["new", {"_index": 367, "title": {}, "content": {"index": {"position": [[3834, 3]]}}}], ["next.j", {"_index": 1, "title": {"documentation/delployment": {"position": [[12, 7]]}}, "content": {"documentation/delployment": {"position": [[74, 7], [543, 7], [814, 7], [1339, 7], [1635, 7]]}, "documentation/search": {"position": [[367, 8], [552, 7], [1824, 7]]}, "index": {"position": [[11, 7], [86, 7], [337, 7], [1728, 7], [3462, 7]]}}}], ["next.js'", {"_index": 305, "title": {}, "content": {"index": {"position": [[2287, 9]]}}}], ["nextauth.j", {"_index": 227, "title": {}, "content": {"index": {"position": [[749, 11]]}}}], ["offerings.dock", {"_index": 322, "title": {}, "content": {"index": {"position": [[2745, 16]]}}}], ["on", {"_index": 316, "title": {}, "content": {"index": {"position": [[2588, 3]]}}}], ["open", {"_index": 388, "title": {}, "content": {"index": {"position": [[4292, 4]]}}}], ["oper", {"_index": 239, "title": {}, "content": {"index": {"position": [[955, 11]]}}}], ["optim", {"_index": 204, "title": {}, "content": {"index": {"position": [[349, 7], [1883, 13], [2062, 13], [2114, 13], [2137, 12]]}}}], ["optional)if", {"_index": 58, "title": {}, "content": {"documentation/delployment": {"position": [[910, 12]]}}}], ["organ", {"_index": 345, "title": {}, "content": {"index": {"position": [[3341, 9]]}}}], ["organization.typescript", {"_index": 215, "title": {}, "content": {"index": {"position": [[505, 24]]}}}], ["orm", {"_index": 237, "title": {}, "content": {"index": {"position": [[928, 3]]}}}], ["out", {"_index": 256, "title": {}, "content": {"index": {"position": [[1253, 3], [4049, 3]]}}}], ["overal", {"_index": 178, "title": {}, "content": {"documentation/search": {"position": [[2091, 7]]}}}], ["overview", {"_index": 123, "title": {}, "content": {"documentation/search": {"position": [[438, 8]]}}}], ["package.json", {"_index": 24, "title": {}, "content": {"documentation/delployment": {"position": [[256, 12], [288, 12]]}}}], ["package.json.step", {"_index": 14, "title": {}, "content": {"documentation/delployment": {"position": [[136, 17]]}}}], ["page", {"_index": 149, "title": {}, "content": {"documentation/search": {"position": [[1042, 6]]}, "index": {"position": [[1982, 5], [3481, 5]]}}}], ["pages.search", {"_index": 129, "title": {}, "content": {"documentation/search": {"position": [[609, 12], [1205, 12]]}}}], ["pages.strip", {"_index": 312, "title": {}, "content": {"index": {"position": [[2475, 12]]}}}], ["part", {"_index": 362, "title": {}, "content": {"index": {"position": [[3705, 5]]}}}], ["particip", {"_index": 392, "title": {}, "content": {"index": {"position": [[4330, 11]]}}}], ["pass", {"_index": 75, "title": {}, "content": {"documentation/delployment": {"position": [[1395, 4]]}}}], ["path", {"_index": 156, "title": {}, "content": {"documentation/search": {"position": [[1177, 5]]}}}], ["payment", {"_index": 313, "title": {}, "content": {"index": {"position": [[2518, 7]]}}}], ["perfect", {"_index": 276, "title": {}, "content": {"index": {"position": [[1813, 7]]}}}], ["perform", {"_index": 205, "title": {}, "content": {"index": {"position": [[357, 11]]}}}], ["pipelines.get", {"_index": 329, "title": {}, "content": {"index": {"position": [[3021, 17]]}}}], ["platform", {"_index": 372, "title": {}, "content": {"index": {"position": [[3991, 10]]}}}], ["pleas", {"_index": 332, "title": {}, "content": {"index": {"position": [[3079, 6]]}}}], ["plugin", {"_index": 119, "title": {}, "content": {"documentation/search": {"position": [[395, 7], [930, 7]]}}}], ["port", {"_index": 48, "title": {}, "content": {"documentation/delployment": {"position": [[717, 4], [751, 4]]}}}], ["post", {"_index": 99, "title": {}, "content": {"documentation/search": {"position": [[13, 5]]}, "index": {"position": [[1597, 6]]}}}], ["power", {"_index": 115, "title": {}, "content": {"documentation/search": {"position": [[339, 7]]}, "index": {"position": [[117, 8], [1529, 8], [2637, 5]]}}}], ["practic", {"_index": 283, "title": {}, "content": {"index": {"position": [[1928, 9], [2947, 9]]}}}], ["practices.key", {"_index": 199, "title": {}, "content": {"index": {"position": [[271, 13]]}}}], ["prerequisitesdock", {"_index": 5, "title": {}, "content": {"documentation/delployment": {"position": [[0, 19]]}}}], ["presence.perform", {"_index": 290, "title": {}, "content": {"index": {"position": [[2041, 20]]}}}], ["prisma", {"_index": 228, "title": {}, "content": {"index": {"position": [[765, 6], [921, 6]]}}}], ["process", {"_index": 143, "title": {}, "content": {"documentation/search": {"position": [[896, 9], [1886, 11]]}, "index": {"position": [[2526, 10]]}}}], ["process:cont", {"_index": 124, "title": {}, "content": {"documentation/search": {"position": [[454, 15]]}}}], ["product", {"_index": 92, "title": {}, "content": {"documentation/installation": {"position": [[34, 8]]}, "index": {"position": [[564, 13], [2877, 10]]}}}], ["project", {"_index": 3, "title": {"documentation/delployment": {"position": [[24, 7]]}, "documentation/search": {"position": [[64, 7]]}}, "content": {"documentation/delployment": {"position": [[86, 7], [551, 7], [1647, 7]]}, "documentation/search": {"position": [[43, 7]]}, "index": {"position": [[186, 7], [4444, 7]]}}}], ["project'", {"_index": 384, "title": {}, "content": {"index": {"position": [[4243, 9]]}}}], ["provid", {"_index": 135, "title": {}, "content": {"documentation/search": {"position": [[718, 8]]}, "index": {"position": [[207, 9], [3928, 7]]}}}], ["providers.databas", {"_index": 235, "title": {}, "content": {"index": {"position": [[882, 18]]}}}], ["providers.styl", {"_index": 243, "title": {}, "content": {"index": {"position": [[1073, 18]]}}}], ["pull", {"_index": 68, "title": {}, "content": {"documentation/delployment": {"position": [[1188, 4]]}, "index": {"position": [[4312, 4]]}}}], ["purchases.aw", {"_index": 317, "title": {}, "content": {"index": {"position": [[2597, 13]]}}}], ["push", {"_index": 56, "title": {}, "content": {"documentation/delployment": {"position": [[867, 4], [1000, 4]]}}}], ["qualiti", {"_index": 221, "title": {}, "content": {"index": {"position": [[621, 8]]}}}], ["queri", {"_index": 136, "title": {}, "content": {"documentation/search": {"position": [[739, 5], [1672, 7]]}, "index": {"position": [[999, 8]]}}}], ["quickli", {"_index": 103, "title": {}, "content": {"documentation/search": {"position": [[134, 7], [2035, 7]]}}}], ["radix", {"_index": 260, "title": {}, "content": {"index": {"position": [[1326, 5]]}}}], ["rank", {"_index": 287, "title": {}, "content": {"index": {"position": [[2015, 8]]}}}], ["<PERSON><PERSON>", {"_index": 249, "title": {}, "content": {"index": {"position": [[1161, 7]]}}}], ["react", {"_index": 117, "title": {}, "content": {"documentation/search": {"position": [[376, 6], [1590, 5], [1737, 5]]}, "index": {"position": [[1651, 5], [2323, 5], [2383, 5], [3520, 5]]}}}], ["readi", {"_index": 326, "title": {}, "content": {"index": {"position": [[2902, 6]]}}}], ["refactor", {"_index": 222, "title": {}, "content": {"index": {"position": [[637, 12]]}}}], ["refer", {"_index": 333, "title": {}, "content": {"index": {"position": [[3086, 5]]}}}], ["registr", {"_index": 232, "title": {}, "content": {"index": {"position": [[805, 13]]}}}], ["registri", {"_index": 57, "title": {}, "content": {"documentation/delployment": {"position": [[901, 8], [1035, 8], [1226, 8]]}}}], ["rehyp", {"_index": 145, "title": {}, "content": {"documentation/search": {"position": [[923, 6], [1866, 6]]}}}], ["relev", {"_index": 12, "title": {}, "content": {"documentation/delployment": {"position": [[116, 8]]}}}], ["remark", {"_index": 144, "title": {}, "content": {"documentation/search": {"position": [[912, 6], [1855, 6]]}}}], ["remot", {"_index": 60, "title": {}, "content": {"documentation/delployment": {"position": [[969, 6], [1144, 6], [1163, 6]]}}}], ["render", {"_index": 209, "title": {}, "content": {"index": {"position": [[420, 10]]}}}], ["repositori", {"_index": 340, "title": {}, "content": {"index": {"position": [[3228, 11], [4278, 10]]}}}], ["request", {"_index": 391, "title": {}, "content": {"index": {"position": [[4317, 9]]}}}], ["respons", {"_index": 254, "title": {}, "content": {"index": {"position": [[1235, 10]]}}}], ["result", {"_index": 172, "title": {}, "content": {"documentation/search": {"position": [[1693, 8]]}}}], ["reusabl", {"_index": 354, "title": {}, "content": {"index": {"position": [[3511, 8]]}}}], ["review", {"_index": 16, "title": {}, "content": {"documentation/delployment": {"position": [[157, 6], [249, 6]]}}}], ["rich", {"_index": 266, "title": {}, "content": {"index": {"position": [[1493, 4]]}}}], ["robust", {"_index": 194, "title": {}, "content": {"index": {"position": [[219, 6]]}}}], ["root", {"_index": 39, "title": {}, "content": {"documentation/delployment": {"position": [[520, 4]]}}}], ["rout", {"_index": 213, "title": {}, "content": {"index": {"position": [[488, 7], [1679, 7], [1740, 7], [2311, 7]]}}}], ["router", {"_index": 212, "title": {}, "content": {"index": {"position": [[467, 6], [3474, 6]]}}}], ["run", {"_index": 43, "title": {}, "content": {"documentation/delployment": {"position": [[599, 3], [664, 3], [1239, 3], [1713, 4]]}}}], ["s3", {"_index": 320, "title": {}, "content": {"index": {"position": [[2719, 3]]}}}], ["saa", {"_index": 186, "title": {}, "content": {"index": {"position": [[19, 4], [94, 4], [181, 4], [3904, 4], [3963, 4]]}}}], ["safe", {"_index": 218, "title": {}, "content": {"index": {"position": [[541, 4], [985, 4]]}}}], ["schema", {"_index": 242, "title": {}, "content": {"index": {"position": [[1029, 6]]}}}], ["scores.internation", {"_index": 301, "title": {}, "content": {"index": {"position": [[2207, 28]]}}}], ["script", {"_index": 13, "title": {}, "content": {"documentation/delployment": {"position": [[125, 7], [324, 7]]}}}], ["scriptsensur", {"_index": 25, "title": {}, "content": {"documentation/delployment": {"position": [[269, 13]]}}}], ["sdk", {"_index": 319, "title": {}, "content": {"index": {"position": [[2681, 4]]}}}], ["seamlessli", {"_index": 225, "title": {}, "content": {"index": {"position": [[703, 10]]}}}], ["search", {"_index": 94, "title": {"documentation/search": {"position": [[13, 6]]}}, "content": {"documentation/search": {"position": [[62, 6], [178, 9], [294, 6], [698, 6], [1236, 6], [1338, 6], [1563, 6], [1627, 6], [1777, 6], [1904, 6], [1971, 6]]}, "index": {"position": [[2001, 6]]}}}], ["search.search", {"_index": 133, "title": {}, "content": {"documentation/search": {"position": [[671, 13]]}}}], ["section", {"_index": 97, "title": {"documentation/search": {"position": [[49, 7]]}}, "content": {"documentation/search": {"position": [[96, 8]]}, "index": {"position": [[3158, 8]]}}}], ["secur", {"_index": 230, "title": {}, "content": {"index": {"position": [[791, 6], [2511, 6]]}}}], ["see", {"_index": 395, "title": {}, "content": {"index": {"position": [[4374, 3], [4487, 3]]}}}], ["seo", {"_index": 282, "title": {}, "content": {"index": {"position": [[1919, 3]]}}}], ["separ", {"_index": 275, "title": {}, "content": {"index": {"position": [[1796, 8]]}}}], ["server", {"_index": 207, "title": {}, "content": {"index": {"position": [[408, 6], [1805, 7]]}}}], ["serverless", {"_index": 272, "title": {}, "content": {"index": {"position": [[1697, 10]]}}}], ["servers.ci/cd", {"_index": 325, "title": {}, "content": {"index": {"position": [[2888, 13]]}}}], ["servic", {"_index": 158, "title": {}, "content": {"documentation/search": {"position": [[1354, 7], [1920, 7]]}, "index": {"position": [[2650, 8], [3874, 8]]}}}], ["service.conclusionbi", {"_index": 175, "title": {}, "content": {"documentation/search": {"position": [[1793, 20]]}}}], ["services.conclusionbi", {"_index": 84, "title": {}, "content": {"documentation/delployment": {"position": [[1557, 21]]}}}], ["session", {"_index": 233, "title": {}, "content": {"index": {"position": [[823, 7]]}}}], ["set", {"_index": 19, "title": {}, "content": {"documentation/delployment": {"position": [[214, 3]]}, "index": {"position": [[1375, 3], [2961, 7]]}}}], ["setup", {"_index": 334, "title": {}, "content": {"index": {"position": [[3116, 5]]}}}], ["shadcn", {"_index": 261, "title": {}, "content": {"index": {"position": [[1339, 6]]}}}], ["share", {"_index": 356, "title": {}, "content": {"index": {"position": [[3564, 6], [4197, 5]]}}}], ["side", {"_index": 208, "title": {}, "content": {"index": {"position": [[415, 4]]}}}], ["site", {"_index": 210, "title": {}, "content": {"index": {"position": [[438, 4]]}}}], ["slug", {"_index": 154, "title": {}, "content": {"documentation/search": {"position": [[1112, 5]]}}}], ["social", {"_index": 288, "title": {}, "content": {"index": {"position": [[2028, 6]]}}}], ["solut", {"_index": 161, "title": {}, "content": {"documentation/search": {"position": [[1387, 8]]}}}], ["sourc", {"_index": 351, "title": {}, "content": {"index": {"position": [[3426, 6]]}}}], ["specif", {"_index": 370, "title": {}, "content": {"index": {"position": [[3895, 8]]}}}], ["split", {"_index": 292, "title": {}, "content": {"index": {"position": [[2097, 10]]}}}], ["src/content/doc", {"_index": 142, "title": {}, "content": {"documentation/search": {"position": [[852, 16]]}}}], ["start", {"_index": 29, "title": {}, "content": {"documentation/delployment": {"position": [[349, 8]]}, "index": {"position": [[3150, 7]]}}}], ["startedto", {"_index": 330, "title": {}, "content": {"index": {"position": [[3039, 9]]}}}], ["static", {"_index": 128, "title": {}, "content": {"documentation/search": {"position": [[571, 10], [1004, 10], [1170, 6], [1836, 6]]}, "index": {"position": [[431, 6]]}}}], ["step", {"_index": 85, "title": {}, "content": {"documentation/delployment": {"position": [[1595, 6]]}, "documentation/installation": {"position": [[13, 5]]}, "index": {"position": [[3186, 4], [3194, 4]]}}}], ["store", {"_index": 139, "title": {}, "content": {"documentation/search": {"position": [[812, 6]]}}}], ["stripe", {"_index": 314, "title": {}, "content": {"index": {"position": [[2542, 7]]}}}], ["structur", {"_index": 346, "title": {}, "content": {"index": {"position": [[3351, 9]]}}}], ["structureour", {"_index": 343, "title": {}, "content": {"index": {"position": [[3301, 12]]}}}], ["style", {"_index": 359, "title": {}, "content": {"index": {"position": [[3592, 6]]}}}], ["submiss", {"_index": 279, "title": {}, "content": {"index": {"position": [[1839, 12]]}}}], ["submit", {"_index": 390, "title": {}, "content": {"index": {"position": [[4305, 6]]}}}], ["subscript", {"_index": 315, "title": {}, "content": {"index": {"position": [[2560, 12]]}}}], ["successfulli", {"_index": 86, "title": {}, "content": {"documentation/delployment": {"position": [[1610, 12]]}}}], ["suit", {"_index": 309, "title": {}, "content": {"index": {"position": [[2359, 6]]}}}], ["support", {"_index": 234, "title": {}, "content": {"index": {"position": [[847, 7], [1515, 7], [2240, 7], [2762, 8]]}}}], ["support.api", {"_index": 271, "title": {}, "content": {"index": {"position": [[1667, 11]]}}}], ["sure", {"_index": 38, "title": {}, "content": {"documentation/delployment": {"position": [[500, 4]]}}}], ["system.blog", {"_index": 264, "title": {}, "content": {"index": {"position": [[1455, 11]]}}}], ["tailwind", {"_index": 246, "title": {}, "content": {"index": {"position": [[1124, 8], [3603, 8]]}}}], ["target", {"_index": 9, "title": {}, "content": {"documentation/delployment": {"position": [[60, 6]]}}}], ["team", {"_index": 324, "title": {}, "content": {"index": {"position": [[2868, 4]]}}}], ["technolog", {"_index": 197, "title": {}, "content": {"index": {"position": [[249, 12]]}}}], ["termin", {"_index": 35, "title": {}, "content": {"documentation/delployment": {"position": [[459, 8]]}}}], ["test", {"_index": 180, "title": {"documentation/testing": {"position": [[0, 7]]}}, "content": {"index": {"position": [[2389, 7], [2444, 7]]}}}], ["text", {"_index": 183, "title": {}, "content": {"documentation/testing": {"position": [[11, 4], [21, 4]]}, "index": {"position": [[1538, 4]]}}}], ["theme", {"_index": 366, "title": {}, "content": {"index": {"position": [[3823, 6]]}}}], ["this.overviewth", {"_index": 114, "title": {}, "content": {"documentation/search": {"position": [[277, 16]]}}}], ["through", {"_index": 106, "title": {}, "content": {"documentation/search": {"position": [[188, 7]]}}}], ["time", {"_index": 297, "title": {}, "content": {"index": {"position": [[2172, 5], [2592, 4]]}}}], ["tipsenviron", {"_index": 72, "title": {}, "content": {"documentation/delployment": {"position": [[1304, 15]]}}}], ["translations.test", {"_index": 308, "title": {}, "content": {"index": {"position": [[2338, 20]]}}}], ["type", {"_index": 217, "title": {}, "content": {"index": {"position": [[536, 4], [980, 4], [1622, 5], [3648, 4]]}}}], ["typescript", {"_index": 219, "title": {}, "content": {"index": {"position": [[578, 10], [3637, 10]]}}}], ["typic", {"_index": 173, "title": {}, "content": {"documentation/search": {"position": [[1710, 9]]}}}], ["ui", {"_index": 259, "title": {}, "content": {"index": {"position": [[1317, 3], [1332, 2], [1346, 3]]}}}], ["under", {"_index": 399, "title": {}, "content": {"index": {"position": [[4464, 5]]}}}], ["unit", {"_index": 311, "title": {}, "content": {"index": {"position": [[2423, 4]]}}}], ["unstyl", {"_index": 262, "title": {}, "content": {"index": {"position": [[1382, 9]]}}}], ["up", {"_index": 20, "title": {}, "content": {"documentation/delployment": {"position": [[218, 3]]}, "index": {"position": [[2969, 2]]}}}], ["url", {"_index": 153, "title": {}, "content": {"documentation/search": {"position": [[1108, 3]]}}}], ["us", {"_index": 45, "title": {}, "content": {"documentation/delployment": {"position": [[671, 5], [1359, 4], [1429, 5], [1502, 5], [1655, 5]]}, "documentation/search": {"position": [[261, 4], [563, 4], [906, 5], [996, 4], [1330, 5], [1396, 5], [1463, 3]]}, "index": {"position": [[1722, 5], [2281, 5], [3055, 5], [4163, 5]]}}}], ["user", {"_index": 102, "title": {}, "content": {"documentation/search": {"position": [[125, 5], [730, 5], [1651, 5], [2026, 5]]}, "index": {"position": [[724, 4]]}}}], ["util", {"_index": 236, "title": {}, "content": {"index": {"position": [[913, 7], [1137, 7], [2366, 7], [3542, 7]]}}}], ["variabl", {"_index": 73, "title": {}, "content": {"documentation/delployment": {"position": [[1320, 10], [1376, 10]]}}}], ["variou", {"_index": 118, "title": {}, "content": {"documentation/search": {"position": [[387, 7]]}, "index": {"position": [[859, 7], [3697, 7], [3983, 7]]}}}], ["vercel", {"_index": 373, "title": {}, "content": {"index": {"position": [[4012, 7]]}}}], ["vibrant", {"_index": 380, "title": {}, "content": {"index": {"position": [[4131, 7]]}}}], ["view", {"_index": 171, "title": {}, "content": {"documentation/search": {"position": [[1684, 4]]}}}], ["visit", {"_index": 386, "title": {}, "content": {"index": {"position": [[4261, 5]]}}}], ["vital", {"_index": 300, "title": {}, "content": {"index": {"position": [[2200, 6]]}}}], ["want", {"_index": 59, "title": {}, "content": {"documentation/delployment": {"position": [[927, 4]]}}}], ["web", {"_index": 299, "title": {}, "content": {"index": {"position": [[2196, 3]]}}}], ["welcom", {"_index": 185, "title": {"index": {"position": [[0, 7]]}}, "content": {"index": {"position": [[0, 7]]}}}], ["well", {"_index": 344, "title": {}, "content": {"index": {"position": [[3336, 4]]}}}], ["without", {"_index": 250, "title": {}, "content": {"index": {"position": [[1190, 7], [1777, 7]]}}}], ["you'll", {"_index": 337, "title": {}, "content": {"index": {"position": [[3174, 6], [4383, 6]]}}}]], "pipeline": ["stemmer"]}, "docs": {"documentation/delployment": {"title": "Deploying a Next.js 13+ Project with <PERSON><PERSON>", "url": "/docs/documentation/delployment", "content": "PrerequisitesDocker installed on your local machine and the target host.A Next.js 13+ project with a Dockerfile and relevant scripts in package.json.Step 1: Review the DockerfileEnsure your Dockerfile is correctly set up. Here is an example:Step 2: Review package.json ScriptsEnsure your package.json includes the necessary scripts for building and starting your application. Here is an example:Step 3: Build the Docker ImageRun the following command in your terminal to build the Docker image. Make sure you are in the root directory of your Next.js project where the Dockerfile is located.Step 4: Run the Docker ContainerAfter building the Docker image, you can run it using the following command:This command maps port 3000 of your host machine to port 3000 of the Docker container, allowing you to access your Next.js application at http://localhost:3000.Step 5: Push the Docker Image to a Docker Registry (Optional)If you want to deploy your Docker container to a remote host, you might need to push your Docker image to a Docker registry like Docker Hub.Login to Docker Hub:Tag your Docker image:Push the Docker image:Step 6: Deploy to a Remote HostOn your remote host, you need to pull the Docker image from the Docker registry and run it.Pull the Docker image:Run the Docker container:Additional TipsEnvironment Variables: If your Next.js application uses environment variables, you can pass them to the Docker container using the -e flag.Docker Compose: For more complex deployments, consider using Docker Compose to manage multiple containers and services.ConclusionBy following these steps, you can successfully deploy your Next.js 13+ project using Docker. This approach ensures that your application runs consistently across different environments, making it easier to manage and deploy.\n", "excerpt": "PrerequisitesDocker installed on your local machine and the target host.A Next.js 13+ project with a Dockerfile and relevant scripts in package.json.S..."}, "documentation/installation": {"title": "Installation", "url": "/docs/documentation/installation", "content": "Follow these steps to install our product.\n", "excerpt": "Follow these steps to install our product.\n..."}, "documentation/search": {"title": "Implementing Search Functionality in the `/docs` Section of Our Project", "url": "/docs/documentation/search", "content": "In this blog post, we will explore how our project implements search functionality in the /docs section. This feature allows users to quickly find the documentation they need by searching through the content. We will break down the key components and libraries used to achieve this.OverviewThe search functionality in our documentation is powered by a combination of Next.js, React, and various plugins and libraries. Here’s a high-level overview of the process:Content Management: Documentation content is managed in Markdown files.Static Generation: Next.js is used to statically generate the documentation pages.Search Indexing: Content is indexed to enable efficient search.Search Interface: A search interface is provided to users to query the indexed content.Content ManagementOur documentation content is stored in Markdown files located in the src/content/docs directory. These files are processed using remark and rehype plugins to convert Markdown into HTML.Static GenerationNext.js is used to statically generate the documentation pages. The getDocBySlug function fetches the content based on the URL slug, and the generateStaticParams function generates the static paths for all documentation pages.Search IndexingTo enable search functionality, we need to index the content of our documentation. This can be achieved using a search indexing service like Algolia or a custom solution using libraries like lunr.js.Example with AlgoliaIndexing Content: Use Algolia’s API to index the content of your Markdown files.Search Interface: Integrate Algolia’s search interface into your React components.Search InterfaceThe search interface allows users to input their queries and view the results. This is typically implemented as a React component that interacts with the search indexing service.ConclusionBy combining Next.js for static generation, remark and rehype for Markdown processing, and a search indexing service like Algolia, we can efficiently implement search functionality in our documentation. This allows users to quickly find the information they need, enhancing their overall experience.\n", "excerpt": "In this blog post, we will explore how our project implements search functionality in the /docs section. This feature allows users to quickly find the..."}, "documentation/testing": {"title": "Testing", "url": "/docs/documentation/testing", "content": "Home\n\nLink text\n\nAlt text\n", "excerpt": "Home\n\nLink text\n\nAlt text\n..."}, "index": {"title": "Welcome", "url": "/docs/index", "content": "Welcome to Next.js SaaS BoilerplateWelcome to the comprehensive documentation for our Next.js SaaS Boilerplate. This powerful and flexible boilerplate is designed to jumpstart your SaaS project development, providing a robust foundation with modern technologies and best practices.Key FeaturesNext.js 14: Leverage the latest features of Next.js for optimal performance and developer experience. Benefit from server-side rendering, static site generation, and the App Router for efficient routing and code organization.TypeScript: Enjoy type-safe code and enhanced productivity. TypeScript integration ensures better code quality, easier refactoring, and improved developer collaboration.Authentication: Seamlessly integrate user authentication with NextAuth.js and Prisma adapter. Implement secure login, registration, and session management with support for various authentication providers.Database Management: Utilize Prisma ORM for efficient database operations. Benefit from type-safe database queries, migrations, and easy schema management across different database providers.Styling: Craft beautiful interfaces with Tailwind CSS utility-first approach. Rapidly build custom designs without leaving your HTML, and enjoy a fully responsive layout out of the box.UI Components: Build accessible and customizable UIs with Radix UI and Shadcn UI. Leverage a comprehensive set of unstyled, accessible components that you can easily adapt to your design system.Blog and Documentation: Create rich content with MDX support and a powerful text editor. Easily manage and display documentation, blog posts, and other content types with full Markdown and React component support.API Routes: Implement serverless API endpoints using Next.js API routes. Create backend functionality without managing a separate server, perfect for handling form submissions, API integrations, and more.SEO Optimization: Benefit from built-in SEO best practices and easy customization of metadata for each page. Improve your search engine rankings and social media presence.Performance Optimization: Enjoy automatic code splitting, image optimization, and font optimization for blazing-fast load times and improved Core Web Vitals scores.Internationalization: Add support for multiple languages with ease using Next.js's built-in i18n routing and React-Intl for translations.Testing Suite: Utilize Jest and React Testing Library for comprehensive unit and integration testing of your components and pages.Stripe Integration: Implement secure payment processing with Stripe, including subscription management and one-time purchases.AWS Integration: Leverage the power of AWS services with the included AWS SDK, making it easy to integrate with S3, Lambda, and other AWS offerings.Docker Support: Containerize your application for consistent development and deployment environments across your team and production servers.CI/CD Ready: Includes configuration files and best practices for setting up Continuous Integration and Continuous Deployment pipelines.Getting StartedTo begin using this boilerplate, please refer to the installation and setup instructions in the Getting Started section. There, you'll find step-by-step guidance on how to clone the repository, install dependencies, and configure your environment.Project StructureOur boilerplate follows a well-organized structure to keep your code clean and maintainable:/src: Contains the main source code of the application/app: Next.js App Router pages and layouts/components: Reusable React components/lib: Utility functions and shared logic/styles: Global styles and Tailwind CSS configuration/types: TypeScript type definitions/config: Configuration files for various parts of the applicationCustomizationThe boilerplate is designed to be highly customizable. You can easily modify the theme, add new components, or integrate additional services to fit your specific SaaS needs.DeploymentWe provide guidance on deploying your SaaS application to various platforms, including Vercel, AWS, and DigitalOcean. Check out our deployment guides for detailed instructions.Support and CommunityJoin our vibrant community of developers using this boilerplate. Get help, share your experiences, and contribute to the project's growth. Visit our GitHub repository to open issues, submit pull requests, or participate in discussions.We're excited to see what you'll build with this boilerplate. Happy coding!LicenseThis project is licensed under the MIT License. See the LICENSE file for details.\n", "excerpt": "Welcome to Next.js SaaS BoilerplateWelcome to the comprehensive documentation for our Next.js SaaS Boilerplate. This powerful and flexible boilerplate..."}}}