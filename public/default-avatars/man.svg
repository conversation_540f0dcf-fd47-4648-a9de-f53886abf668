<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_364)">
<rect width="1080" height="1080" fill="white"/>
<mask id="path-1-inside-1_0_364" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M310.946 562.941C335.92 464.119 425.436 391 532 391C657.921 391 760 493.079 760 619C760 685.696 757.974 753.131 735.716 803.886C724.751 828.889 709.04 849.399 686.474 863.767C663.865 878.163 633.421 887 592 887C467.998 887 367.984 807.524 325.672 714.008C323.67 709.581 319.196 706.799 314.34 706.961C313.564 706.987 312.783 707 312 707C274.445 707 244 676.555 244 639C244 605.126 268.78 577.017 301.2 571.851C305.938 571.096 309.771 567.592 310.946 562.941ZM532 367C416.833 367 319.736 444.245 289.647 549.737C249.637 559.728 220 595.893 220 639C220 688.156 258.552 728.308 307.07 730.87C354.507 828.845 460.538 911 592 911C636.853 911 672.047 901.406 699.364 884.012C726.724 866.591 745.228 841.954 757.695 813.525C782.3 757.418 784 684.852 784 619C784 479.824 671.176 367 532 367ZM307.45 621.439C305.735 615.038 299.155 611.239 292.753 612.954C286.351 614.669 282.552 621.249 284.268 627.651L292.55 658.561C294.265 664.962 300.845 668.761 307.247 667.046C313.649 665.331 317.448 658.751 315.732 652.349L307.45 621.439Z"/>
</mask>
<path d="M310.946 562.941L287.678 557.06L310.946 562.941ZM735.716 803.886L713.736 794.248L735.716 803.886ZM686.474 863.767L699.364 884.012L686.474 863.767ZM325.672 714.008L347.538 704.114L347.538 704.114L325.672 714.008ZM314.34 706.961L315.138 730.948H315.139L314.34 706.961ZM301.2 571.851L304.977 595.552L301.2 571.851ZM289.647 549.737L295.461 573.022C303.789 570.943 310.372 564.574 312.727 556.32L289.647 549.737ZM307.07 730.87L328.671 720.411C324.86 712.54 317.069 707.365 308.335 706.904L307.07 730.87ZM699.364 884.012L686.474 863.767H686.474L699.364 884.012ZM757.695 813.525L735.716 803.886V803.886L757.695 813.525ZM292.753 612.954L298.965 636.136H298.965L292.753 612.954ZM307.45 621.439L330.632 615.228V615.228L307.45 621.439ZM284.268 627.651L261.086 633.863L284.268 627.651ZM292.55 658.561L269.368 664.772H269.368L292.55 658.561ZM307.247 667.046L313.459 690.228H313.459L307.247 667.046ZM315.732 652.349L338.914 646.137L338.914 646.137L315.732 652.349ZM532 367C414.187 367 315.28 447.837 287.678 557.06L334.215 568.821C356.559 480.402 436.684 415 532 415V367ZM784 619C784 479.824 671.176 367 532 367V415C644.666 415 736 506.334 736 619H784ZM757.695 813.525C782.302 757.413 784 684.838 784 619H736C736 686.554 733.645 748.849 713.736 794.248L757.695 813.525ZM699.364 884.012C726.722 866.592 745.227 841.956 757.695 813.525L713.736 794.248C704.276 815.821 691.357 832.206 673.584 843.523L699.364 884.012ZM592 911C636.855 911 672.048 901.405 699.364 884.012L673.584 843.523C655.681 854.921 629.987 863 592 863V911ZM303.806 723.901C349.514 824.924 457.439 911 592 911V863C478.557 863 386.454 790.124 347.538 704.114L303.806 723.901ZM315.139 730.948C310.283 731.109 305.809 728.327 303.806 723.901L347.538 704.114C341.53 690.836 328.109 682.49 313.542 682.974L315.139 730.948ZM312 731C313.052 731 314.099 730.982 315.138 730.948L313.542 682.974C313.029 682.991 312.514 683 312 683V731ZM220 639C220 689.81 261.19 731 312 731V683C287.699 683 268 663.301 268 639H220ZM297.424 548.15C253.53 555.145 220 593.151 220 639H268C268 617.101 284.03 598.89 304.977 595.552L297.424 548.15ZM287.678 557.06C288.853 552.409 292.686 548.905 297.424 548.15L304.977 595.552C319.19 593.287 330.689 582.775 334.215 568.821L287.678 557.06ZM312.727 556.32C339.954 460.861 427.837 391 532 391V343C405.829 343 299.518 427.63 266.567 543.154L312.727 556.32ZM244 639C244 607.172 265.88 580.409 295.461 573.022L283.833 526.452C233.393 539.047 196 584.614 196 639H244ZM308.335 706.904C272.495 705.011 244 675.325 244 639H196C196 700.987 244.609 751.605 305.804 754.837L308.335 706.904ZM592 887C470.879 887 372.621 811.183 328.671 720.411L285.469 741.329C336.393 846.506 450.197 935 592 935V887ZM686.474 863.767C663.866 878.162 633.423 887 592 887V935C640.284 935 680.227 924.649 712.254 904.256L686.474 863.767ZM735.716 803.886C724.751 828.891 709.038 849.4 686.474 863.767L712.254 904.256C744.41 883.782 765.706 855.017 779.675 823.164L735.716 803.886ZM760 619C760 685.682 757.976 753.127 735.716 803.886L779.675 823.164C806.625 761.708 808 684.022 808 619H760ZM532 391C657.921 391 760 493.079 760 619H808C808 466.569 684.431 343 532 343V391ZM298.965 636.136C292.563 637.852 285.983 634.053 284.268 627.651L330.632 615.228C325.486 596.023 305.746 584.626 286.541 589.772L298.965 636.136ZM307.45 621.439C309.165 627.841 305.366 634.421 298.965 636.136L286.541 589.772C267.337 594.918 255.94 614.658 261.086 633.863L307.45 621.439ZM315.732 652.349L307.45 621.439L261.086 633.863L269.368 664.772L315.732 652.349ZM301.035 643.864C307.437 642.148 314.017 645.947 315.732 652.349L269.368 664.772C274.514 683.977 294.254 695.374 313.459 690.228L301.035 643.864ZM292.55 658.561C290.835 652.159 294.634 645.579 301.035 643.864L313.459 690.228C332.663 685.082 344.06 665.342 338.914 646.137L292.55 658.561ZM284.268 627.651L292.55 658.561L338.914 646.137L330.632 615.228L284.268 627.651Z" fill="black" mask="url(#path-1-inside-1_0_364)"/>
<path d="M637.66 596.759C637.66 596.759 655.468 625.898 659.307 640.226C663.147 654.554 660.954 683.693 660.954 683.693" stroke="black" stroke-width="16" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M521 506C521 506 540.333 490 566 490C591.667 490 611 506 611 506" stroke="black" stroke-width="20" stroke-linecap="square" stroke-linejoin="round"/>
<path d="M667 506C667 506 686.333 490 712 490C737.667 490 757 506 757 506" stroke="black" stroke-width="20" stroke-linecap="square" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M570 564C583.255 564 594 553.255 594 540C594 526.745 583.255 516 570 516C556.745 516 546 526.745 546 540C546 553.255 556.745 564 570 564ZM708 564C721.255 564 732 553.255 732 540C732 526.745 721.255 516 708 516C694.745 516 684 526.745 684 540C684 553.255 694.745 564 708 564Z" fill="black"/>
<path d="M549 759C549 759 589.996 769 619 769C648.004 769 669 759 669 759" stroke="black" stroke-width="16" stroke-linecap="round" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M611.547 230.463C698.094 236.925 725 287 724.589 333.555C748.639 343.003 766 370.026 766 401.887C766 428.369 713 469 713 469C713 469 631.472 473.034 581.383 464.202C514.014 452.323 462.039 412.237 441 412C419.961 411.763 402.482 416.589 384 454C365.518 491.411 385.823 520.524 384 551C384 551 372.886 607.857 366.493 651.64L366 655L328.117 564.825H287.146C265.13 517.303 245 413 269 379C293 345 310 351 346 360C354.615 315.384 393.524 291.763 430.725 270.311L433 269C466 250 525 224 611.547 230.463Z" fill="black" stroke="black" stroke-width="12" stroke-linecap="round" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M711 764H707L706.345 762.707C700.564 751.423 694.782 743.855 689 740C680 734 658 735 618 735L615.605 734.995C576.569 734.845 541.74 731.16 529 739C520.333 744.333 514.333 752.667 511 764H462L366 655H383C388.048 663.078 393.048 668.411 398 671C406.446 675.414 431.388 682.267 438 687C450.427 695.895 463.76 707.895 478 723H506L532 713C566.333 708.333 598.667 706 629 706C659.333 706 688 709.333 715 716L734 739H747C747 739 770 768 734 834C698 900 652.614 901.017 640 905C621 911 543.351 912.626 512 903L510.102 902.411C478.827 892.616 434.373 873.798 408 829C390.238 798.83 376.339 741.682 366.303 657.557L366 655L461 769H512C527.555 792.761 543.888 808.761 561 817C577.941 825.157 592.158 817.724 603.653 794.702L604 794H629C631.667 809.333 638.333 817 649 817C659.56 817 679.921 799.685 710.083 765.055L711 764Z" fill="black" stroke="black" stroke-width="12" stroke-linecap="round" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M281 720C281 720 292 745 303 738C314 731 300.686 703.464 300.686 697.308C300.686 691.153 308.938 687.771 312.938 691.143C316.938 694.514 320.749 704.393 323 717C328 745 315 757 300 758C276.009 759.599 263 712 263 712L281 720Z" fill="white" stroke="black" stroke-width="8" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_0_364">
<rect width="1080" height="1080" fill="white"/>
</clipPath>
</defs>
</svg>
