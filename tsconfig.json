{"compilerOptions": {"target": "ES5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "types": ["node", "@testing-library/jest-dom"], "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "types.d.ts", ".next/types/**/*.ts", "src/app/(marketing)/(landing)/(privacy)/**/*.mdx", "src/types/**/*.d.ts", "jest.config.ts", "src/lib/code-highlighter.ts", "tailwind.config.ts"], "exclude": ["node_modules"]}