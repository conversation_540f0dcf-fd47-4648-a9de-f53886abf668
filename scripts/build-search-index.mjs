import fs from "fs"
import path from "path"
import matter from "gray-matter"
import lunr from "lunr"
import { remark } from "remark"
import mdxToPlainText from "remark-mdx-to-plain-text"

function createExcerpt(content, positions) {
  if (!positions || positions.length === 0) {
    return content.substring(0, 150) + "..."
  }

  const start = Math.max(positions[0][0] - 30, 0)
  const end = Math.min(positions[0][0] + positions[0][1] + 120, content.length)
  return content.substring(start, end) + "..."
}
async function buildSearchIndex(docsPath = "src/content/docs") {
  const docsDirectory = path.join(process.cwd(), docsPath)
  const docs = {}

  const documents = []

  const traverseDirectory = async (currentPath, slug = []) => {
    const files = fs.readdirSync(currentPath)
    for (const file of files) {
      const filePath = path.join(currentPath, file)
      const stat = fs.statSync(filePath)
      const newSlug = [...slug, file.replace(".mdx", "")]

      if (stat.isDirectory()) {
        await traverseDirectory(filePath, newSlug)
      } else if (path.extname(file) === ".mdx") {
        const fileContents = fs.readFileSync(filePath, "utf8")
        const { data, content } = matter(fileContents)

        const url = `/docs/${newSlug.join("/")}`
        const slugString = newSlug.join("/")
        const title = data.title || file.replace(".mdx", "")

        // Convert MDX content to plain text
        const plainTextContent = await remark()
          .use(mdxToPlainText)
          .process(content)
        const plainText = String(plainTextContent)

        const excerpt = createExcerpt(plainText)

        documents.push({
          slug: slugString,
          title,
          url,
          content: plainText,
          excerpt,
        })

        docs[slugString] = { title, url, content: plainText, excerpt }
      }
    }
  }

  await traverseDirectory(docsDirectory)

  const idx = lunr(function () {
    this.ref("slug")
    this.field("title")
    this.field("content")
    this.metadataWhitelist = ["position"]

    documents.forEach((doc) => {
      this.add(doc)
    })
  })

  fs.writeFileSync(
    path.join(process.cwd(), "public", "search-index.json"),
    JSON.stringify({ index: idx.toJSON(), docs })
  )
}

buildSearchIndex() // Run the function
