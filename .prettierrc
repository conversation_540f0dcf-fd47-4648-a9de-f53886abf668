{"endOfLine": "lf", "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "plugins": ["@ianvs/prettier-plugin-sort-imports"], "overrides": [{"files": ["*.js", "*.jsx", "*.ts", "*.tsx"], "options": {"importOrder": ["^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "", "^types$", "^@/env(.*)$", "^@/types/(.*)$", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/styles/(.*)$", "^@/app/(.*)$", "", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderBuiltinModulesToTop": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderMergeDuplicateImports": true, "importOrderCombineTypeAndValueImports": true}}, {"files": "*.mdx", "options": {"parser": "mdx"}}]}