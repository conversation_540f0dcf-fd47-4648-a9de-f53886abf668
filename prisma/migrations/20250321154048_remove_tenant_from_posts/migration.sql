/*
  Warnings:

  - The values [GUEST] on the enum `Role` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `tenant_id` on the `posts` table. All the data in the column will be lost.
  - You are about to drop the column `tenant_id` on the `tags` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[slug]` on the table `tags` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "Role_new" AS ENUM ('USER', 'COLLABORATOR', 'ADMIN', 'OWNER');
ALTER TABLE "invites" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "tenants_users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "users" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TABLE "invites" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TABLE "tenants_users" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TYPE "Role" RENAME TO "Role_old";
ALTER TYPE "Role_new" RENAME TO "Role";
DROP TYPE "Role_old";
ALTER TABLE "invites" ALTER COLUMN "role" SET DEFAULT 'COLLABORATOR';
ALTER TABLE "tenants_users" ALTER COLUMN "role" SET DEFAULT 'USER';
ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'USER';
COMMIT;

-- DropForeignKey
ALTER TABLE "posts" DROP CONSTRAINT "posts_tenant_id_fkey";

-- DropForeignKey
ALTER TABLE "tags" DROP CONSTRAINT "tags_tenant_id_fkey";

-- DropIndex
DROP INDEX "tags_slug_tenant_id_key";

-- AlterTable
ALTER TABLE "posts" DROP COLUMN "tenant_id";

-- AlterTable
ALTER TABLE "tags" DROP COLUMN "tenant_id";

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'USER';

-- CreateIndex
CREATE UNIQUE INDEX "tags_slug_key" ON "tags"("slug");
