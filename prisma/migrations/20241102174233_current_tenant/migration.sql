/*
  Warnings:

  - A unique constraint covering the columns `[slug,tenant_id]` on the table `tags` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "tags_slug_key";

-- AlterTable
ALTER TABLE "sessions" ADD COLUMN     "current_tenant_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "tags_slug_tenant_id_key" ON "tags"("slug", "tenant_id");

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_current_tenant_id_fkey" FOREIGN KEY ("current_tenant_id") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;
