import * as falso from '@ngneat/falso';
import { PrismaClient, Role, Tier } from '@prisma/client';
import { add } from 'date-fns';
import { v4 as uuidv4 } from 'uuid';

import { stripe } from '../src/lib/stripe';

// Change this to test different user roles: 'OWNER' | 'ADMIN' | 'COLLABORATOR' | 'USER' | 'GUEST'
const TEST_USER_ROLE: Role = Role.ADMIN;

const SEED_CONFIG = {
  USERS_COUNT: 10,
  POSTS_COUNT: 50,
  TAGS_COUNT: 15,
  COMMENTS_COUNT: 200,
  LIKES_COUNT: 300,
  INVITES_COUNT: 20,
  SUBSCRIBERS_COUNT: 15,
  API_KEYS_COUNT: 15,
} as const;

const prisma = new PrismaClient();

// Function to slugify strings
function slugify(str: string) {
  if (typeof str !== 'string') {
    throw new Error('Input must be a string');
  }

  return str
    .normalize('NFKD')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
}

// Generate rich content for posts
function generatePostContent() {
  return {
    type: 'doc',
    content: [
      {
        type: 'heading',
        attrs: { level: 1 },
        content: [{ type: 'text', text: falso.randProductName() }],
      },
      {
        type: 'paragraph',
        content: [
          { type: 'text', text: falso.randParagraph({ length: 3 }).join(' ') },
        ],
      },
      {
        type: 'heading',
        attrs: { level: 2 },
        content: [{ type: 'text', text: 'Key Features' }],
      },
      {
        type: 'bulletList',
        content: falso.randProduct({ length: 3 }).map((product) => ({
          type: 'listItem',
          content: [
            {
              type: 'paragraph',
              content: [{ type: 'text', text: product.title }],
            },
          ],
        })),
      },
    ],
  };
}

function generateApiKey() {
  return `sk_${falso.randUuid().replace(/-/g, '')}`;
}

async function seedStripePlans() {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.warn(
      '⚠️ STRIPE_SECRET_KEY not found, skipping Stripe plan creation'
    );
    return null;
  }

  console.log('🔄 Checking for existing Stripe products...');

  try {
    // Get all active products
    const existingProducts = await stripe.products.list({ active: true });

    // If no products exist, create default ones
    if (existingProducts.data.length === 0) {
      console.log('No Stripe products found. Creating default products...');

      const defaultPlans = [
        {
          name: 'Starter',
          description: 'Perfect for individuals and small projects',
          monthlyPrice: 9,
          yearlyPrice: 90,
          tier: Tier.STARTER,
          features: ['Basic features', 'Single user', '5 posts per month'],
        },
        {
          name: 'Pro',
          description: 'Great for professionals and small teams',
          monthlyPrice: 29,
          yearlyPrice: 290,
          tier: Tier.PRO,
          isPopular: true,
          features: [
            'Unlimited posts',
            'Custom domain',
            'Analytics',
            'Priority support',
          ],
        },
        {
          name: 'Enterprise',
          description: 'For large organizations with advanced needs',
          monthlyPrice: 99,
          yearlyPrice: 990,
          tier: Tier.ENTERPRISE,
          features: [
            'All Pro features',
            'Unlimited team members',
            'SSO',
            'API access',
            'Dedicated support',
          ],
        },
      ];

      const createdPlans = await Promise.all(
        defaultPlans.map(async (plan) => {
          // Create product in Stripe
          const product = await stripe.products.create({
            name: plan.name,
            description: plan.description,
          });

          // Create monthly price
          const monthlyPrice = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(plan.monthlyPrice * 100),
            currency: 'usd',
            recurring: { interval: 'month' },
            nickname: `${plan.name} Monthly`,
          });

          // Create yearly price
          const yearlyPrice = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(plan.yearlyPrice * 100),
            currency: 'usd',
            recurring: { interval: 'year' },
            nickname: `${plan.name} Yearly`,
          });

          return {
            name: plan.name,
            description: plan.description,
            monthlyPrice: plan.monthlyPrice,
            yearlyPrice: plan.yearlyPrice,
            stripeProductId: product.id,
            stripePriceIdMonthly: monthlyPrice.id,
            stripePriceIdYearly: yearlyPrice.id,
            features: plan.features,
          };
        })
      );

      console.log('✅ Successfully created default Stripe products and prices');
      return createdPlans;
    }

    // If products exist, fetch their prices and return them
    console.log('Found existing Stripe products, fetching prices...');

    const stripePlans = await Promise.all(
      existingProducts.data.map(async (product) => {
        const prices = await stripe.prices.list({
          product: product.id,
          active: true,
        });

        const monthlyPrice = prices.data.find(
          (price) => price.recurring?.interval === 'month'
        );
        const yearlyPrice = prices.data.find(
          (price) => price.recurring?.interval === 'year'
        );

        if (!monthlyPrice || !yearlyPrice) {
          console.warn(
            `⚠️ Skipping product ${product.name} - missing monthly or yearly price`
          );
          return null;
        }

        return {
          name: product.name,
          description: product.description || '',
          monthlyPrice: monthlyPrice.unit_amount! / 100,
          yearlyPrice: yearlyPrice.unit_amount! / 100,
          stripeProductId: product.id,
          stripePriceIdMonthly: monthlyPrice.id,
          stripePriceIdYearly: yearlyPrice.id,
          features: [],
        };
      })
    );

    const validStripePlans = stripePlans.filter(
      (plan): plan is NonNullable<typeof plan> => plan !== null
    );

    if (validStripePlans.length === 0) {
      throw new Error(
        'No valid Stripe plans found with both monthly and yearly prices'
      );
    }

    console.log('✅ Successfully fetched Stripe plans:', validStripePlans);
    return validStripePlans;
  } catch (error) {
    console.error('❌ Error handling Stripe plans:', error);
    throw error;
  }
}

async function main() {
  console.log('🌱 Starting seed...');

  try {
    // Clear existing data
    console.log('🧹 Cleaning existing data...');
    await prisma.$transaction([
      prisma.notification.deleteMany(),
      prisma.like.deleteMany(),
      prisma.comment.deleteMany(),
      prisma.post.deleteMany(),
      prisma.tag.deleteMany(),
      prisma.apiKey.deleteMany(),
      prisma.invite.deleteMany(),
      prisma.subscriber.deleteMany(),
      prisma.tenantsOnUsers.deleteMany(),
      prisma.tenant.deleteMany(),
      prisma.user.deleteMany(),
      prisma.plan.deleteMany(),
    ]);
    console.log('✅ Successfully cleaned existing data');

    // Create test user
    console.log(`👤 Creating ${TEST_USER_ROLE.toLowerCase()} user...`);
    const user = await prisma.user.create({
      data: {
        name: 'Rick Ramirez',
        email: '<EMAIL>',
        username: 'ricka7x',
        role: TEST_USER_ROLE,
        bio: 'Test user',
        urls: ['https://example.com'],
      },
    });
    console.log('✅ Created user:', user.email);

    // Create default settings for test user
    console.log('⚙️ Creating settings for test user...');
    await prisma.settings.create({
      data: {
        userId: user.id,
        blogNotifications: true,
        subscriptionNotifications: false,
        marketingNotifications: false,
        marketingEmails: true,
        securityEmails: true,
      },
    });
    console.log('✅ Created settings for test user');

    // Create tenant for all user roles
    console.log('🏢 Creating tenant...');
    const tenant = await prisma.tenant.create({
      data: {
        name: 'Default Tenant',
        subdomain: 'default',
        ownerId: user.id,
      },
    });

    // Associate tenant with user based on role
    await prisma.tenantsOnUsers.create({
      data: {
        userId: user.id,
        tenantId: tenant.id,
        role: TEST_USER_ROLE,
      },
    });

    await prisma.user.update({
      where: { id: user.id },
      data: { currentTenantId: tenant.id },
    });

    console.log('✅ Created and associated tenant');

    // Create Stripe plans
    console.log('💳 Creating Stripe plans...');
    const stripePlans = await seedStripePlans();

    if (!stripePlans) {
      console.warn('⚠️ No Stripe plans were created');
    } else {
      console.log('📝 Creating database plans...');
      await Promise.all(
        stripePlans.map((plan) =>
          prisma.plan.create({
            data: {
              name: plan.name,
              description: plan.description,
              monthlyPrice: plan.monthlyPrice,
              yearlyPrice: plan.yearlyPrice,
              stripeProductId: plan.stripeProductId,
              stripePriceIdMonthly: plan.stripePriceIdMonthly,
              stripePriceIdYearly: plan.stripePriceIdYearly,
              features: plan.features,
            },
          })
        )
      );
      console.log('✅ Successfully created database plans');
    }

    // Create additional users
    console.log(`👥 Creating ${SEED_CONFIG.USERS_COUNT} additional users...`);
    const users = await Promise.all(
      falso.randUser({ length: SEED_CONFIG.USERS_COUNT }).map(async (user) => {
        const role = falso.rand([Role.USER, Role.COLLABORATOR]);
        const newUser = await prisma.user.create({
          data: {
            name: user.firstName + ' ' + user.lastName,
            username: user.username,
            email: user.email,
            role: role,
            bio: falso.randParagraph(),
            urls: falso.randUrl({ length: 2 }),
            currentTenantId: tenant.id,
          },
        });

        // Create settings for each additional user
        await prisma.settings.create({
          data: {
            userId: newUser.id,
            blogNotifications: falso.randBoolean(),
            subscriptionNotifications: falso.randBoolean(),
            marketingNotifications: falso.randBoolean(),
            marketingEmails: falso.randBoolean(),
            securityEmails: true, // Always keep security emails enabled
          },
        });

        await prisma.tenantsOnUsers.create({
          data: {
            userId: newUser.id,
            tenantId: tenant.id,
            role: role,
          },
        });

        return newUser;
      })
    );
    console.log('✅ Successfully created additional users');

    // Create notifications
    console.log('🔔 Creating notifications...');
    const notifications = await Promise.all([
      // System announcement
      prisma.notification.create({
        data: {
          type: 'ANNOUNCEMENT',
          content:
            'Welcome to the platform! Check out our getting started guide.',
          isRead: false,
          userId: user.id,
          fromId: user.id, // Changed from 'system' to user.id
          tenantId: tenant.id,
        },
      }),

      // Subscription notification
      prisma.notification.create({
        data: {
          type: 'SUBSCRIPTION_PURCHASE',
          content: 'Thank you for subscribing to our Pro plan!',
          isRead: false,
          userId: user.id,
          fromId: user.id,
          tenantId: tenant.id,
        },
      }),

      // Create some notifications for random users
      ...users.slice(0, 5).map((randomUser) =>
        prisma.notification.create({
          data: {
            type: falso.rand([
              'COMMENT',
              'LIKE_COMMENT',
              'LIKE_POST',
              'REPLY',
              'SUBSCRIPTION_PURCHASE',
              'ANNOUNCEMENT',
            ] as const),
            content: falso.randSentence(),
            isRead: falso.randBoolean(),
            userId: randomUser.id,
            fromId: falso.rand([...users, user]).id,
            tenantId: tenant.id,
          },
        })
      ),
    ]);

    console.log(
      `✅ Successfully created ${notifications.length} notifications`
    );

    // Create tags
    console.log(`🏷️ Creating ${SEED_CONFIG.TAGS_COUNT} tags...`);
    const tagSet = new Set();
    while (tagSet.size < SEED_CONFIG.TAGS_COUNT) {
      tagSet.add(falso.randWord());
    }

    const tags = await Promise.all(
      Array.from(tagSet).map(async (word) => {
        let slug = slugify(word as string);
        let counter = 1;
        while (true) {
          try {
            return await prisma.tag.create({
              data: {
                name: word as string,
                slug: slug,
              },
            });
          } catch (error: any) {
            if (error.code === 'P2002') {
              slug = `${slugify(word as string)}-${counter}`;
              counter++;
            } else {
              throw error;
            }
          }
        }
      })
    );
    console.log('✅ Successfully created tags');

    // Create posts
    console.log(`📝 Creating ${SEED_CONFIG.POSTS_COUNT} posts...`);
    const uniqueTitles = new Set();
    const posts = await Promise.all([
      // First create some posts for the owner
      ...Array.from({ length: 5 }).map(async () => {
        let title;
        do {
          title = falso.randTextRange({ min: 5, max: 10 });
        } while (uniqueTitles.has(title));
        uniqueTitles.add(title);

        return prisma.post.create({
          data: {
            title,
            slug: slugify(title),
            content: generatePostContent(),
            excerpt: falso.randSentence().slice(0, 150),
            published: true,
            cover: falso.randImg(),
            authorId: user.id,

            tags: {
              connect: falso
                .rand(tags, { length: falso.randNumber({ min: 1, max: 3 }) })
                .map((tag) => ({ id: tag.id })),
            },
          },
        });
      }),
      // Then create the rest of the posts
      ...Array.from({ length: SEED_CONFIG.POSTS_COUNT - 5 }).map(async () => {
        let title;
        do {
          title = falso.randTextRange({ min: 5, max: 10 });
        } while (uniqueTitles.has(title));
        uniqueTitles.add(title);

        try {
          return await prisma.post.create({
            data: {
              title,
              slug: slugify(title),
              content: generatePostContent(),
              excerpt: falso.randSentence().slice(0, 150),
              published: falso.randBoolean(),
              cover: falso.randImg(),
              authorId: falso.rand(users).id,

              tags: {
                connect: falso
                  .rand(tags, { length: falso.randNumber({ min: 1, max: 3 }) })
                  .map((tag) => ({ id: tag.id })),
              },
            },
          });
        } catch (error) {
          console.error('❌ Error creating post:', error);
          return null;
        }
      }),
    ]);

    const successfulPosts = posts.filter(
      (post): post is NonNullable<typeof post> => post !== null
    );
    console.log(`✅ Successfully created ${successfulPosts.length} posts`);

    // Create post-related notifications
    console.log('🔔 Creating post-related notifications...');
    await Promise.all(
      successfulPosts.slice(0, 5).map((post) =>
        prisma.notification.create({
          data: {
            type: 'ANNOUNCEMENT',
            content: `New post published: "${post.title}"`,
            isRead: falso.randBoolean(),
            userId: post.authorId,
            fromId: post.authorId,
            tenantId: tenant.id,
            postId: post.id,
          },
        })
      )
    );
    console.log('✅ Successfully created post notifications');

    // Create comments
    console.log(`💬 Creating ${SEED_CONFIG.COMMENTS_COUNT} comments...`);
    await Promise.all(
      Array.from({ length: SEED_CONFIG.COMMENTS_COUNT }).map(() => {
        const post = falso.rand(successfulPosts);
        return prisma.comment.create({
          data: {
            content: falso.randParagraph(),
            authorId: falso.rand(users).id,
            postId: post.id,
          },
        });
      })
    );
    console.log('✅ Successfully created comments');

    // Create likes
    console.log(`👍 Creating ${SEED_CONFIG.LIKES_COUNT} likes...`);
    await Promise.all(
      Array.from({ length: SEED_CONFIG.LIKES_COUNT }).map(() => {
        const post = falso.rand(successfulPosts);
        return prisma.like.create({
          data: {
            authorId: falso.rand(users).id,
            postId: post.id,
          },
        });
      })
    );
    console.log('✅ Successfully created likes');

    // Create invites
    console.log(`📨 Creating ${SEED_CONFIG.INVITES_COUNT} invites...`);
    await Promise.all(
      Array.from({ length: SEED_CONFIG.INVITES_COUNT }).map(async () => {
        const sender = falso.rand([user, ...users]);
        const expiresAt = add(new Date(), { days: 7 });

        return prisma.invite.create({
          data: {
            email: falso.randEmail(),
            status: falso.rand(['PENDING', 'ACCEPTED', 'REJECTED']),
            token: uuidv4(),
            expiresAt,
            sender: {
              connect: { id: sender.id },
            },
            tenant: {
              connect: { id: tenant.id },
            },
          },
        });
      })
    );
    console.log('✅ Successfully created invites');

    // Create subscribers
    console.log(`📧 Creating ${SEED_CONFIG.SUBSCRIBERS_COUNT} subscribers...`);
    try {
      await Promise.all(
        Array.from({ length: SEED_CONFIG.SUBSCRIBERS_COUNT }).map(async () => {
          const subscriber = await prisma.subscriber.create({
            data: {
              email: falso.randEmail(),
              name: falso.randFullName(),
              tenant: {
                connect: { id: tenant.id },
              },
            },
          });
          console.log(`✓ Created subscriber: ${subscriber.email}`);
          return subscriber;
        })
      );
      console.log('✅ Successfully created subscribers');
    } catch (error) {
      console.error('❌ Error creating subscribers:', error);
      throw error;
    }

    // Create API keys
    console.log(`🔑 Creating API keys...`);
    try {
      // First create 10 API keys for the user
      console.log('Creating 10 API keys for user...');
      await Promise.all(
        Array.from({ length: 10 }).map(async (_, index) => {
          const apiKey = await prisma.apiKey.create({
            data: {
              name: `Owner API Key ${index + 1}`,
              key: generateApiKey(),
              userId: user.id,
              tenantId: tenant.id,
            },
          });
          console.log(`✓ Created API key: ${apiKey.name} for user`);
          return apiKey;
        })
      );

      // Create remaining API keys for random users
      const remainingKeys = SEED_CONFIG.API_KEYS_COUNT - 10;
      if (remainingKeys > 0) {
        console.log(
          `Creating ${remainingKeys} additional API keys for other users...`
        );
        await Promise.all(
          Array.from({ length: remainingKeys }).map(async (_, index) => {
            const user = falso.rand(users);
            const apiKey = await prisma.apiKey.create({
              data: {
                name: `User API Key ${index + 1}`,
                key: generateApiKey(),
                userId: user.id,
                tenantId: tenant.id,
              },
            });
            console.log(
              `✓ Created API key: ${apiKey.name} for user: ${user.name}`
            );
            return apiKey;
          })
        );
      }

      console.log('✅ Successfully created all API keys');
    } catch (error) {
      console.error('❌ Error creating API keys:', error);
      throw error;
    }

    console.log('✨ Seed completed successfully');
  } catch (error) {
    console.error('❌ Seed failed:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seed script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed');
  });
