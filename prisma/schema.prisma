generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_URL_NON_POOLING")
}

model Account {
  id                String  @id @default(uuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id                     String           @id @default(uuid())
  name                   String?
  username               String?          @unique
  bio                    String?
  urls                   String[]
  role                   Role             @default(USER)
  isActive               Boolean          @default(true) @map("is_active")
  email                  String?          @unique
  emailVerified          DateTime?        @map("email_verified")
  image                  String?
  stripeCustomerId       String?          @unique @map("stripe_customer_id")
  stripeSubscriptionId   String?          @unique @map("stripe_subscription_id")
  stripePriceId          String?          @map("stripe_price_id")
  stripeCurrentPeriodEnd DateTime?        @map("stripe_current_period_end")
  subscriptionStatus     String?
  currentTenantId        String?          @map("current_tenant_id")
  bookmarks              Bookmark[]
  accounts               Account[]
  apiKeys                ApiKey[]
  comments               Comment[]
  invitesSent            Invite[]         @relation("InviteSender")
  likes                  Like[]
  actionsPerformed       Notification[]   @relation("UserActions")
  notifications          Notification[]   @relation("UserNotifications")
  posts                  Post[]
  sessions               Session[]
  settings               Settings?
  ownedTenants           Tenant[]         @relation("TenantOwner")
  tenants                TenantsOnUsers[]

  @@map("users")
}

model Settings {
  id                        String  @id @default(uuid())
  blogNotifications         Boolean @default(true)
  subscriptionNotifications Boolean @default(true)
  marketingNotifications    Boolean @default(true)
  marketingEmails           Boolean @default(true)
  securityEmails            Boolean @default(true)
  userId                    String  @unique @map("user_id")
  user                      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("settings")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model ProcessedEvent {
  id        String   @id
  createdAt DateTime @default(now())

  @@map("processed_events")
}

model Post {
  id            String         @id @default(cuid())
  title         String
  slug          String         @unique
  cover         String?
  content       Json?
  excerpt       String?
  published     Boolean        @default(false)
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @map("updated_at")
  authorId      String         @map("author_id")
  bookmarks     Bookmark[]
  comments      Comment[]
  likes         Like[]
  notifications Notification[]
  author        User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  tags          Tag[]          @relation("PostToTag")

  @@index([slug])
  @@map("posts")
}

model Comment {
  id            String         @id @default(cuid())
  content       String
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @map("updated_at")
  authorId      String         @map("author_id")
  postId        String         @map("post_id")
  parentId      String?        @map("parent_id")
  author        User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  parent        Comment?       @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies       Comment[]      @relation("CommentReplies")
  post          Post           @relation(fields: [postId], references: [id], onDelete: Cascade)
  likes         Like[]
  notifications Notification[]

  @@map("comments")
}

model Tag {
  id    String @id @default(cuid())
  name  String
  slug  String @unique
  posts Post[] @relation("PostToTag")

  @@map("tags")
}

model Like {
  id            String         @id @default(cuid())
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @map("updated_at")
  authorId      String         @map("author_id")
  postId        String?        @map("post_id")
  commentId     String?        @map("comment_id")
  author        User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  comment       Comment?       @relation(fields: [commentId], references: [id], onDelete: Cascade)
  post          Post?          @relation(fields: [postId], references: [id], onDelete: Cascade)
  notifications Notification[]

  @@unique([authorId, postId, commentId])
  @@map("likes")
}

model Notification {
  id        String           @id @default(cuid())
  type      NotificationType
  content   String
  isRead    Boolean          @default(false) @map("is_read")
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @default(now()) @map("updated_at")
  userId    String           @map("user_id")
  fromId    String           @map("from_id")
  postId    String?          @map("post_id")
  commentId String?          @map("comment_id")
  likeId    String?          @map("like_id")
  tenantId  String           @map("tenant_id")
  comment   Comment?         @relation(fields: [commentId], references: [id], onDelete: Cascade)
  from      User             @relation("UserActions", fields: [fromId], references: [id], onDelete: Cascade)
  like      Like?            @relation(fields: [likeId], references: [id], onDelete: Cascade)
  post      Post?            @relation(fields: [postId], references: [id], onDelete: Cascade)
  tenant    Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User             @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Invite {
  id        String       @id @default(cuid())
  email     String
  status    InviteStatus @default(PENDING)
  token     String       @unique
  expiresAt DateTime     @map("expires_at")
  createdAt DateTime     @default(now()) @map("created_at")
  updatedAt DateTime     @default(now()) @map("updated_at")
  senderId  String       @map("sender_id")
  tenantId  String       @map("tenant_id")
  role      Role         @default(COLLABORATOR)
  sender    User         @relation("InviteSender", fields: [senderId], references: [id], onDelete: Cascade)
  tenant    Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([email, senderId])
  @@map("invites")
}

model ApiKey {
  id        String   @id @default(cuid())
  name      String
  key       String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  userId    String   @map("user_id")
  tenantId  String   @map("tenant_id")
  isActive  Boolean  @default(true) @map("is_active")
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model Subscriber {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String   @map("tenant_id")
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("subscribers")
}

enum Tier {
  STARTER
  PRO
  ENTERPRISE
}

model Plan {
  id                   String   @id @default(cuid())
  name                 String
  tier                 Tier     @default(STARTER)
  description          String
  features             String[]
  monthlyPrice         Float
  yearlyPrice          Float
  stripePriceIdMonthly String
  stripePriceIdYearly  String
  stripeProductId      String
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  isPopular            Boolean  @default(false)

  @@map("plans")
}

model Tenant {
  id            String           @id @default(cuid())
  name          String
  description   String?
  subdomain     String           @unique
  logo          String?
  createdAt     DateTime         @default(now()) @map("created_at")
  updatedAt     DateTime         @updatedAt @map("updated_at")
  ownerId       String           @map("owner_id")
  apiKeys       ApiKey[]
  invites       Invite[]
  notifications Notification[]
  subscribers   Subscriber[]
  owner         User             @relation("TenantOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  users         TenantsOnUsers[]

  @@map("tenants")
}

model TenantsOnUsers {
  userId    String
  tenantId  String
  role      Role     @default(USER)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, tenantId])
  @@map("tenants_users")
}

model Bookmark {
  id        String   @id @default(cuid())
  userId    String
  postId    String
  createdAt DateTime @default(now())
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@index([userId])
  @@index([postId])
  @@map("bookmarks")
}

enum Role {
  USER
  COLLABORATOR
  ADMIN
  OWNER
}

enum NotificationType {
  COMMENT
  LIKE_COMMENT
  LIKE_POST
  REPLY
  SUBSCRIPTION_PURCHASE
  ANNOUNCEMENT
}

enum InviteStatus {
  PENDING
  ACCEPTED
  REJECTED
}
